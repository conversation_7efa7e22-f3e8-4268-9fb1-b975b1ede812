# configuration file for git-cliff
# see https://github.com/orhun/git-cliff#configuration-file

[changelog]
# changelog header
header = """
# Changelog\n
All notable changes to HyperSwitch will be documented here.\n
"""
# template for the changelog body
# https://tera.netlify.app/docs/#introduction
body = """
{% set newline = "\n" -%}
{% set commit_base_url = "https://github.com/juspay/hyperswitch/commit/" -%}
{% set compare_base_url = "https://github.com/juspay/hyperswitch/compare/" -%}
{% if version -%}
    ## {{ version }}
{% else -%}
    ## [unreleased]
{% endif -%}
{% for group, commits in commits | group_by(attribute="group") %}
    {# The `striptags` removes the HTML comments added while grouping -#}
    ### {{ group | striptags | trim | upper_first }}
    {% for scope, commits in commits | group_by(attribute="scope") %}
        - {{ "**" ~ scope ~ ":" ~ "**" -}}
        {% for commit in commits -%}
            {% if commits | length != 1 %}{{ newline ~ "  - " }}{% else %}{{ " " }}{% endif -%}
            {{ commit.message | upper_first | trim }} ([`{{ commit.id | truncate(length=7, end="") }}`]({{ commit_base_url ~ commit.id }}))
        {%- endfor -%}
    {%- endfor -%}
    {%- for commit in commits -%}
        {% if commit.scope %}{% else %}
        - {{ commit.message | upper_first | trim }} ([`{{ commit.id | truncate(length=7, end="") }}`]({{ commit_base_url ~ commit.id }}))
        {%- endif %}
    {%- endfor %}
{% endfor %}
{% if previous and previous.commit_id and commit_id -%}
    **Full Changelog:** [`{{ previous.version }}...{{ version }}`]({{ compare_base_url }}{{ previous.version }}...{{ version }})\n
{% endif %}
"""
# remove the leading and trailing whitespace from the template
trim = true
# changelog footer
footer = ""

[git]
# parse the commits based on https://www.conventionalcommits.org
conventional_commits = true
# filter out the commits that are not conventional
filter_unconventional = false
# process each line of a commit as an individual commit
split_commits = false
# regex for preprocessing the commit messages
commit_preprocessors = [
    { pattern = "^ +", replace = "" }, # remove spaces at the beginning of the message
    { pattern = " +", replace = " " }, # replace multiple spaces with a single space
    { pattern = "\\(#([0-9]+)\\)", replace = "([#${1}](https://github.com/juspay/hyperswitch/pull/${1}))" }, # replace PR numbers with links
    { pattern = "(\\n?Co-authored-by: .+ <.+@.+>\\n?)+", replace = "" }, # remove co-author information
    { pattern = "(\\n?Signed-off-by: .+ <.+@.+>\\n?)+", replace = "" }, # remove sign-off information
]
# regex for parsing and grouping commits
# the HTML comments (`<!-- N -->`) are a workaround to get sections in custom order, since `git-cliff` sorts sections in alphabetical order
# reference: https://github.com/orhun/git-cliff/issues/9
commit_parsers = [
    { message = "^(?i)(feat)", group = "<!-- 0 -->Features" },
    { message = "^(?i)(fix)", group = "<!-- 1 -->Bug Fixes" },
    { message = "^(?i)(perf)", group = "<!-- 2 -->Performance" },
    { body = ".*security", group = "<!-- 3 -->Security" },
    { message = "^(?i)(refactor)", group = "<!-- 4 -->Refactors" },
    { message = "^(?i)(test)", group = "<!-- 5 -->Testing" },
    { message = "^(?i)(docs)", group = "<!-- 6 -->Documentation" },
    { message = "^(?i)(chore\\(version\\)): (V|v)[\\d]+\\.[\\d]+\\.[\\d]+", skip = true },
    { message = "^(?i)(chore\\(version\\)): [0-9]{4}\\.[0-9]{2}\\.[0-9]{2}(\\.[0-9]+)?(-.+)?", skip = true },
    { message = "^(?i)(chore)", group = "<!-- 7 -->Miscellaneous Tasks" },
    { message = "^(?i)(build)", group = "<!-- 8 -->Build System / Dependencies" },
    { message = "^(?i)(ci)", skip = true },
]
# protect breaking changes from being skipped due to matching a skipping commit_parser
protect_breaking_commits = false
# filter out the commits that are not matched by commit parsers
filter_commits = false
# glob pattern for matching git tags
tag_pattern = "[0-9]{4}\\.[0-9]{2}\\.[0-9]{2}(\\.[0-9]+)?(-.+)?"
# regex for skipping tags
# skip_tags = "v0.1.0-beta.1"
# regex for ignoring tags
# ignore_tags = ""
# sort the tags topologically
topo_order = true
# sort the commits inside sections by oldest/newest order
sort_commits = "oldest"
# limit the number of commits included in the changelog.
# limit_commits = 42
