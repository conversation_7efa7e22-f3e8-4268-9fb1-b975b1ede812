name: Feature request
description: Submit a proposal for a new feature
title: "[FEATURE] "
labels: ["C-feature", "S-awaiting-triage"]
body:
  - type: markdown
    attributes:
      value: |
        We value your time and efforts in submitting this feature request form. Here are a few things to check before clicking the submit button :)
        1. Make sure you're on the latest version of the app. Features are being added all the time, and it is entirely possible what you're requesting has already been added. The current release can be found here: https://github.com/juspay/hyperswitch/releases/latest.
        2. Check the changelog file to confirm that the feature hasn't been added for an upcoming release: https://github.com/juspay/hyperswitch/blob/main/CHANGELOG.md
        3. Please try to fill this template completely to the best of your abilities. A feature request that is clear and explicit in its needs lets us get to work faster.

  - type: textarea
    id: feature-description
    attributes:
      label: Feature Description
      description: A clear and concise description of what the feature is.
      placeholder: In my use-case, ...
    validations:
      required: true

  - type: textarea
    id: implementation
    attributes:
      label: Possible Implementation
      description: A clear and concise description of what you want to happen.
      placeholder: Not obligatory, but ideas as to the implementation of the addition or change
    validations:
      required: true

  - type: checkboxes
    id: no-duplicate-issues
    attributes:
      label: Have you spent some time checking if this feature request has been raised before?
      options:
        - label: I checked and didn't find a similar issue
          required: true

  - type: checkboxes
    id: read-contributing-guidelines
    attributes:
      label: Have you read the Contributing Guidelines?
      options:
        - label: I have read the [Contributing Guidelines](https://github.com/juspay/hyperswitch/blob/main/docs/CONTRIBUTING.md)
          required: true

  - type: dropdown
    id: willing-to-submit-pr
    attributes:
      label: Are you willing to submit a PR?
      description: This is absolutely not required, but we are happy to guide you in the contribution process.
      options:
        - Yes, I am willing to submit a PR!
        - No, but I'm happy to collaborate on a PR with someone else
        - No, I don't have time to work on this right now
