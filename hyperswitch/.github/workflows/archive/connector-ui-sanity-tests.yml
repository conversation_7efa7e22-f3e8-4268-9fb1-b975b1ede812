name: Connector UI Sanity Tests

on:
  workflow_dispatch:
  pull_request:
  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Disable incremental compilation.
  #
  # Incremental compilation is useful as part of an edit-build-test-edit cycle,
  # as it lets the compiler avoid recompiling code that hasn't changed. However,
  # on CI, we're not making small edits; we're almost always building the entire
  # project from scratch. Thus, incremental compilation on CI actually
  # introduces *additional* overhead to support making future builds
  # faster...but no future builds will ever occur in any given CI environment.
  #
  # See https://matklad.github.io/2021/09/04/fast-rust-builds.html#ci-workflow
  # for details.
  CARGO_INCREMENTAL: 1
  # Allow more retries for network requests in cargo (downloading crates) and
  # rustup (installing toolchains). This should help to reduce flaky CI failures
  # from transient network timeouts or other issues.
  CARGO_NET_RETRY: 10
  RUSTUP_MAX_RETRIES: 10
  # Don't emit giant backtraces in the CI logs.
  RUST_BACKTRACE: short
  RUST_MIN_STACK: 10485760

jobs:
  test_connectors:
    name: Run connector UI tests
    runs-on: ubuntu-latest

    services:
      redis:
        image: "redis"
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      postgres:
        image: "postgres:14.5"
        env:
          POSTGRES_USER: db_user
          POSTGRES_PASSWORD: db_pass
          POSTGRES_DB: hyperswitch_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    strategy:
      fail-fast: false
      matrix:
        connector:
          # do not use more than 2 runners, try to group less time taking connectors together
          - stripe,airwallex,bluesnap,checkout,trustpay_3ds,payu,authorizedotnet,aci,noon
          - adyen_uk,shift4,worldline,multisafepay,paypal,mollie,nexinets

    steps:
      - name: Ignore Tests incase of merge group or pull request from external repository
        if: (github.event_name == 'merge_group') || (github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name)
        shell: bash
        run: |
          echo "Skipped tests as the event is merge_group or pull_request from external repository"
          exit 0

      - name: Checkout repository
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        uses: actions/checkout@v4

      - name: Download Encrypted TOML from S3 and Decrypt
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.CONNECTOR_CREDS_AWS_ACCESS_KEY_ID }}
          AWS_REGION: ${{ secrets.CONNECTOR_CREDS_AWS_REGION }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.CONNECTOR_CREDS_AWS_SECRET_ACCESS_KEY }}
          CONNECTOR_AUTH_PASSPHRASE: ${{ secrets.CONNECTOR_AUTH_PASSPHRASE }}
          CONNECTOR_CREDS_S3_BUCKET_URI: ${{ secrets.CONNECTOR_CREDS_S3_BUCKET_URI}}
          DESTINATION_FILE_NAME: "connector_auth.toml.gpg"
          S3_SOURCE_FILE_NAME: "cf05a6ab-525e-4888-98b3-3b4a443b87c0.toml.gpg"
        shell: bash
        run: |
          mkdir -p ${HOME}/target/secrets ${HOME}/target/test
          aws s3 cp "${CONNECTOR_CREDS_S3_BUCKET_URI}/${S3_SOURCE_FILE_NAME}" "${HOME}/target/secrets/${DESTINATION_FILE_NAME}"
          gpg --quiet --batch --yes --decrypt --passphrase="${CONNECTOR_AUTH_PASSPHRASE}" --output "${HOME}/target/test/connector_auth.toml" "${HOME}/target/secrets/${DESTINATION_FILE_NAME}"

      - name: Set connector auth file path in env
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        run: echo "CONNECTOR_AUTH_FILE_PATH=${HOME}/target/test/connector_auth.toml" >> $GITHUB_ENV

      - name: Set connector tests file path in env
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        run: echo "CONNECTOR_TESTS_FILE_PATH=${HOME}/target/test/connector_tests.json" >> $GITHUB_ENV

      - name: Set ignore_browser_profile usage in env
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        run: echo "IGNORE_BROWSER_PROFILE=true" >> $GITHUB_ENV

      - name: Install latest compiler
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Build and Cache Rust Dependencies
        uses: Swatinem/rust-cache@v2.7.7
        with:
          save-if: false

      - name: Install Diesel CLI with Postgres Support
        uses: baptiste0928/cargo-install@v3.3.0
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        with:
          crate: diesel_cli
          features: postgres
          args: --no-default-features

      - name: Diesel migration run
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        env:
          DATABASE_URL: postgres://db_user:db_pass@localhost:5432/hyperswitch_db
        run: diesel migration run

      - name: Build project
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        run: cargo build --package router --bin router

      - name: Start server
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        env:
          BROWSER_DATA_PASSPHRASE: ${{ secrets.CONNECTOR_AUTH_PASSPHRASE }}
          INPUT: ${{ matrix.connector }}
          UI_TESTCASES_PATH: ${{ secrets.UI_TESTCASES_PATH }}
        shell: bash
        run: .github/scripts/start_ui_test_server.sh

      - name: Run tests
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        env:
          INPUT: ${{ matrix.connector }}
        shell: bash
        run: |
          RED='\033[0;31m'
          RESET='\033[0m'
          failed_connectors=()

          for i in $(echo "$INPUT" | tr "," "\n"); do
              echo "${i}"
              if ! cargo test --package test_utils --test connectors -- "${i}_ui::" --test-threads=1; then
                  failed_connectors+=("${i}")
              fi
          done

          if [ ${#failed_connectors[@]} -gt 0 ]; then
              echo -e "${RED}One or more connectors failed to run:${RESET}"
              printf '%s\n' "${failed_connectors[@]}"
              exit 1
          fi
