name: Create a nightly tag

on:
  workflow_call:
    secrets:
      token:
        description: GitHub token for authenticating with GitHub
        required: true
    outputs:
      tag:
        description: The tag that was created by the workflow
        value: ${{ jobs.create-nightly-tag.outputs.tag }}

env:
  # Allow more retries for network requests in cargo (downloading crates) and
  # rustup (installing toolchains). This should help to reduce flaky CI failures
  # from transient network timeouts or other issues.
  CARGO_NET_RETRY: 10
  RUSTUP_MAX_RETRIES: 10

  # The branch name that this workflow is allowed to run on.
  # If the workflow is run on any other branch, this workflow will fail.
  ALLOWED_BRANCH_NAME: main

jobs:
  create-nightly-tag:
    name: Create a nightly tag
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.token }}

      - name: Check if the workflow is run on an allowed branch
        shell: bash
        run: |
          if [[ "${{ github.ref }}" != "refs/heads/${ALLOWED_BRANCH_NAME}" ]]; then
            echo "::error::This workflow is expected to be run from the '${ALLOWED_BRANCH_NAME}' branch. Current branch: '${{ github.ref }}'"
            exit 1
          fi

      - name: Check if the latest commit is a tag
        shell: bash
        run: |
          if [[ -n "$(git tag --points-at HEAD)" ]]; then
            echo "::error::The latest commit on the branch is already a tag"
            exit 1
          fi

        # Pulling latest changes in case pre-release steps push new commits
      - name: Pull allowed branch
        shell: bash
        run: git pull

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable

      - name: Install git-cliff
        uses: taiki-e/install-action@v2
        with:
          tool: git-cliff
          checksum: true

      - name: Obtain previous and next tag information
        shell: bash
        run: |
          # Calendar versioning format followed: `YYYY.0M.0D.MICRO`
          # - MICRO version number starts from 0 (to allow for multiple tags in a single day)
          # - Hotfixes or patches can be suffixed as `-hotfix1` or `-patch1` after the MICRO version number

          CURRENT_UTC_DATE="$(date --utc '+%04Y.%02m.%02d')"

          # Check if any tags exist on the current branch which contain the current UTC date
          if ! git tag --merged | grep --quiet "${CURRENT_UTC_DATE}"; then
            # Search for date-like tags (no strict checking), sort and obtain previous tag
            PREVIOUS_TAG="$(
              git tag --merged \
                | grep --extended-regexp '[0-9]{4}\.[0-9]{2}\.[0-9]{2}' \
                | sort --version-sort \
                | tail --lines 1
            )"

            # No tags with current date exist, next tag will be just tagged with current date and micro version number 0
            NEXT_MICRO_VERSION_NUMBER='0'
            NEXT_TAG="${CURRENT_UTC_DATE}.${NEXT_MICRO_VERSION_NUMBER}"

          else
            # Some tags exist with current date, find out latest micro version number
            PREVIOUS_TAG="$(
              git tag --merged \
                | grep "${CURRENT_UTC_DATE}" \
                | sort --version-sort \
                | tail --lines 1
            )"
            PREVIOUS_MICRO_VERSION_NUMBER="$(
              echo -n "${PREVIOUS_TAG}" \
                | sed --regexp-extended 's/[0-9]{4}\.[0-9]{2}\.[0-9]{2}(\.([0-9]+))?(-(.+))?/\2/g'
            )"
            #                              ^^^^^^^^  ^^^^^^^^  ^^^^^^^^    ^^^^^^     ^^^^
            #                                YEAR     MONTH      DAY       MICRO      Any suffix, say `hotfix1`
            #
            #                                                              The 2nd capture group contains the micro version number

            if [[ -z "${PREVIOUS_MICRO_VERSION_NUMBER}" ]]; then
              # Micro version number is empty, set next micro version as 1
              NEXT_MICRO_VERSION_NUMBER='1'
            else
              # Increment previous micro version by 1 and set it as next micro version
              NEXT_MICRO_VERSION_NUMBER="$((PREVIOUS_MICRO_VERSION_NUMBER + 1))"
            fi

            NEXT_TAG="${CURRENT_UTC_DATE}.${NEXT_MICRO_VERSION_NUMBER}"
          fi

          echo "PREVIOUS_TAG=${PREVIOUS_TAG}" >> $GITHUB_ENV
          echo "NEXT_TAG=${NEXT_TAG}" >> $GITHUB_ENV

      - name: Generate changelog
        shell: bash
        run: |
          # Generate changelog content and store it in `release-notes.md`
          git-cliff --config '.github/git-cliff-changelog.toml' --strip header --tag "${NEXT_TAG}" "${PREVIOUS_TAG}^.." \
            | sed "/## ${PREVIOUS_TAG}\$/,\$d" \
            | sed '$s/$/\n- - -/' > release-notes.md

          # Append release notes after the specified pattern in `CHANGELOG.md`
          sed --in-place '0,/^- - -/!b; /^- - -/{
              a
              r release-notes.md
            }' CHANGELOG.md
          rm release-notes.md

      - name: Set git configuration
        shell: bash
        run: |
          git config --local user.name 'github-actions'
          git config --local user.email '41898282+github-actions[bot]@users.noreply.github.com'

      - name: Commit, tag and push generated changelog
        shell: bash
        run: |
          git add CHANGELOG.md
          git commit --message "chore(version): ${NEXT_TAG}"

          git tag "${NEXT_TAG}" HEAD

          git push origin "${ALLOWED_BRANCH_NAME}"
          git push origin "${NEXT_TAG}"

      - name: Set job outputs
        shell: bash
        run: |
          echo "tag=${NEXT_TAG}" >> $GITHUB_OUTPUT
