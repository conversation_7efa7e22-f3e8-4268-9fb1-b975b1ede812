name: Create tag on hotfix branch

on:
  workflow_dispatch:

jobs:
  create_tag:
    runs-on: ubuntu-latest

    steps:
      - name: Generate a token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.HYPERSWITCH_BOT_APP_ID }}
          private-key: ${{ secrets.HYPERSWITCH_BOT_APP_PRIVATE_KEY }}

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ steps.generate_token.outputs.token }}

      - name: Install git-cliff
        uses: taiki-e/install-action@v2
        with:
          tool: git-cliff
          checksum: true

      - name: Check if the input is valid hotfix branch
        shell: bash
        run: |
          if [[ ${{github.ref}} =~ ^refs/heads/hotfix-[0-9]{4}\.[0-9]{2}\.[0-9]{2}\.[0-9]+$ ]]; then
            echo "::notice::${{github.ref}} is a valid hotfix branch."
          else
            echo "::error::${{github.ref}} is not a valid hotfix branch."
            exit 1
          fi

      - name: Check if the latest commit is tag
        shell: bash
        run: |
          if [[ -z "$(git tag --points-at HEAD)" ]]; then
            echo "::notice::The latest commit is not a tag "
          else
            echo "::error::The latest commit on the branch is already a tag"
            exit 1
          fi

      - name: Determine current and next tag
        shell: bash
        run: |
          function get_next_tag() {
            local previous_tag="${1}"
            local previous_hotfix_number
            local next_tag

            previous_hotfix_number="$(echo "${previous_tag}" | awk -F. '{ print $4 }' | sed -E 's/([0-9]+)(-hotfix([0-9]+))?/\3/')"

            if [[ -z "${previous_hotfix_number}" ]]; then
              # Previous tag was not a hotfix tag
              next_tag="${previous_tag}-hotfix1"
            else
              # Previous tag was a hotfix tag, increment hotfix number
              local hotfix_number=$((previous_hotfix_number + 1))
              next_tag="${previous_tag/%${previous_hotfix_number}/${hotfix_number}}"
            fi

            echo "${next_tag}"
          }

          # Search for date-like tags (no strict checking), sort and obtain previous tag
          PREVIOUS_TAG="$(
              git tag --merged \
                | grep --extended-regexp '[0-9]{4}\.[0-9]{2}\.[0-9]{2}' \
                | sort --version-sort \
                | tail --lines 1
            )"
          NEXT_TAG="$(get_next_tag "${PREVIOUS_TAG}")"

          echo "PREVIOUS_TAG=${PREVIOUS_TAG}" >> $GITHUB_ENV
          echo "NEXT_TAG=${NEXT_TAG}" >> $GITHUB_ENV

      - name: Generate changelog
        shell: bash
        run: |
          # Generate changelog content and store it in `release-notes.md`
          git-cliff --config '.github/git-cliff-changelog.toml' --strip header --tag "${NEXT_TAG}" "${PREVIOUS_TAG}^.." \
            | sed "/## ${PREVIOUS_TAG#v}\$/,\$d" \
            | sed '$s/$/\n- - -/' > release-notes.md

          # Append release notes after the specified pattern in CHANGELOG.md
          sed --in-place '0,/^- - -/!b; /^- - -/{
            a
            r release-notes.md
          }' CHANGELOG.md
          rm release-notes.md

      - name: Set Git Configuration
        shell: bash
        run: |
          git config --local user.name 'hyperswitch-bot[bot]'
          git config --local user.email '148525504+hyperswitch-bot[bot]@users.noreply.github.com'

      - name: Push created commit and tag
        shell: bash
        run: |
          # Stage, commit and tag the changelog
          git add CHANGELOG.md
          git commit --message "chore(version): ${NEXT_TAG}"
          git tag --message "$(git show --no-patch --format=%s HEAD)" "${NEXT_TAG}" HEAD
          git push
          git push --tags
