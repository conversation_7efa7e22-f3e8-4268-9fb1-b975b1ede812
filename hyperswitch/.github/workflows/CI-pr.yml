name: CI-pr

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Disable incremental compilation.
  #
  # Incremental compilation is useful as part of an edit-build-test-edit cycle,
  # as it lets the compiler avoid recompiling code that hasn't changed. However,
  # on CI, we're not making small edits; we're almost always building the entire
  # project from scratch. Thus, incremental compilation on CI actually
  # introduces *additional* overhead to support making future builds
  # faster...but no future builds will ever occur in any given CI environment.
  #
  # See https://matklad.github.io/2021/09/04/fast-rust-builds.html#ci-workflow
  # for details.
  CARGO_INCREMENTAL: 0
  # Allow more retries for network requests in cargo (downloading crates) and
  # rustup (installing toolchains). This should help to reduce flaky CI failures
  # from transient network timeouts or other issues.
  CARGO_NET_RETRY: 10
  RUSTUP_MAX_RETRIES: 10
  # Don't emit giant backtraces in the CI logs.
  RUST_BACKTRACE: short

jobs:
  formatting:
    name: Check formatting
    runs-on: ubuntu-latest
    steps:
      - name: Generate a token
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.HYPERSWITCH_BOT_APP_ID }}
          private-key: ${{ secrets.HYPERSWITCH_BOT_APP_PRIVATE_KEY }}

      - name: Checkout repository with token
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}
          token: ${{ steps.generate_token.outputs.token }}

      - name: Checkout repository for fork
        if: ${{ github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name }}
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly
          components: rustfmt

      - name: Check formatting for forked pull requests
        if: ${{ github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name }}
        shell: bash
        run: cargo +nightly fmt --all --check

      - name: Run formatter
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        shell: bash
        run: |
          cargo +nightly fmt --all
          if ! git diff --exit-code --quiet -- crates; then
            echo "::notice::Formatting check failed"
            git config --local user.name 'hyperswitch-bot[bot]'
            git config --local user.email '148525504+hyperswitch-bot[bot]@users.noreply.github.com'
            git add crates
            git commit --message 'chore: run formatter'
            git push
          fi

  check-msrv:
    name: Check compilation on MSRV toolchain
    runs-on: ${{ matrix.runner }}
    if: ${{ ! startsWith(github.event.pull_request.base.ref, 'hotfix-') }}

    env:
      # Use `sccache` for caching compilation artifacts
      # RUSTC_WRAPPER: sccache
      RUSTFLAGS: "-D warnings"

    strategy:
      fail-fast: false
      matrix:
        runner:
          - ubuntu-latest
          - hyperswitch-runners

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install mold linker
        uses: rui314/setup-mold@v1
        if: ${{ runner.os == 'Linux' }}
        with:
          make-default: true

      - name: Get rust version from Cargo.toml
        shell: bash
        run: |
          rust_version=$(yq -oy '.workspace.package.rust-version' Cargo.toml)
          echo "rust_version=${rust_version}" >> $GITHUB_ENV

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: "${{ env.rust_version }}"

      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      # - name: Install sccache
      #   uses: taiki-e/install-action@v2
      #   with:
      #     tool: sccache
      #     checksum: true

      - name: Install rust cache
        uses: Swatinem/rust-cache@v2.7.7
        with:
          save-if: false

      - name: Install cargo-hack
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-hack
          checksum: true

      - name: Install just
        uses: taiki-e/install-action@v2
        with:
          tool: just
          checksum: true

      - name: Install jq
        shell: bash
        run: .github/scripts/install-jq.sh

      - name: Cargo hack
        shell: bash
        env:
          GH_TOKEN: ${{ github.token }}
        run: just ci_hack

  # cargo-deny:
  #   name: Run cargo-deny
  #   runs-on: ubuntu-latest
  #   strategy:
  #     matrix:
  #       checks:
  #         - advisories
  #         - bans licenses sources

  #   # Prevent sudden announcement of a new advisory from failing CI
  #   continue-on-error: ${{ matrix.checks == 'advisories' }}

  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v4

  #     - name: Run cargo-deny
  #       uses: EmbarkStudios/cargo-deny-action@v1.3.2
  #       with:
  #         command: check ${{ matrix.checks }}

  test:
    name: Run tests on stable toolchain
    runs-on: ${{ matrix.runner }}

    env:
      # Use `sccache` for caching compilation artifacts
      # RUSTC_WRAPPER: sccache
      RUSTFLAGS: "-D warnings"

    strategy:
      fail-fast: false
      matrix:
        runner:
          - ubuntu-latest
          - hyperswitch-runners

    steps:
      - name: Generate a token
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.HYPERSWITCH_BOT_APP_ID }}
          private-key: ${{ secrets.HYPERSWITCH_BOT_APP_PRIVATE_KEY }}

      - name: Checkout repository for fork
        if: ${{ (github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name) }}
        uses: actions/checkout@v4

      - name: Checkout repository with token
        if: ${{ (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}
          token: ${{ steps.generate_token.outputs.token }}

      - name: Install mold linker
        uses: rui314/setup-mold@v1
        if: ${{ runner.os == 'Linux' }}
        with:
          make-default: true

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable 2 weeks ago
          components: clippy

      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      # - name: Install sccache
      #   uses: taiki-e/install-action@v2
      #   with:
      #     tool: sccache
      #     checksum: true

      - name: Install rust cache
        uses: Swatinem/rust-cache@v2.7.7
        with:
          save-if: false

      - name: Install cargo-hack
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-hack
          checksum: true

      - name: Install just
        uses: taiki-e/install-action@v2
        with:
          tool: just
          checksum: true

      - name: Install jq
        shell: bash
        run: .github/scripts/install-jq.sh

      # - name: Install cargo-nextest
      #   uses: taiki-e/install-action@v2
      #   with:
      #     tool: cargo-nextest
      #     checksum: true

      - name: Run clippy
        shell: bash
        run: just clippy

      - name: Check Cargo.lock changed
        if: ${{ (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        run: |
          if ! git diff --quiet --exit-code -- Cargo.lock ; then
            git config --local user.name 'hyperswitch-bot[bot]'
            git config --local user.email '148525504+hyperswitch-bot[bot]@users.noreply.github.com'
            git add Cargo.lock
            git commit --message 'chore: update Cargo.lock'
            git push
          fi

      - name: Run cargo check
        if: ${{ ! startsWith(github.event.pull_request.base.ref, 'hotfix-') }}
        shell: bash
        env:
          GH_TOKEN: ${{ github.token }}
        run: just ci_hack

      - name: Cargo build release
        if: ${{ startsWith(github.event.pull_request.base.ref, 'hotfix-') }}
        shell: bash
        run: cargo check --features release

  typos:
    name: Spell check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Spell check
        uses: crate-ci/typos@master

  check-v2:
    name: Check compilation for V2 features
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install mold linker
        uses: rui314/setup-mold@v1
        if: ${{ runner.os == 'Linux' }}
        with:
          make-default: true

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable 2 weeks ago
          components: clippy

      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install rust cache
        uses: Swatinem/rust-cache@v2.7.7
        with:
          save-if: false

      - name: Install just
        uses: taiki-e/install-action@v2
        with:
          tool: just
          checksum: true

      - name: Run cargo clippy with v2 features enabled
        shell: bash
        run: just clippy_v2

      - name: Run cargo check enabling only the release and v2 features
        shell: bash
        env:
          RUSTFLAGS: "-A warnings"
        run: cargo check --no-default-features --features "release,v2"
