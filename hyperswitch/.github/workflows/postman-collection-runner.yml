name: Run postman tests

on:
  pull_request:
  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  CARGO_INCREMENTAL: 1
  CARGO_NET_RETRY: 10
  CONNECTORS: stripe
  RUST_BACKTRACE: short
  RUSTUP_MAX_RETRIES: 10
  RUST_MIN_STACK: 10485760

jobs:
  runner:
    name: Run postman tests
    runs-on: ubuntu-latest

    services:
      redis:
        image: "redis:alpine"
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      postgres:
        image: "postgres:alpine"
        env:
          POSTGRES_USER: db_user
          POSTGRES_PASSWORD: db_pass
          POSTGRES_DB: hyperswitch_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Repository checkout
        uses: actions/checkout@v4

      - name: Download Encrypted TOML from S3 and Decrypt
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.CONNECTOR_CREDS_AWS_ACCESS_KEY_ID }}
          AWS_REGION: ${{ secrets.CONNECTOR_CREDS_AWS_REGION }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.CONNECTOR_CREDS_AWS_SECRET_ACCESS_KEY }}
          CONNECTOR_AUTH_PASSPHRASE: ${{ secrets.CONNECTOR_AUTH_PASSPHRASE }}
          CONNECTOR_CREDS_S3_BUCKET_URI: ${{ secrets.CONNECTOR_CREDS_S3_BUCKET_URI}}
          DESTINATION_FILE_NAME: "connector_auth.toml.gpg"
          S3_SOURCE_FILE_NAME: "22df2f8e-743c-4294-85fd-6f249adb50d5.toml.gpg"
        shell: bash
        run: |
          mkdir -p ${HOME}/target/secrets ${HOME}/target/test

          aws s3 cp "${CONNECTOR_CREDS_S3_BUCKET_URI}/${S3_SOURCE_FILE_NAME}" "${HOME}/target/secrets/${DESTINATION_FILE_NAME}"
          gpg --quiet --batch --yes --decrypt --passphrase="${CONNECTOR_AUTH_PASSPHRASE}" --output "${HOME}/target/test/connector_auth.toml" "${HOME}/target/secrets/${DESTINATION_FILE_NAME}"

      - name: Set paths in env
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        id: config_path
        shell: bash
        run: |
          echo "CONNECTOR_AUTH_FILE_PATH=${HOME}/target/test/connector_auth.toml" >> $GITHUB_ENV

      - name: Fetch keys
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        env:
          TOML_PATH: "./config/development.toml"
        run: |
          LOCAL_ADMIN_API_KEY=$(yq '.secrets.admin_api_key' ${TOML_PATH})
          echo "ADMIN_API_KEY=${LOCAL_ADMIN_API_KEY}" >> $GITHUB_ENV

      - name: Install Rust
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable 2 weeks ago

      - name: Build and Cache Rust Dependencies
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        uses: Swatinem/rust-cache@v2.7.7
        with:
          save-if: false

      - name: Install Protoc
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install Diesel CLI with Postgres Support
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        uses: baptiste0928/cargo-install@v3.3.0
        with:
          crate: diesel_cli
          features: postgres
          args: --no-default-features

      - uses: taiki-e/install-action@v2
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        with:
          tool: just
          checksum: true

      - name: Diesel migration run
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        shell: bash
        env:
          DATABASE_URL: postgres://db_user:db_pass@localhost:5432/hyperswitch_db
        run: just migrate run --locked-schema

      - name: Install newman from fork
        run: npm ci

      - name: Build project
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        run: cargo build --package router --bin router

      - name: Setup Local Server
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        run: |
          # Start the server in the background
          target/debug/router &
          SERVER_PID=$!

          # Wait for the server to start in port 8080
          COUNT=0
          while ! nc -z localhost 8080; do
            if [ $COUNT -gt 12 ]; then # Wait for up to 2 minutes (12 * 10 seconds)
              echo "Server did not start within a reasonable time. Exiting."
              kill ${SERVER_PID}
              exit 1
            else
              COUNT=$((COUNT+1))
              sleep 10
            fi
          done

      - name: Run Tests
        if: ${{ ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name)) || (github.event_name == 'merge_group')}}
        env:
          BASE_URL: "http://localhost:8080"
          GATEWAY_MERCHANT_ID: ${{ secrets.STRIPE_GATEWAY_MERCHANT_ID }}
          GPAY_CERTIFICATE: ${{ secrets.STRIPE_GPAY_CERTIFICATE }}
          GPAY_CERTIFICATE_KEYS: ${{ secrets.STRIPE_GPAY_CERTIFICATE_KEYS }}
        shell: bash
        run: |
          RED='\033[0;31m'
          RESET='\033[0m'
          NEWMAN_PATH=$(pwd)/node_modules/.bin
          export PATH=${NEWMAN_PATH}:${PATH}
          failed_connectors=()

          for i in $(echo "${CONNECTORS}" | tr "," "\n"); do
            echo "${i}"
            if ! cargo run --bin test_utils -- --connector-name="${i}" --base-url="${BASE_URL}" --admin-api-key="${ADMIN_API_KEY}"; then
              failed_connectors+=("${i}")
            fi
          done

          if [ ${#failed_connectors[@]} -gt 0 ]; then
            echo -e "${RED}One or more connectors failed to run:${RESET}"
            printf '%s\n' "${failed_connectors[@]}"
            exit 1
          fi
