name: Validate Generated OpenAPI Spec File

on:
  pull_request:

  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  validate_json:
    name: Validate generated OpenAPI spec file
    runs-on: ubuntu-latest
    steps:
      - name: Generate a token
        if: ${{ github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name }}
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.HYPERSWITCH_BOT_APP_ID }}
          private-key: ${{ secrets.HYPERSWITCH_BOT_APP_PRIVATE_KEY }}

      - name: Checkout PR from fork
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name) }}
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}

      - name: Checkout PR with token
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          token: ${{ steps.generate_token.outputs.token }}

      - name: Checkout merge group HEAD commit
        if: ${{ github.event_name == 'merge_group' }}
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.merge_group.head_sha }}

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable 2 weeks ago

      - name: Generate the OpenAPI spec file for V1
        shell: bash
        run: cargo run -p openapi --features v1

      - name: Generate the OpenAPI spec file for V2
        shell: bash
        run: cargo run -p openapi --features v2

      - name: Install `swagger-cli`
        shell: bash
        run: npm install -g @apidevtools/swagger-cli

      - name: Validate the JSON file
        shell: bash
        run: swagger-cli validate ./api-reference/v1/openapi_spec_v1.json

      - name: Validate the JSON file for V2
        shell: bash
        run: swagger-cli validate ./api-reference/v2/openapi_spec_v2.json

      - name: Commit the JSON file if it is not up-to-date
        # PR originated from same repository
        if: ${{ (github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name == github.event.pull_request.base.repo.full_name) }}
        shell: bash
        run: |
          if ! git diff --quiet --exit-code -- api-reference/v1/openapi_spec_v1.json api-reference/v2/openapi_spec_v2.json ; then
            git config --local user.name 'hyperswitch-bot[bot]'
            git config --local user.email '148525504+hyperswitch-bot[bot]@users.noreply.github.com'
            git add api-reference/v1/openapi_spec_v1.json api-reference/v2/openapi_spec_v2.json
            git commit --message 'docs(openapi): re-generate OpenAPI specification'
            git push
          fi

      - name: Fail check if the JSON file is not up-to-date
        if: ${{ (github.event_name == 'merge_group') || ((github.event_name == 'pull_request') && (github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name)) }}
        shell: bash
        run: |
          if ! git diff --quiet --exit-code -- api-reference/v1/openapi_spec_v1.json ; then
            echo '::error::The OpenAPI spec file is not up-to-date. Please re-generate the OpenAPI spec file using `cargo run -p openapi --features v1 && cargo run -p openapi --features v2` and commit it.'
            exit 1
          fi
