name: Migration Consistency Tests

on:
  pull_request:

  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Allow more retries for network requests in cargo (downloading crates) and
  # rustup (installing toolchains). This should help to reduce flaky CI failures
  # from transient network timeouts or other issues.
  CARGO_NET_RETRY: 10
  RUSTUP_MAX_RETRIES: 10

jobs:
  migration_verify:
    name: Verify consistency of migrations with `schema.rs` file
    runs-on: ubuntu-latest

    env:
      DATABASE_URL: postgres://postgres:postgres@localhost:5432/hyperswitch_db
      PGUSER: postgres
      PGPASSWORD: postgres
      PGHOST: localhost
      PGPORT: 5432

    services:
      postgres:
        image: "postgres:alpine"
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: stable 2 weeks ago

      - uses: baptiste0928/cargo-install@v3.3.0
        with:
          crate: diesel_cli
          features: postgres
          args: --no-default-features

      - uses: taiki-e/install-action@v2
        with:
          tool: just
          checksum: true

      - name: Create database
        shell: bash
        run: just resurrect hyperswitch_db

      - name: Verify `diesel migration run for v1`
        shell: bash
        run: just migrate run --locked-schema

      - name: Verify `diesel migration redo for v1`
        shell: bash
        run: just migrate redo --locked-schema --all

      - name: Drop `DB for v2 column ordering mismatch`
        shell: bash
        run: just resurrect hyperswitch_db

      - name: Verify `diesel migration run for v2`
        shell: bash
        run: just migrate_v2 run --locked-schema

      - name: Verify `diesel migration redo for v2`
        shell: bash
        run: just migrate_v2 redo --locked-schema --all
