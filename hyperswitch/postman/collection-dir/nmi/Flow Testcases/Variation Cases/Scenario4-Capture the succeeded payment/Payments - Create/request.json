{"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw_json_formatted": {"amount": "{{random_number}}", "currency": "USD", "confirm": true, "capture_method": "automatic", "capture_on": "2022-09-10T10:11:12Z", "amount_to_capture": "{{random_number}}", "customer_id": "StripeCustomer", "email": "<EMAIL>", "name": "<PERSON>", "phone": "999999999", "phone_country_code": "+65", "description": "Its my first payment request", "authentication_type": "no_three_ds", "return_url": "https://duck.com", "payment_method": "card", "payment_method_data": {"card": {"card_number": "****************", "card_exp_month": "10", "card_exp_year": "25", "card_holder_name": "j<PERSON><PERSON>", "card_cvc": "123"}}, "billing": {"address": {"line1": "1467", "line2": "Harrison Street", "line3": "Harrison Street", "city": "San Fransico", "state": "California", "zip": "94122", "country": "US", "first_name": "<PERSON>"}}, "shipping": {"address": {"line1": "1467", "line2": "Harrison Street", "line3": "Harrison Street", "city": "San Fransico", "state": "California", "zip": "94122", "country": "US", "first_name": "<PERSON>"}}, "statement_descriptor_name": "jose<PERSON>", "statement_descriptor_suffix": "JS", "metadata": {"udf1": "value1", "new_customer": "true", "login_date": "2019-09-10T10:11:12Z"}}}, "url": {"raw": "{{baseUrl}}/payments", "host": ["{{baseUrl}}"], "path": ["payments"]}, "description": "To process a payment you will have to create a payment, attach a payment method and confirm. Depending on the user journey you wish to achieve, you may opt to all the steps in a single request or in a sequence of API request using following APIs: (i) Payments - Update, (ii) Payments - Confirm, and (iii) Payments - Capture"}