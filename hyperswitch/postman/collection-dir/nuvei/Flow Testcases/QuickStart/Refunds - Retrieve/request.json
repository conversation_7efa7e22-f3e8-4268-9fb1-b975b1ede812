{"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/refunds/:id?force_sync=true", "host": ["{{baseUrl}}"], "path": ["refunds", ":id"], "query": [{"key": "force_sync", "value": "true"}], "variable": [{"key": "id", "value": "{{refund_id}}", "description": "(Required) unique refund id"}]}, "description": "To retrieve the properties of a Refund. This may be used to get the status of a previously initiated payment or next action for an ongoing payment"}