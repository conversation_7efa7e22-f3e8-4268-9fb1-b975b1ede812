{"auth": {"type": "apikey", "apikey": [{"key": "value", "value": "{{admin_api_key}}", "type": "string"}, {"key": "key", "value": "api-key", "type": "string"}, {"key": "in", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw_json_formatted": {"merchant_id": "merchant_{{$timestamp}}", "locker_id": "m0010", "merchant_name": "NewAge Retailer", "primary_business_details": [{"country": "US", "business": "default"}], "merchant_details": {"primary_contact_person": "<PERSON>", "primary_email": "<EMAIL>", "primary_phone": "sunt laborum", "secondary_contact_person": "<PERSON>2", "secondary_email": "<EMAIL>", "secondary_phone": "cillum do dolor id", "website": "www.example.com", "about_business": "Online Retail with a wide selection of organic products for North America", "address": {"line1": "1467", "line2": "Harrison Street", "line3": "Harrison Street", "city": "San Fransico", "state": "California", "zip": "94122", "country": "US"}}, "return_url": "https://duck.com", "webhook_details": {"webhook_version": "1.0.1", "webhook_username": "ekart_retail", "webhook_password": "password_ekart@123", "payment_created_enabled": true, "payment_succeeded_enabled": true, "payment_failed_enabled": true}, "sub_merchants_enabled": false, "metadata": {"city": "NY", "unit": "245"}}}, "url": {"raw": "{{baseUrl}}/accounts", "host": ["{{baseUrl}}"], "path": ["accounts"]}, "description": "Create a new account for a merchant. The merchant could be a seller or retailer or client who likes to receive and send payments."}