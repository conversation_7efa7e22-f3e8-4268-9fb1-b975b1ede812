{"auth": {"type": "apikey", "apikey": [{"key": "value", "value": "{{publishable_key}}", "type": "string"}, {"key": "key", "value": "api-key", "type": "string"}, {"key": "in", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw_json_formatted": {"client_secret": "{{client_secret}}"}}, "url": {"raw": "{{baseUrl}}/payments/:id/confirm", "host": ["{{baseUrl}}"], "path": ["payments", ":id", "confirm"], "variable": [{"key": "id", "value": "{{payment_id}}"}]}, "description": "To process a payment you will have to create a payment, attach a payment method and confirm. Depending on the user journey you wish to achieve, you may opt to all the steps in a single request or in a sequence of API request using following APIs: (i) Payments - Update, (ii) Payments - Confirm, and (iii) Payments - Capture"}