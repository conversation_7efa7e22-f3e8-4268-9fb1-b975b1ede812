{"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw_json_formatted": {"cancellation_reason": "requested_by_customer"}}, "url": {"raw": "{{baseUrl}}/payments/:id/cancel", "host": ["{{baseUrl}}"], "path": ["payments", ":id", "cancel"], "variable": [{"key": "id", "value": "{{payment_id}}", "description": "(Required) unique payment id"}]}, "description": "A Payment could can be cancelled when it is in one of these statuses: requires_payment_method, requires_capture, requires_confirmation, requires_customer_action"}