{"variable": [{"key": "baseUrl", "value": "", "type": "string"}, {"key": "admin_api_key", "value": "", "type": "string"}, {"key": "api_key", "value": "", "type": "string"}, {"key": "merchant_id", "value": ""}, {"key": "payment_id", "value": ""}, {"key": "customer_id", "value": ""}, {"key": "mandate_id", "value": ""}, {"key": "payment_method_id", "value": ""}, {"key": "refund_id", "value": ""}, {"key": "merchant_connector_id", "value": ""}, {"key": "client_secret", "value": "", "type": "string"}, {"key": "connector_api_key", "value": "", "type": "string"}, {"key": "publishable_key", "value": "", "type": "string"}, {"key": "api_key_id", "value": "", "type": "string"}, {"key": "payment_token", "value": ""}, {"key": "connector_key1", "value": "", "type": "string"}]}