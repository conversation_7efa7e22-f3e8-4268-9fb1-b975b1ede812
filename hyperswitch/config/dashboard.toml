[default.theme]
primary_color="#006DF9"
primary_hover_color="#005ED6"
sidebar_primary="#FCFCFD"     
sidebar_secondary= "#FFFFFF"  
sidebar_hover_color= "#D9DDE5"  
sidebar_primary_text_color="#1C6DEA" 
sidebar_secondary_text_color= "#525866" 
sidebar_border_color= "#ECEFF3" 

[default.endpoints]
api_url="http://localhost:8080" # The backend hyperswitch API server for making payments
sdk_url="http://localhost:9050/HyperLoader.js" # SDK distribution url used for loading the SDK in control center
hypersense_url=""
logo_url=""
favicon_url=""
agreement_url=""
agreement_version=""
apple_pay_certificate_url=""
mixpanel_token=""
recon_iframe_url=""
dss_certificate_url=""
[default.features]
test_live_toggle=false
is_live_mode=false
email=false
quick_start=false
audit_trail=false
sample_data=false
frm=true
payout=true
recon=true
test_processors=true
feedback=false
mixpanel=false
generate_report=false
surcharge=true
dispute_evidence_upload=false
paypal_automatic_flow=false
threeds_authenticator=true
global_search=false
global_search_filters=false
dispute_analytics=false
authentication_analytics=false
configure_pmts=true
branding=false
granularity=false
compliance_certificate=false
pm_authentication_processor=true
performance_monitor=false
new_analytics=false
new_analytics_smart_retries=false
new_analytics_refunds=false
new_analytics_filters=false
down_time=false
dev_theme_feature=false
tax_processor=true
force_cookies=false
x_feature_route=false
tenant_user=false
dev_click_to_pay=true
dev_recon_v2_product=false
dev_recovery_v2_product=false
dev_vault_v2_product=false
dev_intelligent_routing_v2=false 
dev_modularity_v2=false
dev_alt_payment_methods=false
dev_hypersense_v2_product=false
maintenance_alert=""

[default.merchant_config]
[default.merchant_config.new_analytics]
org_ids=[]
merchant_ids=[]
profile_ids=[]