# For explanantion of each config, please refer to the `config/config.example.toml` file

[analytics.clickhouse]
username = "clickhouse_username"     # Clickhouse username
password = "clickhouse_password"     # Clickhouse password (optional)
host = "http://localhost:8123"       # Clickhouse host in http(s)://<URL>:<PORT> format
database_name = "clickhouse_db_name" # Clickhouse database name

# Analytics configuration.
[analytics]
source = "sqlx" # The Analytics source/strategy to be used
forex_enabled = false # Boolean to enable or disable forex conversion

[analytics.sqlx]
username = "db_user"      # Analytics DB Username
password = "db_pass"      # Analytics DB Password
host = "localhost"        # Analytics DB Host
port = 5432               # Analytics DB Port
dbname = "hyperswitch_db" # Name of Database
pool_size = 5             # Number of connections to keep open
connection_timeout = 10   # Timeout for database connection in seconds
queue_strategy = "Fifo"   # Add the queue strategy used by the database bb8 client

[api_keys]
hash_key = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef" # API key hashing key.

[applepay_decrypt_keys]
apple_pay_ppc = "APPLE_PAY_PAYMENT_PROCESSING_CERTIFICATE"         # Payment Processing Certificate provided by Apple Pay (https://developer.apple.com/) Certificates, Identifiers & Profiles > Apple Pay Payment Processing Certificate
apple_pay_ppc_key = "APPLE_PAY_PAYMENT_PROCESSING_CERTIFICATE_KEY" # Private key generated by Elliptic-curve prime256v1 curve. You can use `openssl ecparam -out private.key -name prime256v1 -genkey` to generate the private key
apple_pay_merchant_cert = "APPLE_PAY_MERCHNAT_CERTIFICATE"         # Merchant Certificate provided by Apple Pay (https://developer.apple.com/) Certificates, Identifiers & Profiles > Apple Pay Merchant Identity Certificate
apple_pay_merchant_cert_key = "APPLE_PAY_MERCHNAT_CERTIFICATE_KEY" # Private key generated by RSA:2048 algorithm. Refer Hyperswitch Docs (https://docs.hyperswitch.io/hyperswitch-cloud/payment-methods-setup/wallets/apple-pay/ios-application/) to generate the private key

[paze_decrypt_keys]
paze_private_key = "PAZE_PRIVATE_KEY"                       # Base 64 Encoded Private Key File cakey.pem generated for Paze -> Command to create private key: openssl req -newkey rsa:2048 -x509 -keyout cakey.pem -out cacert.pem -days 365
paze_private_key_passphrase = "PAZE_PRIVATE_KEY_PASSPHRASE" # PEM Passphrase used for generating Private Key File cakey.pem

[google_pay_decrypt_keys]
google_pay_root_signing_keys = "GOOGLE_PAY_ROOT_SIGNING_KEYS" # Base 64 Encoded Root Signing Keys provided by Google Pay (https://developers.google.com/pay/api/web/guides/resources/payment-data-cryptography)

[applepay_merchant_configs]
common_merchant_identifier = "APPLE_PAY_COMMON_MERCHANT_IDENTIFIER"                        # Refer to config.example.toml to learn how you can generate this value
merchant_cert = "APPLE_PAY_MERCHANT_CERTIFICATE"                                           # Merchant Certificate provided by Apple Pay (https://developer.apple.com/) Certificates, Identifiers & Profiles > Apple Pay Merchant Identity Certificate
merchant_cert_key = "APPLE_PAY_MERCHANT_CERTIFICATE_KEY"                                   # Private key generate by RSA:2048 algorithm. Refer Hyperswitch Docs (https://docs.hyperswitch.io/hyperswitch-cloud/payment-methods-setup/wallets/apple-pay/ios-application/) to generate the private key
applepay_endpoint = "https://apple-pay-gateway.apple.com/paymentservices/registerMerchant" # Apple pay gateway merchant endpoint

[connector_onboarding.paypal]
enabled = true                         # boolean
client_id = "paypal_client_id"
client_secret = "paypal_client_secret"
partner_id = "paypal_partner_id"

[connector_request_reference_id_config]
merchant_ids_send_payment_id_as_connector_request_id = [
  "merchant_id_1",
  "merchant_id_2",
  "etc.,",
]

[cors]
max_age = 30                            # Maximum time (in seconds) for which this CORS request may be cached.
origins = "http://localhost:8080"       # List of origins that are allowed to make requests.
allowed_methods = "GET,POST,PUT,DELETE" # List of methods that are allowed
wildcard_origin = false                 # If true, allows any origin to make requests

# EmailClient configuration. Only applicable when the `email` feature flag is enabled.
[email]
sender_email = "<EMAIL>" # Sender email
aws_region = ""                      # AWS region used by AWS SES
allowed_unverified_days = 1          # Number of days the api calls ( with jwt token ) can be made without verifying the email
active_email_client = "SES"          # The currently active email client
recon_recipient_email = "<EMAIL>"          # Recipient email for recon request email
prod_intent_recipient_email = "<EMAIL>" # Recipient email for prod intent email

# Configuration for aws ses, applicable when the active email client is SES
[email.aws_ses]
email_role_arn = ""        # The amazon resource name ( arn ) of the role which has permission to send emails
sts_role_session_name = "" # An identifier for the assumed role session, used to uniquely identify a session.

[events]
source = "logs" # The event sink to push events supports kafka or logs (stdout)

[events.kafka]
brokers = []                             # Kafka broker urls for bootstrapping the client
intent_analytics_topic = "topic"         # Kafka topic to be used for PaymentIntent events
attempt_analytics_topic = "topic"        # Kafka topic to be used for PaymentAttempt events
refund_analytics_topic = "topic"         # Kafka topic to be used for Refund events
api_logs_topic = "topic"                 # Kafka topic to be used for incoming api events
connector_logs_topic = "topic"           # Kafka topic to be used for connector api events
outgoing_webhook_logs_topic = "topic"    # Kafka topic to be used for outgoing webhook events
dispute_analytics_topic = "topic"        # Kafka topic to be used for Dispute events
audit_events_topic = "topic"             # Kafka topic to be used for Payment Audit events
payout_analytics_topic = "topic"         # Kafka topic to be used for Payouts and PayoutAttempt events
consolidated_events_topic = "topic"      # Kafka topic to be used for Consolidated events
authentication_analytics_topic = "topic" # Kafka topic to be used for Authentication events
fraud_check_analytics_topic = "topic"    # Kafka topic to be used for Fraud Check events
routing_logs_topic = "topic"             # Kafka topic to be used for Routing events

# File storage configuration
[file_storage]
file_storage_backend = "aws_s3" # File storage backend to be used

[file_storage.aws_s3]
region = "bucket_region" # The AWS region used by AWS S3 for file storage
bucket_name = "bucket"   # The AWS S3 bucket name for file storage

# This section provides configs for currency conversion api
[forex_api]
api_key = ""                      # Api key for making request to foreign exchange Api
fallback_api_key = ""             # Api key for the fallback service
data_expiration_delay_in_seconds = 21600     # Expiration time for data in cache as well as redis in seconds
redis_lock_timeout_in_seconds = 100        # Redis remains write locked for 100 s once the acquire_redis_lock is called
redis_ttl_in_seconds = 172800     # Time to expire for forex data stored in Redis

[jwekey] # 3 priv/pub key pair
vault_encryption_key = ""       # public key in pem format, corresponding private key in rust locker
rust_locker_encryption_key = "" # public key in pem format, corresponding private key in rust locker
vault_private_key = ""          # private key in pem format, corresponding public key in rust locker

# Locker settings contain details for accessing a card locker, a
# PCI Compliant storage entity which stores payment method information
# like card details
[locker]
host = ""                                                             # Locker host
host_rs = ""                                                          # Rust Locker host
mock_locker = true                                                    # Emulate a locker locally using Postgres
locker_signing_key_id = "1"                                           # Key_id to sign basilisk hs locker
locker_enabled = true                                                 # Boolean to enable or disable saving cards in locker
redis_temp_locker_encryption_key = "redis_temp_locker_encryption_key" # Encryption key for redis temp locker
ttl_for_storage_in_secs = 220752000                                   # Time to live for storage entries in locker


[log.console]
enabled = true
level = "DEBUG"
log_format = "json"

[log.file]
enabled = false
level = "DEBUG"
log_format = "json"

# Telemetry configuration for metrics and traces
[log.telemetry]
traces_enabled = false                                # boolean [true or false], whether traces are enabled
metrics_enabled = false                               # boolean [true or false], whether metrics are enabled
ignore_errors = false                                 # boolean [true or false], whether to ignore errors during traces or metrics pipeline setup
sampling_rate = 0.1                                   # decimal rate between 0.0 - 1.0
otel_exporter_otlp_endpoint = "http://localhost:4317" # endpoint to send metrics and traces to, can include port number
otel_exporter_otlp_timeout = 5000                     # timeout (in milliseconds) for sending metrics and traces
use_xray_generator = false                            # Set this to true for AWS X-ray compatible traces
route_to_trace = ["*/confirm"]
bg_metrics_collection_interval_in_secs = 15           # Interval for collecting the metrics in background thread

[lock_settings]
delay_between_retries_in_milliseconds = 500 # Delay between retries in milliseconds
redis_lock_expiry_seconds = 180             # Seconds before the redis lock expires

# Controls whether merchant ID authentication is enabled.
# When enabled, payment endpoints will accept and require a x-merchant-id header in the request.
[merchant_id_auth]
merchant_id_auth_enabled = false

# Main SQL data store credentials
[master_database]
username = "db_user"      # DB Username
password = "db_pass"      # DB Password. Use base-64 encoded kms encrypted value here when kms is enabled
host = "localhost"        # DB Host
port = 5432               # DB Port
dbname = "hyperswitch_db" # Name of Database
pool_size = 5             # Number of connections to keep open
connection_timeout = 10   # Timeout for database connection in seconds
queue_strategy = "Fifo"   # Add the queue strategy used by the database bb8 client

[generic_link]
[generic_link.payment_method_collect]
sdk_url = "http://localhost:9090/0.16.7/v0/HyperLoader.js"
expiry = 900
[generic_link.payment_method_collect.ui_config]
theme = "#4285F4"
logo = "https://app.hyperswitch.io/HyperswitchFavicon.png"
merchant_name = "HyperSwitch"
[generic_link.payment_method_collect.enabled_payment_methods]
card = "credit,debit"
bank_transfer = "ach,bacs,sepa"
wallet = "paypal,pix,venmo"

[generic_link.payout_link]
sdk_url = "http://localhost:9090/0.16.7/v0/HyperLoader.js"
expiry = 900
[generic_link.payout_link.ui_config]
theme = "#4285F4"
logo = "https://app.hyperswitch.io/HyperswitchFavicon.png"
merchant_name = "HyperSwitch"
[generic_link.payout_link.enabled_payment_methods]
card = "credit,debit"

[payment_link]
sdk_url = "http://localhost:9090/0.16.7/v0/HyperLoader.js"

[payment_method_auth]
pm_auth_key = "pm_auth_key" # Payment method auth key used for authorization
redis_expiry = 900          # Redis expiry time in milliseconds

[proxy]
http_url = "http://proxy_http_url"              # Proxy all HTTP traffic via this proxy
https_url = "https://proxy_https_url"           # Proxy all HTTPS traffic via this proxy
bypass_proxy_hosts = "localhost, cluster.local" # A comma-separated list of domains or IP addresses that should not use the proxy. Whitespace between entries would be ignored.

# Redis credentials
[redis]
host = "127.0.0.1"
port = 6379
pool_size = 5 # Number of connections to keep open
reconnect_max_attempts = 5 # Maximum number of reconnection attempts to make before failing. Set to 0 to retry forever.
reconnect_delay = 5 # Delay between reconnection attempts, in milliseconds
default_ttl = 300 # Default TTL for entries, in seconds
default_hash_ttl = 900 # Default TTL for hashes entries, in seconds
use_legacy_version = false # RESP protocol for fred crate (set this to true if using RESPv2 or redis version < 6)
stream_read_count = 1 # Default number of entries to read from stream if not provided in stream read options
auto_pipeline = true # Whether or not the client should automatically pipeline commands across tasks when possible.
disable_auto_backpressure = false # Whether or not to disable the automatic backpressure features when pipelining is enabled.
max_in_flight_commands = 5000 # The maximum number of in-flight commands (per connection) before backpressure will be applied.
default_command_timeout = 30 # An optional timeout to apply to all commands. In seconds
unresponsive_timeout = 10 # An optional timeout for Unresponsive commands in seconds. This should be less than default_command_timeout.
max_feed_count = 200 # The maximum number of frames that will be fed to a socket before flushing.
cluster_enabled = true # boolean
cluster_urls = [
  "redis.cluster.uri-1:8080",
  "redis.cluster.uri-2:4115",
] # List of redis cluster urls

# Replica SQL data store credentials
[replica_database]
username = "replica_user" # DB Username
password = "db_pass"      # DB Password. Use base-64 encoded kms encrypted value here when kms is enabled
host = "localhost"        # DB Host
port = 5432               # DB Port
dbname = "hyperswitch_db" # Name of Database
pool_size = 5             # Number of connections to keep open
connection_timeout = 10   # Timeout for database connection in seconds
queue_strategy = "Fifo"   # Add the queue strategy used by the database bb8 client

[report_download_config]
dispute_function = "report_download_config_dispute_function" # Config to download dispute report
payment_function = "report_download_config_payment_function" # Config to download payment report
refund_function = "report_download_config_refund_function"   # Config to download refund report
region = "report_download_config_region"                     # Region of the bucket

[opensearch]
host = "https://localhost:9200"
enabled = false

[opensearch.auth]
auth = "basic"
username = "admin"
password = "admin"
region = "eu-central-1"

[opensearch.indexes]
payment_attempts = "hyperswitch-payment-attempt-events"
payment_intents = "hyperswitch-payment-intent-events"
refunds = "hyperswitch-refund-events"
disputes = "hyperswitch-dispute-events"
sessionizer_payment_attempts = "sessionizer-payment-attempt-events"
sessionizer_payment_intents = "sessionizer-payment-intent-events"
sessionizer_refunds = "sessionizer-refund-events"
sessionizer_disputes = "sessionizer-dispute-events"

# Configuration for the Key Manager Service
[key_manager]
url = "http://localhost:5000" # URL of the encryption service

# This section provides some secret values.
[secrets]
master_enc_key = "sample_key"            # Master Encryption key used to encrypt merchant wise encryption key. Should be 32-byte long.
admin_api_key = "test_admin"             # admin API key for admin authentication.
jwt_secret = "secret"                    # JWT secret used for user authentication.

# Server configuration
[server]
workers = 8
port = 8080
host = "127.0.0.1"
# This is the grace time (in seconds) given to the actix-server to stop the execution
# For more details: https://actix.rs/docs/server/#graceful-shutdown
shutdown_timeout = 30
# HTTP Request body limit. Defaults to 32kB
request_body_limit = 32_768

# HTTPS Server Configuration
# Self-signed Private Key and Certificate can be generated with mkcert for local development
[server.tls]
port = 8081
host = "127.0.0.1"
private_key = "/path/to/private_key.pem"
certificate = "/path/to/certificate.pem"

[secrets_management]
secrets_manager = "aws_kms" # Secrets manager client to be used

[secrets_management.aws_kms]
key_id = "kms_key_id" # The AWS key ID used by the KMS SDK for decrypting data.
region = "kms_region" # The AWS region used by the KMS SDK for decrypting data.

[encryption_management]
encryption_manager = "aws_kms" # Encryption manager client to be used

[encryption_management.aws_kms]
key_id = "kms_key_id" # The AWS key ID used by the KMS SDK for decrypting data.
region = "kms_region" # The AWS region used by the KMS SDK for decrypting data.

[crm]
crm_manager = "hubspot_proxy" # Crm manager client to be used

[crm.hubspot_proxy]
form_id=""     # Form ID for Hubspot integration
request_url="" # Request URL for Hubspot API


[multitenancy]
enabled = false
global_tenant = { tenant_id = "global", schema = "public", redis_key_prefix = "", clickhouse_database = "default"}

[multitenancy.tenants.public]
base_url = "http://localhost:8080"
schema = "public"
accounts_schema = "public"
redis_key_prefix = ""
clickhouse_database = "default"

[multitenancy.tenants.public.user]
control_center_url =  "http://localhost:9000"

[user_auth_methods]
encryption_key = "user_auth_table_encryption_key" # Encryption key used for encrypting data in user_authentication_methods table

[cell_information]
id = "12345" # Default CellID for Global Cell Information

[network_tokenization_service] # Network Tokenization Service Configuration
generate_token_url= ""        # base url to generate token
fetch_token_url= ""           # base url to fetch token
token_service_api_key= ""      # api key for token service
public_key= ""                # public key to encrypt data for token service
private_key= ""               # private key to decrypt  response payload from token service
key_id= ""                    # key id to encrypt data for token service
delete_token_url= ""          # base url to delete token from token service
check_token_status_url= ""    # base url to check token status from token service

[grpc_client.dynamic_routing_client] # Dynamic Routing Client Configuration
host = "localhost" # Client Host
port = 7000        # Client Port
service = "dynamo" # Service name

[theme.storage]
file_storage_backend = "aws_s3" # Theme storage backend to be used

[theme.storage.aws_s3]
region = "bucket_region" # AWS region where the S3 bucket for theme storage is located
bucket_name = "bucket"   # AWS S3 bucket name for theme storage

[theme.email_config]
entity_name = "Hyperswitch"                      # Name of the entity to be showed in emails
entity_logo_url = "https://example.com/logo.svg" # Logo URL of the entity to be used in emails
foreground_color = "#000000"                     # Foreground color of email text
primary_color = "#006DF9"                        # Primary color of email body
background_color = "#FFFFFF"                     # Background color of email body

[connectors.unified_authentication_service] #Unified Authentication Service Configuration
base_url = "http://localhost:8000" #base url to call unified authentication service

[connectors.hyperswitch_vault] # Hyperswitch Vault Configuration
base_url = "http://localhost:8080" # base url to call hyperswitch vault service

[clone_connector_allowlist]
merchant_ids = "merchant_ids"           # Comma-separated list of allowed merchant IDs
connector_names = "connector_names"     # Comma-separated list of allowed connector names
