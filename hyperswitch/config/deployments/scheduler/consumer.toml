# Scheduler settings provides a point to modify the behaviour of scheduler flow.
# It defines the streams/queues name and configuration as well as event selection variables
[scheduler]
consumer_group = "scheduler_group"
graceful_shutdown_interval = 60000 # Specifies how much time to wait while re-attempting shutdown for a service (in milliseconds)
loop_interval = 3000               # Specifies how much time to wait before starting the defined behaviour of producer or consumer (in milliseconds)0
stream = "scheduler_stream"

[scheduler.consumer]
consumer_group = "scheduler_group"
disabled = false                   # This flag decides if the consumer should actively consume task

# Scheduler server configuration
[scheduler.server]
port = 3000                       # Port on which the server will listen for incoming requests
host = "127.0.0.1"                # Host IP address to bind the server to
workers = 1                       # Number of actix workers to handle incoming requests concurrently
