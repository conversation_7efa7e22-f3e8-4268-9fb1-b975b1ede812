CREATE TABLE payout_queue (
    `payout_id` String,
    `payout_attempt_id` String,
    `merchant_id` String,
    `customer_id` String,
    `address_id` String,
    `profile_id` String,
    `payout_method_id` Nullable(String),
    `payout_type` LowCardinality(String),
    `amount` UInt64,
    `destination_currency` LowCardinality(String),
    `source_currency` LowCardinality(String),
    `description` Nullable(String),
    `recurring` Bool,
    `auto_fulfill` Bool,
    `return_url` Nullable(String),
    `entity_type` LowCardinality(String),
    `metadata` Nullable(String),
    `created_at` DateTime CODEC(T64, LZ4),
    `last_modified_at` DateTime CODEC(T64, LZ4),
    `attempt_count` UInt16,
    `status` LowCardinality(String),
    `connector` Nullable(String),
    `connector_payout_id` String,
    `is_eligible` Nullable(Bool),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `business_country` LowCardinality(Nullable(String)),
    `business_label` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `sign_flag` Int8
) ENGINE = Kafka SETTINGS kafka_broker_list = 'kafka0:29092',
kafka_topic_list = 'hyperswitch-payout-events',
kafka_group_name = 'hyper',
kafka_format = 'JSONEachRow',
kafka_handle_error_mode = 'stream';

CREATE TABLE payout (
    `payout_id` String,
    `payout_attempt_id` String,
    `merchant_id` String,
    `customer_id` String,
    `address_id` String,
    `profile_id` String,
    `payout_method_id` Nullable(String),
    `payout_type` LowCardinality(String),
    `amount` UInt64,
    `destination_currency` LowCardinality(String),
    `source_currency` LowCardinality(String),
    `description` Nullable(String),
    `recurring` Bool,
    `auto_fulfill` Bool,
    `return_url` Nullable(String),
    `entity_type` LowCardinality(String),
    `metadata` Nullable(String),
    `created_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `last_modified_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `attempt_count` UInt16,
    `status` LowCardinality(String),
    `connector` Nullable(String),
    `connector_payout_id` String,
    `is_eligible` Nullable(Bool),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `business_country` LowCardinality(Nullable(String)),
    `business_label` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `inserted_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `sign_flag` Int8,
    INDEX payoutTypeIndex payout_type TYPE bloom_filter GRANULARITY 1,
    INDEX destinationCurrencyIndex destination_currency TYPE bloom_filter GRANULARITY 1,
    INDEX sourceCurrencyIndex source_currency TYPE bloom_filter GRANULARITY 1,
    INDEX entityTypeIndex entity_type TYPE bloom_filter GRANULARITY 1,
    INDEX statusIndex status TYPE bloom_filter GRANULARITY 1,
    INDEX businessCountryIndex business_country TYPE bloom_filter GRANULARITY 1
) ENGINE = CollapsingMergeTree(sign_flag) PARTITION BY toStartOfDay(created_at)
ORDER BY
    (created_at, merchant_id, payout_id) TTL created_at + toIntervalMonth(6) SETTINGS index_granularity = 8192;

CREATE MATERIALIZED VIEW payout_mv TO payout (
    `payout_id` String,
    `payout_attempt_id` String,
    `merchant_id` String,
    `customer_id` String,
    `address_id` String,
    `profile_id` String,
    `payout_method_id` Nullable(String),
    `payout_type` LowCardinality(String),
    `amount` UInt64,
    `destination_currency` LowCardinality(String),
    `source_currency` LowCardinality(String),
    `description` Nullable(String),
    `recurring` Bool,
    `auto_fulfill` Bool,
    `return_url` Nullable(String),
    `entity_type` LowCardinality(String),
    `metadata` Nullable(String),
    `created_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `last_modified_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `attempt_count` UInt16,
    `status` LowCardinality(String),
    `connector` Nullable(String),
    `connector_payout_id` String,
    `is_eligible` Nullable(Bool),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `business_country` LowCardinality(Nullable(String)),
    `business_label` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `inserted_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `sign_flag` Int8,
) AS
SELECT
    payout_id,
    payout_attempt_id,
    merchant_id,
    customer_id,
    address_id,
    profile_id,
    payout_method_id,
    payout_type,
    amount,
    destination_currency,
    source_currency,
    description,
    recurring,
    auto_fulfill,
    return_url,
    entity_type,
    metadata,
    created_at,
    last_modified_at,
    attempt_count,
    status,
    connector,
    connector_payout_id,
    is_eligible,
    error_message,
    error_code,
    business_country,
    business_label,
    merchant_connector_id,
    now() as inserted_at,
    sign_flag
FROM
    payout_queue;