CREATE TABLE authentication_queue (
    `authentication_id` String,
    `merchant_id` String,
    `authentication_connector` LowCardinality(String),
    `connector_authentication_id` Nullable(String),
    `authentication_data` Nullable(String),
    `payment_method_id` Nullable(String),
    `authentication_type` LowCardinality(Nullable(String)),
    `authentication_status` LowCardinality(String),
    `authentication_lifecycle_status` LowCardinality(String),
    `created_at` DateTime64(3),
    `modified_at` DateTime64(3),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `connector_metadata` Nullable(String),
    `maximum_supported_version` LowCardinality(Nullable(String)),
    `threeds_server_transaction_id` Nullable(String),
    `cavv` Nullable(String),
    `authentication_flow_type` Nullable(String),
    `message_version` LowCardinality(Nullable(String)),
    `eci` Nullable(String),
    `trans_status` LowCardinality(Nullable(String)),
    `acquirer_bin` Nullable(String),
    `acquirer_merchant_id` Nullable(String),
    `three_ds_method_data` Nullable(String),
    `three_ds_method_url` Nullable(String),
    `acs_url` Nullable(String),
    `challenge_request` Nullable(String),
    `acs_reference_number` Nullable(String),
    `acs_trans_id` Nullable(String),
    `acs_signed_content` Nullable(String),
    `profile_id` String,
    `payment_id` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `ds_trans_id` Nullable(String),
    `directory_server_id` Nullable(String),
    `acquirer_country_code` Nullable(String),
    `organization_id` String,
    `sign_flag` Int8,
    `mcc` Nullable(String),
    `amount` Nullable(UInt32),
    `currency` LowCardinality(Nullable(String)),
    `merchant_country` LowCardinality(Nullable(String)),
    `billing_country` LowCardinality(Nullable(String)),
    `shipping_country` LowCardinality(Nullable(String)),
    `issuer_country` LowCardinality(Nullable(String)),
    `earliest_supported_version` LowCardinality(Nullable(String)),
    `latest_supported_version` LowCardinality(Nullable(String)),
    `whitelist_decision` Nullable(Bool),
    `device_manufacturer` Nullable(String),
    `platform` LowCardinality(Nullable(String)),
    `device_type` LowCardinality(Nullable(String)),
    `device_brand` Nullable(String),
    `device_os` Nullable(String),
    `device_display` Nullable(String),
    `browser_name` Nullable(String),
    `browser_version` Nullable(String),
    `issuer_id` Nullable(String),
    `scheme_name` Nullable(String),
    `exemption_requested` Nullable(Bool),
    `exemption_accepted` Nullable(Bool)
) ENGINE = Kafka SETTINGS kafka_broker_list = 'kafka0:29092',
kafka_topic_list = 'hyperswitch-authentication-events',
kafka_group_name = 'hyper',
kafka_format = 'JSONEachRow',
kafka_handle_error_mode = 'stream';

CREATE TABLE authentications (
    `authentication_id` String,
    `merchant_id` String,
    `authentication_connector` LowCardinality(String),
    `connector_authentication_id` Nullable(String),
    `authentication_data` Nullable(String),
    `payment_method_id` Nullable(String),
    `authentication_type` LowCardinality(Nullable(String)),
    `authentication_status` LowCardinality(String),
    `authentication_lifecycle_status` LowCardinality(String),
    `created_at` DateTime64(3) DEFAULT now64(),
    `inserted_at` DateTime64(3) DEFAULT now64(),
    `modified_at` DateTime64(3) DEFAULT now64(),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `connector_metadata` Nullable(String),
    `maximum_supported_version` LowCardinality(Nullable(String)),
    `threeds_server_transaction_id` Nullable(String),
    `cavv` Nullable(String),
    `authentication_flow_type` Nullable(String),
    `message_version` LowCardinality(Nullable(String)),
    `eci` Nullable(String),
    `trans_status` LowCardinality(Nullable(String)),
    `acquirer_bin` Nullable(String),
    `acquirer_merchant_id` Nullable(String),
    `three_ds_method_data` Nullable(String),
    `three_ds_method_url` Nullable(String),
    `acs_url` Nullable(String),
    `challenge_request` Nullable(String),
    `acs_reference_number` Nullable(String),
    `acs_trans_id` Nullable(String),
    `acs_signed_content` Nullable(String),
    `profile_id` String,
    `payment_id` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `ds_trans_id` Nullable(String),
    `directory_server_id` Nullable(String),
    `acquirer_country_code` Nullable(String),
    `organization_id` String,
    `sign_flag` Int8,
    `mcc` Nullable(String),
    `amount` Nullable(UInt32),
    `currency` LowCardinality(Nullable(String)),
    `merchant_country` LowCardinality(Nullable(String)),
    `billing_country` LowCardinality(Nullable(String)),
    `shipping_country` LowCardinality(Nullable(String)),
    `issuer_country` LowCardinality(Nullable(String)),
    `earliest_supported_version` LowCardinality(Nullable(String)),
    `latest_supported_version` LowCardinality(Nullable(String)),
    `whitelist_decision` Nullable(Bool),
    `device_manufacturer` Nullable(String),
    `platform` LowCardinality(Nullable(String)),
    `device_type` LowCardinality(Nullable(String)),
    `device_brand` Nullable(String),
    `device_os` Nullable(String),
    `device_display` Nullable(String),
    `browser_name` Nullable(String),
    `browser_version` Nullable(String),
    `issuer_id` Nullable(String),
    `scheme_name` Nullable(String),
    `exemption_requested` Nullable(Bool),
    `exemption_accepted` Nullable(Bool),
    INDEX authenticationConnectorIndex authentication_connector TYPE bloom_filter GRANULARITY 1,
    INDEX transStatusIndex trans_status TYPE bloom_filter GRANULARITY 1,
    INDEX authenticationTypeIndex authentication_type TYPE bloom_filter GRANULARITY 1,
    INDEX authenticationStatusIndex authentication_status TYPE bloom_filter GRANULARITY 1
) ENGINE = CollapsingMergeTree(sign_flag) PARTITION BY toStartOfDay(created_at)
ORDER BY
    (created_at, merchant_id, authentication_id) TTL toStartOfDay(created_at) + toIntervalMonth(18) SETTINGS index_granularity = 8192;

CREATE MATERIALIZED VIEW authentication_mv TO authentications (
    `authentication_id` String,
    `merchant_id` String,
    `authentication_connector` LowCardinality(String),
    `connector_authentication_id` Nullable(String),
    `authentication_data` Nullable(String),
    `payment_method_id` Nullable(String),
    `authentication_type` LowCardinality(Nullable(String)),
    `authentication_status` LowCardinality(String),
    `authentication_lifecycle_status` LowCardinality(String),
    `created_at` DateTime64(3) DEFAULT now64(),
    `inserted_at` DateTime64(3) DEFAULT now64(),
    `modified_at` DateTime64(3) DEFAULT now64(),
    `error_message` Nullable(String),
    `error_code` Nullable(String),
    `connector_metadata` Nullable(String),
    `maximum_supported_version` LowCardinality(Nullable(String)),
    `threeds_server_transaction_id` Nullable(String),
    `cavv` Nullable(String),
    `authentication_flow_type` Nullable(String),
    `message_version` LowCardinality(Nullable(String)),
    `eci` Nullable(String),
    `trans_status` LowCardinality(Nullable(String)),
    `acquirer_bin` Nullable(String),
    `acquirer_merchant_id` Nullable(String),
    `three_ds_method_data` Nullable(String),
    `three_ds_method_url` Nullable(String),
    `acs_url` Nullable(String),
    `challenge_request` Nullable(String),
    `acs_reference_number` Nullable(String),
    `acs_trans_id` Nullable(String),
    `acs_signed_content` Nullable(String),
    `profile_id` String,
    `payment_id` Nullable(String),
    `merchant_connector_id` Nullable(String),
    `ds_trans_id` Nullable(String),
    `directory_server_id` Nullable(String),
    `acquirer_country_code` Nullable(String),
    `organization_id` String,
    `sign_flag` Int8,
    `mcc` Nullable(String),
    `amount` Nullable(UInt32),
    `currency` LowCardinality(Nullable(String)),
    `merchant_country` LowCardinality(Nullable(String)),
    `billing_country` LowCardinality(Nullable(String)),
    `shipping_country` LowCardinality(Nullable(String)),
    `issuer_country` LowCardinality(Nullable(String)),
    `earliest_supported_version` LowCardinality(Nullable(String)),
    `latest_supported_version` LowCardinality(Nullable(String)),
    `whitelist_decision` Nullable(Bool),
    `device_manufacturer` Nullable(String),
    `platform` LowCardinality(Nullable(String)),
    `device_type` LowCardinality(Nullable(String)),
    `device_brand` Nullable(String),
    `device_os` Nullable(String),
    `device_display` Nullable(String),
    `browser_name` Nullable(String),
    `browser_version` Nullable(String),
    `issuer_id` Nullable(String),
    `scheme_name` Nullable(String),
    `exemption_requested` Nullable(Bool),
    `exemption_accepted` Nullable(Bool)
) AS
SELECT
    authentication_id,
    merchant_id,
    authentication_connector,
    connector_authentication_id,
    authentication_data,
    payment_method_id,
    authentication_type,
    authentication_status,
    authentication_lifecycle_status,
    created_at,
    now64() as inserted_at,
    modified_at,
    error_message,
    error_code,
    connector_metadata,
    maximum_supported_version,
    threeds_server_transaction_id,
    cavv,
    authentication_flow_type,
    message_version,
    eci,
    trans_status,
    acquirer_bin,
    acquirer_merchant_id,
    three_ds_method_data,
    three_ds_method_url,
    acs_url,
    challenge_request,
    acs_reference_number,
    acs_trans_id,
    acs_signed_content,
    profile_id,
    payment_id,
    merchant_connector_id,
    ds_trans_id,
    directory_server_id,
    acquirer_country_code,
    organization_id,
    sign_flag,
    mcc,
    amount,
    currency,
    merchant_country,
    billing_country,
    shipping_country,
    issuer_country,
    earliest_supported_version,
    latest_supported_version,
    whitelist_decision,
    device_manufacturer,
    platform,
    device_type,
    device_brand,
    device_os,
    device_display,
    browser_name,
    browser_version,
    issuer_id,
    scheme_name,
    exemption_requested,
    exemption_accepted
FROM
    authentication_queue
WHERE
    length(_error) = 0;

CREATE MATERIALIZED VIEW authentication_parse_errors (
    `topic` String,
    `partition` Int64,
    `offset` Int64,
    `raw` String,
    `error` String
) ENGINE = MergeTree
ORDER BY
    (topic, partition, offset) SETTINGS index_granularity = 8192 AS
SELECT
    _topic AS topic,
    _partition AS partition,
    _offset AS offset,
    _raw_message AS raw,
    _error AS error
FROM
    authentication_queue
WHERE
    length(_error) > 0;