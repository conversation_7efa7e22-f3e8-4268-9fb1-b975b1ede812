CREATE TABLE payment_intents_queue
(
    `payment_id` String,
    `merchant_id` String,
    `status` LowCardinality(String),
    `amount` UInt32,
    `currency` LowCardinality(Nullable(String)),
    `amount_captured` Nullable(UInt32),
    `customer_id` Nullable(String),
    `description` Nullable(String),
    `return_url` Nullable(String),
    `connector_id` LowCardinality(Nullable(String)),
    `statement_descriptor_name` Nullable(String),
    `statement_descriptor_suffix` Nullable(String),
    `setup_future_usage` LowCardinality(Nullable(String)),
    `off_session` Nullable(Bool),
    `client_secret` Nullable(String),
    `active_attempt_id` String,
    `business_country` LowCardinality(String),
    `business_label` String,
    `attempt_count` UInt8,
    `profile_id` Nullable(String),
    `modified_at` DateTime CODEC(T64, LZ4),
    `created_at` DateTime CODEC(T64, LZ4),
    `last_synced` Nullable(DateTime) CODEC(T64, LZ4),
    `organization_id` String,
    `sign_flag` Int8
) ENGINE = Kafka SETTINGS kafka_broker_list = 'kafka0:29092',
kafka_topic_list = 'hyperswitch-payment-intent-events',
kafka_group_name = 'hyper',
kafka_format = 'JSONEachRow',
kafka_handle_error_mode = 'stream';

CREATE TABLE payment_intents
(
    `payment_id` String,
    `merchant_id` LowCardinality(String),
    `status` LowCardinality(String),
    `amount` UInt32,
    `currency` LowCardinality(Nullable(String)),
    `amount_captured` Nullable(UInt32),
    `customer_id` Nullable(String),
    `description` Nullable(String),
    `return_url` Nullable(String),
    `connector_id` LowCardinality(Nullable(String)),
    `statement_descriptor_name` Nullable(String),
    `statement_descriptor_suffix` Nullable(String),
    `setup_future_usage` LowCardinality(Nullable(String)),
    `off_session` Nullable(Bool),
    `client_secret` Nullable(String),
    `active_attempt_id` String,
    `business_country` LowCardinality(String),
    `business_label` String,
    `attempt_count` UInt8,
    `profile_id` Nullable(String),
    `modified_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `created_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `last_synced` Nullable(DateTime) CODEC(T64, LZ4),
    `inserted_at` DateTime DEFAULT now() CODEC(T64, LZ4),
    `organization_id` String,
    `sign_flag` Int8,
    INDEX connectorIndex connector_id TYPE bloom_filter GRANULARITY 1,
    INDEX currencyIndex currency TYPE bloom_filter GRANULARITY 1,
    INDEX statusIndex status TYPE bloom_filter GRANULARITY 1
)
ENGINE = CollapsingMergeTree(sign_flag)
PARTITION BY toStartOfDay(created_at)
ORDER BY (created_at, merchant_id, payment_id)
TTL created_at + toIntervalMonth(18)
SETTINGS index_granularity = 8192;

CREATE MATERIALIZED VIEW payment_intents_mv TO payment_intents
(
    `payment_id` String,
    `merchant_id` String,
    `status` LowCardinality(String),
    `amount` UInt32,
    `currency` LowCardinality(Nullable(String)),
    `amount_captured` Nullable(UInt32),
    `customer_id` Nullable(String),
    `description` Nullable(String),
    `return_url` Nullable(String),
    `connector_id` LowCardinality(Nullable(String)),
    `statement_descriptor_name` Nullable(String),
    `statement_descriptor_suffix` Nullable(String),
    `setup_future_usage` LowCardinality(Nullable(String)),
    `off_session` Nullable(Bool),
    `client_secret` Nullable(String),
    `active_attempt_id` String,
    `business_country` LowCardinality(String),
    `business_label` String,
    `attempt_count` UInt8,
    `profile_id` Nullable(String),
    `modified_at` DateTime64(3),
    `created_at` DateTime64(3),
    `last_synced` Nullable(DateTime64(3)),
    `inserted_at` DateTime64(3),
    `organization_id` String,
    `sign_flag` Int8
) AS
SELECT
    payment_id,
    merchant_id,
    status,
    amount,
    currency,
    amount_captured,
    customer_id,
    description,
    return_url,
    connector_id,
    statement_descriptor_name,
    statement_descriptor_suffix,
    setup_future_usage,
    off_session,
    client_secret,
    active_attempt_id,
    business_country,
    business_label,
    attempt_count,
    profile_id,
    modified_at,
    created_at,
    last_synced,
    now() AS inserted_at,
    organization_id,
    sign_flag
FROM payment_intents_queue;