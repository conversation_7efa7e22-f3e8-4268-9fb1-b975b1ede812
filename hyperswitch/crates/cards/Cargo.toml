[package]
name = "cards"
description = "Types to handle card masking and validation"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
readme = "README.md"
license.workspace = true

[features]

[dependencies]
error-stack = "0.4.1"
serde = { version = "1.0.219", features = ["derive"] }
thiserror = "1.0.69"
time = "0.3.41"
regex = "1.11.1"

# First party crates
common_utils = { version = "0.1.0", path = "../common_utils" }
masking = { version = "0.1.0", path = "../masking" }

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
router_env = { version = "0.1.0", path = "../router_env", features = ["log_extra_implicit_fields", "log_custom_entries_to_extra"], default-features = false }

[dev-dependencies]
serde_json = "1.0.140"

[lints]
workspace = true
