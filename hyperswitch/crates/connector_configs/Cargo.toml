[package]
name = "connector_configs"
description = "Connector Integration Dashboard"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
license.workspace = true

[features]
default = ["payouts", "dummy_connector"]
production = []
sandbox = []
dummy_connector = ["api_models/dummy_connector"]
payouts = ["api_models/payouts"]
v1 = ["api_models/v1", "common_utils/v1"]

[dependencies]
# First party crates
api_models = { version = "0.1.0", path = "../api_models", package = "api_models" }
common_utils = { version = "0.1.0", path = "../common_utils" }

# Third party crates
serde = { version = "1.0.219", features = ["derive"] }
serde_with = "3.12.0"
toml = "0.8.22"
utoipa = { version = "4.2.3", features = ["preserve_order", "preserve_path_order"] }

[lints]
workspace = true
