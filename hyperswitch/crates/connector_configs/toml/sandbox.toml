[aci]
[[aci.credit]]
  payment_method_type = "Mastercard"
[[aci.credit]]
  payment_method_type = "Visa"
[[aci.credit]]
  payment_method_type = "Interac"
[[aci.credit]]
  payment_method_type = "AmericanExpress"
[[aci.credit]]
  payment_method_type = "JCB"
[[aci.credit]]
  payment_method_type = "DinersClub"
[[aci.credit]]
  payment_method_type = "Discover"
[[aci.credit]]
  payment_method_type = "CartesBancaires"
[[aci.credit]]
  payment_method_type = "UnionPay"
[[aci.debit]]
  payment_method_type = "Mastercard"
[[aci.debit]]
  payment_method_type = "Visa"
[[aci.debit]]
  payment_method_type = "Interac"
[[aci.debit]]
  payment_method_type = "AmericanExpress"
[[aci.debit]]
  payment_method_type = "JCB"
[[aci.debit]]
  payment_method_type = "DinersClub"
[[aci.debit]]
  payment_method_type = "Discover"
[[aci.debit]]
  payment_method_type = "CartesBancaires"
[[aci.debit]]
  payment_method_type = "UnionPay"
[[aci.wallet]]
  payment_method_type = "ali_pay"
[[aci.wallet]]
  payment_method_type = "mb_way"
[[aci.bank_redirect]]
  payment_method_type = "ideal"
[[aci.bank_redirect]]
  payment_method_type = "giropay"
[[aci.bank_redirect]]
  payment_method_type = "sofort"
[[aci.bank_redirect]]
  payment_method_type = "eps"
[[aci.bank_redirect]]
  payment_method_type = "przelewy24"
[[aci.bank_redirect]]
  payment_method_type = "trustly"
[[aci.bank_redirect]]
  payment_method_type = "interac"
[aci.connector_auth.BodyKey]
api_key="API Key"
key1="Entity ID"
[aci.connector_webhook_details]
merchant_secret="Source verification key"


[adyen]
[[adyen.credit]]
  payment_method_type = "Mastercard"
[[adyen.credit]]
  payment_method_type = "Visa"
[[adyen.credit]]
  payment_method_type = "Interac"
[[adyen.credit]]
  payment_method_type = "AmericanExpress"
[[adyen.credit]]
  payment_method_type = "JCB"
[[adyen.credit]]
  payment_method_type = "DinersClub"
[[adyen.credit]]
  payment_method_type = "Discover"
[[adyen.credit]]
  payment_method_type = "CartesBancaires"
[[adyen.credit]]
  payment_method_type = "UnionPay"
[[adyen.debit]]
  payment_method_type = "Mastercard"
[[adyen.debit]]
  payment_method_type = "Visa"
[[adyen.debit]]
  payment_method_type = "Nyce"
[[adyen.debit]]
  payment_method_type = "Pulse"
[[adyen.debit]]
  payment_method_type = "Star"
[[adyen.debit]]
  payment_method_type = "Accel"
[[adyen.debit]]
  payment_method_type = "Interac"
[[adyen.debit]]
  payment_method_type = "AmericanExpress"
[[adyen.debit]]
  payment_method_type = "JCB"
[[adyen.debit]]
  payment_method_type = "DinersClub"
[[adyen.debit]]
  payment_method_type = "Discover"
[[adyen.debit]]
  payment_method_type = "CartesBancaires"
[[adyen.debit]]
  payment_method_type = "UnionPay"
[[adyen.pay_later]]
  payment_method_type = "klarna"
[[adyen.pay_later]]
  payment_method_type = "affirm"
[[adyen.pay_later]]
  payment_method_type = "afterpay_clearpay"
[[adyen.pay_later]]
  payment_method_type = "pay_bright"
[[adyen.pay_later]]
  payment_method_type = "walley"
[[adyen.pay_later]]
  payment_method_type = "alma"
[[adyen.pay_later]]
  payment_method_type = "atome"
[[adyen.bank_debit]]
  payment_method_type = "ach"
[[adyen.bank_debit]]
  payment_method_type = "bacs"
[[adyen.bank_debit]]
  payment_method_type = "sepa"
[[adyen.bank_redirect]]
  payment_method_type = "ideal"
[[adyen.bank_redirect]]
  payment_method_type = "eps"
[[adyen.bank_redirect]]
  payment_method_type = "blik"
[[adyen.bank_redirect]]
  payment_method_type = "trustly"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_czech_republic"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_finland"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_poland"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_slovakia"
[[adyen.bank_redirect]]
  payment_method_type = "bancontact_card"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_fpx"
[[adyen.bank_redirect]]
  payment_method_type = "online_banking_thailand"
[[adyen.bank_redirect]]
  payment_method_type = "bizum"
[[adyen.bank_redirect]]
  payment_method_type = "open_banking_uk"
[[adyen.bank_transfer]]
  payment_method_type = "permata_bank_transfer"
[[adyen.bank_transfer]]
  payment_method_type = "bca_bank_transfer"
[[adyen.bank_transfer]]
  payment_method_type = "bni_va"
[[adyen.bank_transfer]]
  payment_method_type = "bri_va"
[[adyen.bank_transfer]]
  payment_method_type = "cimb_va"
[[adyen.bank_transfer]]
  payment_method_type = "danamon_va"
[[adyen.bank_transfer]]
  payment_method_type = "mandiri_va"
[[adyen.bank_transfer]]
  payment_method_type = "pix"
[[adyen.wallet]]
  payment_method_type = "apple_pay"
[[adyen.wallet]]
  payment_method_type = "google_pay"
[[adyen.wallet]]
  payment_method_type = "paypal"
[[adyen.wallet]]
  payment_method_type = "we_chat_pay"
[[adyen.wallet]]
  payment_method_type = "ali_pay"
[[adyen.wallet]]
  payment_method_type = "mb_way"
[[adyen.wallet]]
  payment_method_type = "ali_pay_hk"
[[adyen.wallet]]
  payment_method_type = "go_pay"
[[adyen.wallet]]
  payment_method_type = "kakao_pay"
[[adyen.wallet]]
  payment_method_type = "twint"
[[adyen.wallet]]
  payment_method_type = "gcash"
[[adyen.wallet]]
  payment_method_type = "vipps"
[[adyen.wallet]]
  payment_method_type = "dana"
[[adyen.wallet]]
  payment_method_type = "momo"
[[adyen.wallet]]
  payment_method_type = "swish"
[[adyen.wallet]]
  payment_method_type = "touch_n_go"
[[adyen.voucher]]
  payment_method_type = "boleto"
[[adyen.voucher]]
  payment_method_type = "alfamart"
[[adyen.voucher]]
  payment_method_type = "indomaret"
[[adyen.voucher]]
  payment_method_type = "oxxo"
[[adyen.voucher]]
  payment_method_type = "seven_eleven"
[[adyen.voucher]]
  payment_method_type = "lawson"
[[adyen.voucher]]
  payment_method_type = "mini_stop"
[[adyen.voucher]]
  payment_method_type = "family_mart"
[[adyen.voucher]]
  payment_method_type = "seicomart"
[[adyen.voucher]]
  payment_method_type = "pay_easy"
[[adyen.gift_card]]
  payment_method_type = "pay_safe_card"
[[adyen.gift_card]]
  payment_method_type = "givex"
[[adyen.card_redirect]]
  payment_method_type = "benefit"
[[adyen.card_redirect]]
  payment_method_type = "knet"
[[adyen.card_redirect]]
  payment_method_type = "momo_atm"
[adyen.connector_auth.BodyKey]
api_key="Adyen API Key"
key1="Adyen Account Id"
[adyen.connector_webhook_details]
merchant_secret="Source verification key"

[[adyen.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[adyen.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[adyen.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[adyen.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[adyen.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[adyen.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[adyen.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[adyen.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[adyen.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[adyen.metadata.endpoint_prefix]
name="endpoint_prefix"
label="Live endpoint prefix"
placeholder="Enter Live endpoint prefix"
required=true
type="Text"

[adyenplatform_payout]
[[adyenplatform_payout.bank_transfer]]
  payment_method_type = "sepa"
[adyenplatform_payout.connector_auth.HeaderKey]
api_key = "Adyen platform's API Key"

[adyenplatform_payout.metadata.source_balance_account]
name="source_balance_account"
label="Source balance account ID"
placeholder="Enter Source balance account ID"
required=true
type="Text"
[adyenplatform_payout.connector_webhook_details]
merchant_secret="Source verification key"

[airwallex]
[[airwallex.credit]]
  payment_method_type = "Mastercard"
[[airwallex.credit]]
  payment_method_type = "Visa"
[[airwallex.credit]]
  payment_method_type = "Interac"
[[airwallex.credit]]
  payment_method_type = "AmericanExpress"
[[airwallex.credit]]
  payment_method_type = "JCB"
[[airwallex.credit]]
  payment_method_type = "DinersClub"
[[airwallex.credit]]
  payment_method_type = "Discover"
[[airwallex.credit]]
  payment_method_type = "CartesBancaires"
[[airwallex.credit]]
  payment_method_type = "UnionPay"
[[airwallex.debit]]
  payment_method_type = "Mastercard"
[[airwallex.debit]]
  payment_method_type = "Visa"
[[airwallex.debit]]
  payment_method_type = "Interac"
[[airwallex.debit]]
  payment_method_type = "AmericanExpress"
[[airwallex.debit]]
  payment_method_type = "JCB"
[[airwallex.debit]]
  payment_method_type = "DinersClub"
[[airwallex.debit]]
  payment_method_type = "Discover"
[[airwallex.debit]]
  payment_method_type = "CartesBancaires"
[[airwallex.debit]]
  payment_method_type = "UnionPay"
[[airwallex.wallet]]
  payment_method_type = "google_pay"
[airwallex.connector_auth.BodyKey]
api_key="API Key"
key1="Client ID"

[airwallex.connector_webhook_details]
merchant_secret="Source verification key"

[[airwallex.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[airwallex.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[airwallex.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[airwallex.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[airwallex.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[airwallex.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]



[authorizedotnet]
[[authorizedotnet.credit]]
  payment_method_type = "Mastercard"
[[authorizedotnet.credit]]
  payment_method_type = "Visa"
[[authorizedotnet.credit]]
  payment_method_type = "Interac"
[[authorizedotnet.credit]]
  payment_method_type = "AmericanExpress"
[[authorizedotnet.credit]]
  payment_method_type = "JCB"
[[authorizedotnet.credit]]
  payment_method_type = "DinersClub"
[[authorizedotnet.credit]]
  payment_method_type = "Discover"
[[authorizedotnet.credit]]
  payment_method_type = "CartesBancaires"
[[authorizedotnet.credit]]
  payment_method_type = "UnionPay"
[[authorizedotnet.debit]]
  payment_method_type = "Mastercard"
[[authorizedotnet.debit]]
  payment_method_type = "Visa"
[[authorizedotnet.debit]]
  payment_method_type = "Interac"
[[authorizedotnet.debit]]
  payment_method_type = "AmericanExpress"
[[authorizedotnet.debit]]
  payment_method_type = "JCB"
[[authorizedotnet.debit]]
  payment_method_type = "DinersClub"
[[authorizedotnet.debit]]
  payment_method_type = "Discover"
[[authorizedotnet.debit]]
  payment_method_type = "CartesBancaires"
[[authorizedotnet.debit]]
  payment_method_type = "UnionPay"
[[authorizedotnet.wallet]]
  payment_method_type = "apple_pay"
[[authorizedotnet.wallet]]
  payment_method_type = "google_pay"
[[authorizedotnet.wallet]]
  payment_method_type = "paypal"
[authorizedotnet.connector_auth.BodyKey]
api_key="API Login ID"
key1="Transaction Key"
[authorizedotnet.connector_webhook_details]
merchant_secret="Source verification key"

[[authorizedotnet.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[authorizedotnet.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[authorizedotnet.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[authorizedotnet.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[authorizedotnet.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[authorizedotnet.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[authorizedotnet.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[authorizedotnet.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[authorizedotnet.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[bambora]
[[bambora.credit]]
  payment_method_type = "Mastercard"
[[bambora.credit]]
  payment_method_type = "Visa"
[[bambora.credit]]
  payment_method_type = "Interac"
[[bambora.credit]]
  payment_method_type = "AmericanExpress"
[[bambora.credit]]
  payment_method_type = "JCB"
[[bambora.credit]]
  payment_method_type = "DinersClub"
[[bambora.credit]]
  payment_method_type = "Discover"
[[bambora.credit]]
  payment_method_type = "CartesBancaires"
[[bambora.credit]]
  payment_method_type = "UnionPay"
[[bambora.debit]]
  payment_method_type = "Mastercard"
[[bambora.debit]]
  payment_method_type = "Visa"
[[bambora.debit]]
  payment_method_type = "Interac"
[[bambora.debit]]
  payment_method_type = "AmericanExpress"
[[bambora.debit]]
  payment_method_type = "JCB"
[[bambora.debit]]
  payment_method_type = "DinersClub"
[[bambora.debit]]
  payment_method_type = "Discover"
[[bambora.debit]]
  payment_method_type = "CartesBancaires"
[[bambora.debit]]
  payment_method_type = "UnionPay"
[bambora.connector_auth.BodyKey]
api_key="Passcode"
key1="Merchant Id"

[bamboraapac]
[[bamboraapac.credit]]
  payment_method_type = "Mastercard"
[[bamboraapac.credit]]
  payment_method_type = "Visa"
[[bamboraapac.credit]]
  payment_method_type = "Interac"
[[bamboraapac.credit]]
  payment_method_type = "AmericanExpress"
[[bamboraapac.credit]]
  payment_method_type = "JCB"
[[bamboraapac.credit]]
  payment_method_type = "DinersClub"
[[bamboraapac.credit]]
  payment_method_type = "Discover"
[[bamboraapac.credit]]
  payment_method_type = "CartesBancaires"
[[bamboraapac.credit]]
  payment_method_type = "UnionPay"
[[bamboraapac.debit]]
  payment_method_type = "Mastercard"
[[bamboraapac.debit]]
  payment_method_type = "Visa"
[[bamboraapac.debit]]
  payment_method_type = "Interac"
[[bamboraapac.debit]]
  payment_method_type = "AmericanExpress"
[[bamboraapac.debit]]
  payment_method_type = "JCB"
[[bamboraapac.debit]]
  payment_method_type = "DinersClub"
[[bamboraapac.debit]]
  payment_method_type = "Discover"
[[bamboraapac.debit]]
  payment_method_type = "CartesBancaires"
[[bamboraapac.debit]]
  payment_method_type = "UnionPay"
[bamboraapac.connector_auth.SignatureKey]
api_key="Username"
key1="Account Number"
api_secret="Password"

[bankofamerica]
[[bankofamerica.credit]]
  payment_method_type = "Mastercard"
[[bankofamerica.credit]]
  payment_method_type = "Visa"
[[bankofamerica.credit]]
  payment_method_type = "Interac"
[[bankofamerica.credit]]
  payment_method_type = "AmericanExpress"
[[bankofamerica.credit]]
  payment_method_type = "JCB"
[[bankofamerica.credit]]
  payment_method_type = "DinersClub"
[[bankofamerica.credit]]
  payment_method_type = "Discover"
[[bankofamerica.credit]]
  payment_method_type = "CartesBancaires"
[[bankofamerica.credit]]
  payment_method_type = "UnionPay"
[[bankofamerica.debit]]
  payment_method_type = "Mastercard"
[[bankofamerica.debit]]
  payment_method_type = "Visa"
[[bankofamerica.debit]]
  payment_method_type = "Interac"
[[bankofamerica.debit]]
  payment_method_type = "AmericanExpress"
[[bankofamerica.debit]]
  payment_method_type = "JCB"
[[bankofamerica.debit]]
  payment_method_type = "DinersClub"
[[bankofamerica.debit]]
  payment_method_type = "Discover"
[[bankofamerica.debit]]
  payment_method_type = "CartesBancaires"
[[bankofamerica.debit]]
  payment_method_type = "UnionPay"
[[bankofamerica.wallet]]
  payment_method_type = "apple_pay"
[[bankofamerica.wallet]]
  payment_method_type = "google_pay"
[[bankofamerica.wallet]]
  payment_method_type = "samsung_pay"
[bankofamerica.connector_auth.SignatureKey]
api_key="Key"
key1="Merchant ID"
api_secret="Shared Secret"
[bankofamerica.connector_webhook_details]
merchant_secret="Source verification key"

[[bankofamerica.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[bankofamerica.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[bankofamerica.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector","Hyperswitch"]

[[bankofamerica.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[bankofamerica.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[bankofamerica.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[bankofamerica.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[bankofamerica.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[bankofamerica.connector_wallets_details.samsung_pay]]
name="service_id"
label="Samsung Pay Service Id"
placeholder="Enter Samsung Pay Service Id"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.samsung_pay]]
name="merchant_display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[bankofamerica.connector_wallets_details.samsung_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[bankofamerica.connector_wallets_details.samsung_pay]]
name="allowed_brands"
label="Allowed Brands"
placeholder="Enter Allowed Brands"
required=true
type="MultiSelect"
options=["visa","masterCard","amex","discover"]

[barclaycard]
[[barclaycard.credit]]
  payment_method_type = "Mastercard"
[[barclaycard.credit]]
  payment_method_type = "Visa"
[[barclaycard.credit]]
  payment_method_type = "AmericanExpress"
[[barclaycard.credit]]
  payment_method_type = "JCB"
[[barclaycard.credit]]
  payment_method_type = "Discover"
[[barclaycard.credit]]
  payment_method_type = "Maestro"
[[barclaycard.credit]]
  payment_method_type = "Interac"
[[barclaycard.credit]]
  payment_method_type = "DinersClub"
[[barclaycard.credit]]
  payment_method_type = "CartesBancaires"
[[barclaycard.credit]]
  payment_method_type = "UnionPay"
[[barclaycard.debit]]
  payment_method_type = "Mastercard"
[[barclaycard.debit]]
  payment_method_type = "Visa"
[[barclaycard.debit]]
  payment_method_type = "AmericanExpress"
[[barclaycard.debit]]
  payment_method_type = "JCB"
[[barclaycard.debit]]
  payment_method_type = "Discover"
[[barclaycard.debit]]
  payment_method_type = "Maestro"
[[barclaycard.debit]]
  payment_method_type = "Interac"
[[barclaycard.debit]]
  payment_method_type = "DinersClub"
[[barclaycard.debit]]
  payment_method_type = "CartesBancaires"
[[barclaycard.debit]]
  payment_method_type = "UnionPay"
[barclaycard.connector_auth.SignatureKey]
api_key="Key"
key1="Merchant ID"
api_secret="Shared Secret"

[bitpay]
[[bitpay.crypto]]
  payment_method_type = "crypto_currency"
[bitpay.connector_auth.HeaderKey]
api_key="API Key"
[bitpay.connector_webhook_details]
merchant_secret="Source verification key"

[bluesnap]
[[bluesnap.credit]]
  payment_method_type = "Mastercard"
[[bluesnap.credit]]
  payment_method_type = "Visa"
[[bluesnap.credit]]
  payment_method_type = "Interac"
[[bluesnap.credit]]
  payment_method_type = "AmericanExpress"
[[bluesnap.credit]]
  payment_method_type = "JCB"
[[bluesnap.credit]]
  payment_method_type = "DinersClub"
[[bluesnap.credit]]
  payment_method_type = "Discover"
[[bluesnap.credit]]
  payment_method_type = "CartesBancaires"
[[bluesnap.credit]]
  payment_method_type = "UnionPay"
[[bluesnap.wallet]]
  payment_method_type = "google_pay"
[[bluesnap.wallet]]
  payment_method_type = "apple_pay"
[bluesnap.connector_auth.BodyKey]
api_key="Password"
key1="Username"
[bluesnap.connector_webhook_details]
merchant_secret="Source verification key"

[[bluesnap.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[bluesnap.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[bluesnap.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[bluesnap.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[bluesnap.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[bluesnap.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[bluesnap.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[bluesnap.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[bluesnap.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[bluesnap.metadata.merchant_id]
name="merchant_id"
label="Merchant Id"
placeholder="Enter Merchant Id"
required=false
type="Text"


[boku]
[[boku.wallet]]
  payment_method_type = "dana"
[[boku.wallet]]
  payment_method_type = "gcash"
[[boku.wallet]]
  payment_method_type = "go_pay"
[[boku.wallet]]
  payment_method_type = "kakao_pay"
[[boku.wallet]]
  payment_method_type = "momo"
[boku.connector_auth.BodyKey]
api_key="API KEY"
key1= "MERCHANT ID"
[boku.connector_webhook_details]
merchant_secret="Source verification key"


[braintree]
[[braintree.credit]]
  payment_method_type = "Mastercard"
[[braintree.credit]]
  payment_method_type = "Visa"
[[braintree.credit]]
  payment_method_type = "Interac"
[[braintree.credit]]
  payment_method_type = "AmericanExpress"
[[braintree.credit]]
  payment_method_type = "JCB"
[[braintree.credit]]
  payment_method_type = "DinersClub"
[[braintree.credit]]
  payment_method_type = "Discover"
[[braintree.credit]]
  payment_method_type = "CartesBancaires"
[[braintree.credit]]
  payment_method_type = "UnionPay"
[[braintree.debit]]
  payment_method_type = "Mastercard"
[[braintree.debit]]
  payment_method_type = "Visa"
[[braintree.debit]]
  payment_method_type = "Interac"
[[braintree.debit]]
  payment_method_type = "AmericanExpress"
[[braintree.debit]]
  payment_method_type = "JCB"
[[braintree.debit]]
  payment_method_type = "DinersClub"
[[braintree.debit]]
  payment_method_type = "Discover"
[[braintree.debit]]
  payment_method_type = "CartesBancaires"
[[braintree.debit]]
  payment_method_type = "UnionPay"
[[braintree.debit]]
  payment_method_type = "UnionPay"
[braintree.connector_webhook_details]
merchant_secret="Source verification key"


[braintree.connector_auth.SignatureKey]
api_key="Public Key"
key1="Merchant Id"
api_secret="Private Key"
[braintree.metadata.merchant_account_id]
name="merchant_account_id"
label="Merchant Account Id"
placeholder="Enter Merchant Account Id"
required=true
type="Text"
[braintree.metadata.merchant_config_currency]
name="merchant_config_currency"
label="Currency"
placeholder="Enter Currency"
required=true
type="Select"
options=[]

[cashtocode]
[[cashtocode.reward]]
  payment_method_type = "classic"
[[cashtocode.reward]]
  payment_method_type = "evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.EUR.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.EUR.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.GBP.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.GBP.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.USD.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.USD.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CAD.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CAD.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CHF.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CHF.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.AUD.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.AUD.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.INR.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.INR.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.JPY.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.JPY.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.NZD.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.NZD.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.ZAR.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.ZAR.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CNY.classic]
password_classic="Password Classic"
username_classic="Username Classic"
merchant_id_classic="MerchantId Classic"
[cashtocode.connector_auth.CurrencyAuthKey.auth_key_map.CNY.evoucher]
password_evoucher="Password Evoucher"
username_evoucher="Username Evoucher"
merchant_id_evoucher="MerchantId Evoucher"
[cashtocode.connector_webhook_details]
merchant_secret="Source verification key"

[checkout]
[[checkout.credit]]
  payment_method_type = "Mastercard"
[[checkout.credit]]
  payment_method_type = "Visa"
[[checkout.credit]]
  payment_method_type = "Interac"
[[checkout.credit]]
  payment_method_type = "AmericanExpress"
[[checkout.credit]]
  payment_method_type = "JCB"
[[checkout.credit]]
  payment_method_type = "DinersClub"
[[checkout.credit]]
  payment_method_type = "Discover"
[[checkout.credit]]
  payment_method_type = "CartesBancaires"
[[checkout.credit]]
  payment_method_type = "UnionPay"
[[checkout.debit]]
  payment_method_type = "Mastercard"
[[checkout.debit]]
  payment_method_type = "Visa"
[[checkout.debit]]
  payment_method_type = "Interac"
[[checkout.debit]]
  payment_method_type = "AmericanExpress"
[[checkout.debit]]
  payment_method_type = "JCB"
[[checkout.debit]]
  payment_method_type = "DinersClub"
[[checkout.debit]]
  payment_method_type = "Discover"
[[checkout.debit]]
  payment_method_type = "CartesBancaires"
[[checkout.debit]]
  payment_method_type = "UnionPay"
[[checkout.wallet]]
  payment_method_type = "apple_pay"
[[checkout.wallet]]
  payment_method_type = "google_pay"
[checkout.connector_auth.SignatureKey]
api_key="Checkout API Public Key"
key1="Processing Channel ID"
api_secret="Checkout API Secret Key"
[checkout.connector_webhook_details]
merchant_secret="Source verification key"

[[checkout.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[checkout.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[checkout.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[checkout.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[checkout.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[checkout.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[checkout.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[checkout.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[checkout.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[checkout.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer Bin"
placeholder="Enter Acquirer Bin"
required=false
type="Text"
[checkout.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant ID"
placeholder="Enter Acquirer Merchant ID"
required=false
type="Text"
[checkout.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=false
type="Text"

[coinbase]
[[coinbase.crypto]]
  payment_method_type = "crypto_currency"
[coinbase.connector_auth.HeaderKey]
api_key="API Key"
[coinbase.connector_webhook_details]
merchant_secret="Source verification key"
[coinbase.metadata.pricing_type]
name="pricing_type"
label="Select the pricing type Example: fixed_price,no_price"
placeholder="Select the pricing type Example: fixed_price,no_price"
required=true
type="Select"
options=["fixed_price","no_price"]

[coingate]
[[coingate.crypto]]
  payment_method_type = "crypto_currency"
[coingate.connector_auth.BodyKey]
api_key="API Key"
key1 ="Merchant Token"
[coingate.metadata.currency_id]
name="currency_id"
label="ID of the currency in which the refund will be issued"
placeholder="Enter ID of the currency in which the refund will be issued"
required=true
type="Number"
[coingate.metadata.platform_id]
name="platform_id"
label="Platform ID of the currency in which the refund will be issued"
placeholder="Enter Platform ID of the currency in which the refund will be issued"
required=true
type="Number"
[coingate.metadata.ledger_account_id]
name="ledger_account_id"
label="ID of the trader balance associated with the currency in which the refund will be issued"
placeholder="Enter ID of the trader balance associated with the currency in which the refund will be issued"
required=true
type="Text"

[cryptopay]
[[cryptopay.crypto]]
  payment_method_type = "crypto_currency"
[cryptopay.connector_auth.BodyKey]
api_key="API Key"
key1="Secret Key"
[cryptopay.connector_webhook_details]
merchant_secret="Source verification key"

[cybersource]
[[cybersource.credit]]
  payment_method_type = "Mastercard"
[[cybersource.credit]]
  payment_method_type = "Visa"
[[cybersource.credit]]
  payment_method_type = "Interac"
[[cybersource.credit]]
  payment_method_type = "AmericanExpress"
[[cybersource.credit]]
  payment_method_type = "JCB"
[[cybersource.credit]]
  payment_method_type = "DinersClub"
[[cybersource.credit]]
  payment_method_type = "Discover"
[[cybersource.credit]]
  payment_method_type = "CartesBancaires"
[[cybersource.credit]]
  payment_method_type = "UnionPay"
[[cybersource.debit]]
  payment_method_type = "Mastercard"
[[cybersource.debit]]
  payment_method_type = "Visa"
[[cybersource.debit]]
  payment_method_type = "Interac"
[[cybersource.debit]]
  payment_method_type = "AmericanExpress"
[[cybersource.debit]]
  payment_method_type = "JCB"
[[cybersource.debit]]
  payment_method_type = "DinersClub"
[[cybersource.debit]]
  payment_method_type = "Discover"
[[cybersource.debit]]
  payment_method_type = "CartesBancaires"
[[cybersource.debit]]
  payment_method_type = "UnionPay"
[[cybersource.wallet]]
  payment_method_type = "apple_pay"
[[cybersource.wallet]]
  payment_method_type = "google_pay"
[[cybersource.wallet]]
  payment_method_type = "paze"
[[cybersource.wallet]]
  payment_method_type = "samsung_pay"
[cybersource.connector_auth.SignatureKey]
api_key="Key"
key1="Merchant ID"
api_secret="Shared Secret"
[cybersource.connector_webhook_details]
merchant_secret="Source verification key"
[cybersource.metadata]
disable_avs = "Disable AVS check"
disable_cvn = "Disable CVN check"

[[cybersource.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[cybersource.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[cybersource.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector","Hyperswitch"]

[[cybersource.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[cybersource.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[cybersource.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[cybersource.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[cybersource.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[cybersource.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[cybersource.connector_wallets_details.samsung_pay]]
name="service_id"
label="Samsung Pay Service Id"
placeholder="Enter Samsung Pay Service Id"
required=true
type="Text"
[[cybersource.connector_wallets_details.samsung_pay]]
name="merchant_display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[cybersource.connector_wallets_details.samsung_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[cybersource.connector_wallets_details.samsung_pay]]
name="allowed_brands"
label="Allowed Brands"
placeholder="Enter Allowed Brands"
required=true
type="MultiSelect"
options=["visa","masterCard","amex","discover"]


[cybersource.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer Bin"
placeholder="Enter Acquirer Bin"
required=false
type="Text"
[cybersource.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant ID"
placeholder="Enter Acquirer Merchant ID"
required=false
type="Text"
[cybersource.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=false
type="Text"

[cybersource_payout]
[[cybersource_payout.credit]]
  payment_method_type = "Mastercard"
[[cybersource_payout.credit]]
  payment_method_type = "Visa"
[[cybersource_payout.credit]]
  payment_method_type = "Interac"
[[cybersource_payout.credit]]
  payment_method_type = "AmericanExpress"
[[cybersource_payout.credit]]
  payment_method_type = "JCB"
[[cybersource_payout.credit]]
  payment_method_type = "DinersClub"
[[cybersource_payout.credit]]
  payment_method_type = "Discover"
[[cybersource_payout.credit]]
  payment_method_type = "CartesBancaires"
[[cybersource_payout.credit]]
  payment_method_type = "UnionPay"
[[cybersource_payout.debit]]
  payment_method_type = "Mastercard"
[[cybersource_payout.debit]]
  payment_method_type = "Visa"
[[cybersource_payout.debit]]
  payment_method_type = "Interac"
[[cybersource_payout.debit]]
  payment_method_type = "AmericanExpress"
[[cybersource_payout.debit]]
  payment_method_type = "JCB"
[[cybersource_payout.debit]]
  payment_method_type = "DinersClub"
[[cybersource_payout.debit]]
  payment_method_type = "Discover"
[[cybersource_payout.debit]]
  payment_method_type = "CartesBancaires"
[[cybersource_payout.debit]]
  payment_method_type = "UnionPay"
[cybersource_payout.connector_auth.SignatureKey]
api_key="Key"
key1="Merchant ID"
api_secret="Shared Secret"

[deutschebank]
[[deutschebank.bank_debit]]
  payment_method_type = "sepa"
[[deutschebank.credit]]
  payment_method_type = "Visa"
[[deutschebank.credit]]
  payment_method_type = "Mastercard"
[[deutschebank.debit]]
  payment_method_type = "Visa"
[[deutschebank.debit]]
  payment_method_type = "Mastercard"
[deutschebank.connector_auth.SignatureKey]
api_key="Client ID"
key1="Merchant ID"
api_secret="Client Key"

[digitalvirgo]
[[digitalvirgo.mobile_payment]]
  payment_method_type = "direct_carrier_billing"
[digitalvirgo.connector_auth.BodyKey]
api_key="Password"
key1="Username"

[dlocal]
[[dlocal.credit]]
  payment_method_type = "Mastercard"
[[dlocal.credit]]
  payment_method_type = "Visa"
[[dlocal.credit]]
  payment_method_type = "Interac"
[[dlocal.credit]]
  payment_method_type = "AmericanExpress"
[[dlocal.credit]]
  payment_method_type = "JCB"
[[dlocal.credit]]
  payment_method_type = "DinersClub"
[[dlocal.credit]]
  payment_method_type = "Discover"
[[dlocal.credit]]
  payment_method_type = "CartesBancaires"
[[dlocal.credit]]
  payment_method_type = "UnionPay"
[[dlocal.debit]]
  payment_method_type = "Mastercard"
[[dlocal.debit]]
  payment_method_type = "Visa"
[[dlocal.debit]]
  payment_method_type = "Interac"
[[dlocal.debit]]
  payment_method_type = "AmericanExpress"
[[dlocal.debit]]
  payment_method_type = "JCB"
[[dlocal.debit]]
  payment_method_type = "DinersClub"
[[dlocal.debit]]
  payment_method_type = "Discover"
[[dlocal.debit]]
  payment_method_type = "CartesBancaires"
[[dlocal.debit]]
  payment_method_type = "UnionPay"
[dlocal.connector_auth.SignatureKey]
api_key="X Login"
key1="X Trans Key"
api_secret="Secret Key"
[dlocal.connector_webhook_details]
merchant_secret="Source verification key"

[ebanx_payout]
[[ebanx_payout.bank_transfer]]
  payment_method_type = "pix"
[ebanx_payout.connector_auth.HeaderKey]
api_key = "Integration Key"

[fiserv]
[[fiserv.credit]]
  payment_method_type = "Mastercard"
[[fiserv.credit]]
  payment_method_type = "Visa"
[[fiserv.credit]]
  payment_method_type = "Interac"
[[fiserv.credit]]
  payment_method_type = "AmericanExpress"
[[fiserv.credit]]
  payment_method_type = "JCB"
[[fiserv.credit]]
  payment_method_type = "DinersClub"
[[fiserv.credit]]
  payment_method_type = "Discover"
[[fiserv.credit]]
  payment_method_type = "CartesBancaires"
[[fiserv.credit]]
  payment_method_type = "UnionPay"
[[fiserv.debit]]
  payment_method_type = "Mastercard"
[[fiserv.debit]]
  payment_method_type = "Visa"
[[fiserv.debit]]
  payment_method_type = "Interac"
[[fiserv.debit]]
  payment_method_type = "AmericanExpress"
[[fiserv.debit]]
  payment_method_type = "JCB"
[[fiserv.debit]]
  payment_method_type = "DinersClub"
[[fiserv.debit]]
  payment_method_type = "Discover"
[[fiserv.debit]]
  payment_method_type = "CartesBancaires"
[[fiserv.debit]]
  payment_method_type = "UnionPay"
[fiserv.connector_auth.SignatureKey]
api_key="API Key"
key1="Merchant ID"
api_secret="API Secret"
[fiserv.connector_webhook_details]
merchant_secret="Source verification key"

[fiserv.metadata.terminal_id]
name="terminal_id"
label="Terminal ID"
placeholder="Enter Terminal ID"
required=true
type="Text"

[fiservemea]
[[fiservemea.credit]]
  payment_method_type = "Mastercard"
[[fiservemea.credit]]
  payment_method_type = "Visa"
[[fiservemea.credit]]
  payment_method_type = "Interac"
[[fiservemea.credit]]
  payment_method_type = "AmericanExpress"
[[fiservemea.credit]]
  payment_method_type = "JCB"
[[fiservemea.credit]]
  payment_method_type = "DinersClub"
[[fiservemea.credit]]
  payment_method_type = "Discover"
[[fiservemea.credit]]
  payment_method_type = "CartesBancaires"
[[fiservemea.credit]]
  payment_method_type = "UnionPay"
[[fiservemea.debit]]
  payment_method_type = "Mastercard"
[[fiservemea.debit]]
  payment_method_type = "Visa"
[[fiservemea.debit]]
  payment_method_type = "Interac"
[[fiservemea.debit]]
  payment_method_type = "AmericanExpress"
[[fiservemea.debit]]
  payment_method_type = "JCB"
[[fiservemea.debit]]
  payment_method_type = "DinersClub"
[[fiservemea.debit]]
  payment_method_type = "Discover"
[[fiservemea.debit]]
  payment_method_type = "CartesBancaires"
[[fiservemea.debit]]
  payment_method_type = "UnionPay"
[fiservemea.connector_auth.BodyKey]
api_key="API Key"
key1="Secret Key"

[forte]
[[forte.credit]]
  payment_method_type = "Mastercard"
[[forte.credit]]
  payment_method_type = "Visa"
[[forte.credit]]
  payment_method_type = "Interac"
[[forte.credit]]
  payment_method_type = "AmericanExpress"
[[forte.credit]]
  payment_method_type = "JCB"
[[forte.credit]]
  payment_method_type = "DinersClub"
[[forte.credit]]
  payment_method_type = "Discover"
[[forte.credit]]
  payment_method_type = "CartesBancaires"
[[forte.credit]]
  payment_method_type = "UnionPay"
[[forte.debit]]
  payment_method_type = "Mastercard"
[[forte.debit]]
  payment_method_type = "Visa"
[[forte.debit]]
  payment_method_type = "Interac"
[[forte.debit]]
  payment_method_type = "AmericanExpress"
[[forte.debit]]
  payment_method_type = "JCB"
[[forte.debit]]
  payment_method_type = "DinersClub"
[[forte.debit]]
  payment_method_type = "Discover"
[[forte.debit]]
  payment_method_type = "CartesBancaires"
[[forte.debit]]
  payment_method_type = "UnionPay"
[forte.connector_auth.MultiAuthKey]
api_key="API Access ID"
key1="Organization ID"
api_secret="API Secure Key"
key2="Location ID"
[forte.connector_webhook_details]
merchant_secret="Source verification key"

[getnet]
[[getnet.credit]]
  payment_method_type = "Mastercard"
[[getnet.credit]]
  payment_method_type = "Visa"
[[getnet.credit]]
  payment_method_type = "Interac"
[[getnet.credit]]
  payment_method_type = "AmericanExpress"
[[getnet.credit]]
  payment_method_type = "JCB"
[[getnet.credit]]
  payment_method_type = "DinersClub"
[[getnet.credit]]
  payment_method_type = "Discover"
[[getnet.credit]]
  payment_method_type = "CartesBancaires"
[[getnet.credit]]
  payment_method_type = "UnionPay"
[[getnet.credit]]
  payment_method_type = "RuPay"
[[getnet.credit]]
  payment_method_type = "Maestro"

[globalpay]
[[globalpay.credit]]
  payment_method_type = "Mastercard"
[[globalpay.credit]]
  payment_method_type = "Visa"
[[globalpay.credit]]
  payment_method_type = "Interac"
[[globalpay.credit]]
  payment_method_type = "AmericanExpress"
[[globalpay.credit]]
  payment_method_type = "JCB"
[[globalpay.credit]]
  payment_method_type = "DinersClub"
[[globalpay.credit]]
  payment_method_type = "Discover"
[[globalpay.credit]]
  payment_method_type = "CartesBancaires"
[[globalpay.credit]]
  payment_method_type = "UnionPay"
[[globalpay.debit]]
  payment_method_type = "Mastercard"
[[globalpay.debit]]
  payment_method_type = "Visa"
[[globalpay.debit]]
  payment_method_type = "Interac"
[[globalpay.debit]]
  payment_method_type = "AmericanExpress"
[[globalpay.debit]]
  payment_method_type = "JCB"
[[globalpay.debit]]
  payment_method_type = "DinersClub"
[[globalpay.debit]]
  payment_method_type = "Discover"
[[globalpay.debit]]
  payment_method_type = "CartesBancaires"
[[globalpay.debit]]
  payment_method_type = "UnionPay"
[[globalpay.bank_redirect]]
  payment_method_type = "ideal"
[[globalpay.bank_redirect]]
  payment_method_type = "giropay"
[[globalpay.bank_redirect]]
  payment_method_type = "sofort"
[[globalpay.bank_redirect]]
  payment_method_type = "eps"
[[globalpay.wallet]]
  payment_method_type = "google_pay"
[[globalpay.wallet]]
  payment_method_type = "paypal"
[globalpay.connector_auth.BodyKey]
api_key="Global App Key"
key1="Global App ID"
[globalpay.connector_webhook_details]
merchant_secret="Source verification key"

[[globalpay.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[globalpay.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[globalpay.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[globalpay.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[globalpay.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[globalpay.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[globalpay.metadata.account_name]
name="account_name"
label="Account Name"
placeholder="Enter Account Name"
required=true
type="Text"

[globepay]
[[globepay.wallet]]
  payment_method_type = "we_chat_pay"
[[globepay.wallet]]
  payment_method_type = "ali_pay"
[globepay.connector_auth.BodyKey]
api_key="Partner Code"
key1="Credential Code"
[globepay.connector_webhook_details]
merchant_secret="Source verification key"

[gocardless]
[[gocardless.bank_debit]]
  payment_method_type = "ach"
[[gocardless.bank_debit]]
  payment_method_type = "becs"
[[gocardless.bank_debit]]
  payment_method_type = "sepa"
[gocardless.connector_auth.HeaderKey]
api_key="Access Token"
[gocardless.connector_webhook_details]
merchant_secret="Source verification key"

[iatapay]
[[iatapay.upi]]
  payment_method_type = "upi_collect"
[iatapay.connector_auth.SignatureKey]
api_key="Client ID"
key1="Airline ID"
api_secret="Client Secret"
[iatapay.connector_webhook_details]
merchant_secret="Source verification key"

[itaubank]
[[itaubank.bank_transfer]]
  payment_method_type = "pix"
[itaubank.connector_auth.MultiAuthKey]
key1="Client Id"
api_key="Client Secret"
api_secret="Certificates"
key2="Certificate Key"

[jpmorgan]
[[jpmorgan.credit]]
  payment_method_type = "AmericanExpress"
[[jpmorgan.credit]]
  payment_method_type = "DinersClub"
[[jpmorgan.credit]]
  payment_method_type = "Discover"
[[jpmorgan.credit]]
  payment_method_type = "JCB"
[[jpmorgan.credit]]
  payment_method_type = "Mastercard"
[[jpmorgan.credit]]
  payment_method_type = "Discover"
[[jpmorgan.credit]]
  payment_method_type = "UnionPay"
[[jpmorgan.credit]]
  payment_method_type = "Visa"
  [[jpmorgan.debit]]
  payment_method_type = "AmericanExpress"
[[jpmorgan.debit]]
  payment_method_type = "DinersClub"
[[jpmorgan.debit]]
  payment_method_type = "Discover"
[[jpmorgan.debit]]
  payment_method_type = "JCB"
[[jpmorgan.debit]]
  payment_method_type = "Mastercard"
[[jpmorgan.debit]]
  payment_method_type = "Discover"
[[jpmorgan.debit]]
  payment_method_type = "UnionPay"
[[jpmorgan.debit]]
  payment_method_type = "Visa"
[jpmorgan.connector_auth.BodyKey]
api_key="Access Token"
key1="Client Secret"

[klarna]
[[klarna.pay_later]]
  payment_method_type = "klarna"
  payment_experience = "invoke_sdk_client"
[[klarna.pay_later]]
  payment_method_type = "klarna"
  payment_experience = "redirect_to_url"
[klarna.connector_auth.BodyKey]
key1="Klarna Merchant Username"
api_key="Klarna Merchant ID Password"
[klarna.metadata.klarna_region]
name="klarna_region"
label="Region of your Klarna Merchant Account"
placeholder="Enter Region of your Klarna Merchant Account"
required=true
type="Select"
options=["Europe","NorthAmerica","Oceania"]

[mifinity]
[[mifinity.wallet]]
  payment_method_type = "mifinity"
[mifinity.connector_auth.HeaderKey]
api_key="key"
[mifinity.metadata.brand_id]
name="brand_id"
label="Merchant Brand ID"
placeholder="Enter Brand ID"
required=true
type="Text"
[mifinity.metadata.destination_account_number]
name="destination_account_number"
label="Destination Account Number"
placeholder="Enter Destination Account Number"
required=true
type="Text"

[razorpay]
[[razorpay.upi]]
  payment_method_type = "upi_collect"
[razorpay.connector_auth.BodyKey]
api_key="Razorpay Id"
key1 = "Razorpay Secret"

[mollie]
[[mollie.credit]]
  payment_method_type = "Mastercard"
[[mollie.credit]]
  payment_method_type = "Visa"
[[mollie.credit]]
  payment_method_type = "Interac"
[[mollie.credit]]
  payment_method_type = "AmericanExpress"
[[mollie.credit]]
  payment_method_type = "JCB"
[[mollie.credit]]
  payment_method_type = "DinersClub"
[[mollie.credit]]
  payment_method_type = "Discover"
[[mollie.credit]]
  payment_method_type = "CartesBancaires"
[[mollie.credit]]
  payment_method_type = "UnionPay"
[[mollie.debit]]
  payment_method_type = "Mastercard"
[[mollie.debit]]
  payment_method_type = "Visa"
[[mollie.debit]]
  payment_method_type = "Interac"
[[mollie.debit]]
  payment_method_type = "AmericanExpress"
[[mollie.debit]]
  payment_method_type = "JCB"
[[mollie.debit]]
  payment_method_type = "DinersClub"
[[mollie.debit]]
  payment_method_type = "Discover"
[[mollie.debit]]
  payment_method_type = "CartesBancaires"
[[mollie.debit]]
  payment_method_type = "UnionPay"
[[mollie.bank_redirect]]
  payment_method_type = "ideal"
[[mollie.bank_redirect]]
  payment_method_type = "giropay"
[[mollie.bank_redirect]]
  payment_method_type = "sofort"
[[mollie.bank_redirect]]
  payment_method_type = "eps"
[[mollie.bank_redirect]]
  payment_method_type = "przelewy24"
[[mollie.bank_redirect]]
  payment_method_type = "bancontact_card"
[[mollie.wallet]]
  payment_method_type = "paypal"
[mollie.connector_auth.BodyKey]
api_key="API Key"
key1="Profile Token"
[mollie.connector_webhook_details]
merchant_secret="Source verification key"

[moneris]
[[moneris.credit]]
  payment_method_type = "Mastercard"
[[moneris.credit]]
  payment_method_type = "Visa"
[[moneris.credit]]
  payment_method_type = "Interac"
[[moneris.credit]]
  payment_method_type = "AmericanExpress"
[[moneris.credit]]
  payment_method_type = "JCB"
[[moneris.credit]]
  payment_method_type = "DinersClub"
[[moneris.credit]]
  payment_method_type = "Discover"
[[moneris.credit]]
  payment_method_type = "CartesBancaires"
[[moneris.credit]]
  payment_method_type = "UnionPay"
[[moneris.debit]]
  payment_method_type = "Mastercard"
[[moneris.debit]]
  payment_method_type = "Visa"
[[moneris.debit]]
  payment_method_type = "Interac"
[[moneris.debit]]
  payment_method_type = "AmericanExpress"
[[moneris.debit]]
  payment_method_type = "JCB"
[[moneris.debit]]
  payment_method_type = "DinersClub"
[[moneris.debit]]
  payment_method_type = "Discover"
[[moneris.debit]]
  payment_method_type = "CartesBancaires"
[[moneris.debit]]
  payment_method_type = "UnionPay"
[moneris.connector_auth.SignatureKey]
api_key="Client Secret"
key1="Client Id"
api_secret="Merchant Id"

[multisafepay]
[[multisafepay.credit]]
  payment_method_type = "Mastercard"
[[multisafepay.credit]]
  payment_method_type = "Visa"
[[multisafepay.credit]]
  payment_method_type = "Interac"
[[multisafepay.credit]]
  payment_method_type = "AmericanExpress"
[[multisafepay.credit]]
  payment_method_type = "JCB"
[[multisafepay.credit]]
  payment_method_type = "DinersClub"
[[multisafepay.credit]]
  payment_method_type = "Discover"
[[multisafepay.credit]]
  payment_method_type = "CartesBancaires"
[[multisafepay.credit]]
  payment_method_type = "UnionPay"
[[multisafepay.debit]]
  payment_method_type = "Mastercard"
[[multisafepay.debit]]
  payment_method_type = "Visa"
[[multisafepay.debit]]
  payment_method_type = "Interac"
[[multisafepay.debit]]
  payment_method_type = "AmericanExpress"
[[multisafepay.debit]]
  payment_method_type = "JCB"
[[multisafepay.debit]]
  payment_method_type = "DinersClub"
[[multisafepay.debit]]
  payment_method_type = "Discover"
[[multisafepay.debit]]
  payment_method_type = "CartesBancaires"
[[multisafepay.debit]]
  payment_method_type = "UnionPay"
[[multisafepay.wallet]]
  payment_method_type = "google_pay"
[[multisafepay.wallet]]
  payment_method_type = "paypal"
[multisafepay.connector_auth.HeaderKey]
api_key="Enter API Key"
[multisafepay.connector_webhook_details]
merchant_secret="Source verification key"

[[multisafepay.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[multisafepay.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[multisafepay.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[multisafepay.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[multisafepay.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[multisafepay.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[nexinets]
[[nexinets.credit]]
  payment_method_type = "Mastercard"
[[nexinets.credit]]
  payment_method_type = "Visa"
[[nexinets.credit]]
  payment_method_type = "Interac"
[[nexinets.credit]]
  payment_method_type = "AmericanExpress"
[[nexinets.credit]]
  payment_method_type = "JCB"
[[nexinets.credit]]
  payment_method_type = "DinersClub"
[[nexinets.credit]]
  payment_method_type = "Discover"
[[nexinets.credit]]
  payment_method_type = "CartesBancaires"
[[nexinets.credit]]
  payment_method_type = "UnionPay"
[[nexinets.debit]]
  payment_method_type = "Mastercard"
[[nexinets.debit]]
  payment_method_type = "Visa"
[[nexinets.debit]]
  payment_method_type = "Interac"
[[nexinets.debit]]
  payment_method_type = "AmericanExpress"
[[nexinets.debit]]
  payment_method_type = "JCB"
[[nexinets.debit]]
  payment_method_type = "DinersClub"
[[nexinets.debit]]
  payment_method_type = "Discover"
[[nexinets.debit]]
  payment_method_type = "CartesBancaires"
[[nexinets.debit]]
  payment_method_type = "UnionPay"
[[nexinets.bank_redirect]]
  payment_method_type = "ideal"
[[nexinets.bank_redirect]]
  payment_method_type = "giropay"
[[nexinets.bank_redirect]]
  payment_method_type = "sofort"
[[nexinets.bank_redirect]]
  payment_method_type = "eps"
[[nexinets.wallet]]
  payment_method_type = "apple_pay"
[[nexinets.wallet]]
  payment_method_type = "paypal"
[nexinets.connector_auth.BodyKey]
api_key="API Key"
key1="Merchant ID"
[nexinets.connector_webhook_details]
merchant_secret="Source verification key"

[[nexinets.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[nexinets.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[nexinets.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[nexixpay]
[[nexixpay.credit]]
  payment_method_type = "Mastercard"
[[nexixpay.credit]]
  payment_method_type = "Visa"
[[nexixpay.credit]]
  payment_method_type = "AmericanExpress"
[[nexixpay.credit]]
  payment_method_type = "JCB"
[[nexixpay.debit]]
  payment_method_type = "Mastercard"
[[nexixpay.debit]]
  payment_method_type = "Visa"
[[nexixpay.debit]]
  payment_method_type = "AmericanExpress"
[[nexixpay.debit]]
  payment_method_type = "JCB"
[nexixpay.connector_auth.HeaderKey]
api_key="API Key"

[nmi]
[[nmi.credit]]
  payment_method_type = "Mastercard"
[[nmi.credit]]
  payment_method_type = "Visa"
[[nmi.credit]]
  payment_method_type = "Interac"
[[nmi.credit]]
  payment_method_type = "AmericanExpress"
[[nmi.credit]]
  payment_method_type = "JCB"
[[nmi.credit]]
  payment_method_type = "DinersClub"
[[nmi.credit]]
  payment_method_type = "Discover"
[[nmi.credit]]
  payment_method_type = "CartesBancaires"
[[nmi.credit]]
  payment_method_type = "UnionPay"
[[nmi.debit]]
  payment_method_type = "Mastercard"
[[nmi.debit]]
  payment_method_type = "Visa"
[[nmi.debit]]
  payment_method_type = "Interac"
[[nmi.debit]]
  payment_method_type = "AmericanExpress"
[[nmi.debit]]
  payment_method_type = "JCB"
[[nmi.debit]]
  payment_method_type = "DinersClub"
[[nmi.debit]]
  payment_method_type = "Discover"
[[nmi.debit]]
  payment_method_type = "CartesBancaires"
[[nmi.debit]]
  payment_method_type = "UnionPay"
[[nmi.bank_redirect]]
  payment_method_type = "ideal"
[[nmi.wallet]]
  payment_method_type = "apple_pay"
[[nmi.wallet]]
  payment_method_type = "google_pay"
[nmi.connector_auth.BodyKey]
api_key="API Key"
key1="Public Key"
[nmi.connector_webhook_details]
merchant_secret="Source verification key"

[[nmi.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[nmi.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[nmi.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[nmi.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[nmi.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[nmi.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[nmi.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[nmi.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[nmi.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[nmi.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer Bin"
placeholder="Enter Acquirer Bin"
required=false
type="Text"
[nmi.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant ID"
placeholder="Enter Acquirer Merchant ID"
required=false
type="Text"
[nmi.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=false
type="Text"

[noon]
[[noon.credit]]
  payment_method_type = "Mastercard"
[[noon.credit]]
  payment_method_type = "Visa"
[[noon.credit]]
  payment_method_type = "Interac"
[[noon.credit]]
  payment_method_type = "AmericanExpress"
[[noon.credit]]
  payment_method_type = "JCB"
[[noon.credit]]
  payment_method_type = "DinersClub"
[[noon.credit]]
  payment_method_type = "Discover"
[[noon.credit]]
  payment_method_type = "CartesBancaires"
[[noon.credit]]
  payment_method_type = "UnionPay"
[[noon.debit]]
  payment_method_type = "Mastercard"
[[noon.debit]]
  payment_method_type = "Visa"
[[noon.debit]]
  payment_method_type = "Interac"
[[noon.debit]]
  payment_method_type = "AmericanExpress"
[[noon.debit]]
  payment_method_type = "JCB"
[[noon.debit]]
  payment_method_type = "DinersClub"
[[noon.debit]]
  payment_method_type = "Discover"
[[noon.debit]]
  payment_method_type = "CartesBancaires"
[[noon.debit]]
  payment_method_type = "UnionPay"
[[noon.wallet]]
  payment_method_type = "apple_pay"
[[noon.wallet]]
  payment_method_type = "google_pay"
[[noon.wallet]]
  payment_method_type = "paypal"
[noon.connector_auth.SignatureKey]
api_key="API Key"
key1="Business Identifier"
api_secret="Application Identifier"
[noon.connector_webhook_details]
merchant_secret="Source verification key"

[[noon.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[noon.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[noon.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[noon.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[noon.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[noon.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[noon.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[noon.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[noon.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[novalnet]
[[novalnet.credit]]
  payment_method_type = "Mastercard"
[[novalnet.credit]]
  payment_method_type = "Visa"
[[novalnet.credit]]
  payment_method_type = "Interac"
[[novalnet.credit]]
  payment_method_type = "AmericanExpress"
[[novalnet.credit]]
  payment_method_type = "JCB"
[[novalnet.credit]]
  payment_method_type = "DinersClub"
[[novalnet.credit]]
  payment_method_type = "Discover"
[[novalnet.credit]]
  payment_method_type = "CartesBancaires"
[[novalnet.credit]]
  payment_method_type = "UnionPay"
[[novalnet.debit]]
  payment_method_type = "Mastercard"
[[novalnet.debit]]
  payment_method_type = "Visa"
[[novalnet.debit]]
  payment_method_type = "Interac"
[[novalnet.debit]]
  payment_method_type = "AmericanExpress"
[[novalnet.debit]]
  payment_method_type = "JCB"
[[novalnet.debit]]
  payment_method_type = "DinersClub"
[[novalnet.debit]]
  payment_method_type = "Discover"
[[novalnet.debit]]
  payment_method_type = "CartesBancaires"
[[novalnet.debit]]
  payment_method_type = "UnionPay"
[[novalnet.wallet]]
  payment_method_type = "google_pay"
[[novalnet.wallet]]
  payment_method_type = "paypal"
[[novalnet.wallet]]
  payment_method_type = "apple_pay"
[novalnet.connector_auth.SignatureKey]
api_key="Product Activation Key"
key1="Payment Access Key"
api_secret="Tariff ID"
[novalnet.connector_webhook_details]
merchant_secret="Source verification key"

[[novalnet.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[novalnet.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[novalnet.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[novalnet.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[novalnet.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[novalnet.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[[novalnet.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[novalnet.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[novalnet.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[novalnet.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[novalnet.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Select"
options=["web","ios"]
[[novalnet.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[novalnet.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[novalnet.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[nuvei]
[[nuvei.credit]]
  payment_method_type = "Mastercard"
[[nuvei.credit]]
  payment_method_type = "Visa"
[[nuvei.credit]]
  payment_method_type = "Interac"
[[nuvei.credit]]
  payment_method_type = "AmericanExpress"
[[nuvei.credit]]
  payment_method_type = "JCB"
[[nuvei.credit]]
  payment_method_type = "DinersClub"
[[nuvei.credit]]
  payment_method_type = "Discover"
[[nuvei.credit]]
  payment_method_type = "CartesBancaires"
[[nuvei.credit]]
  payment_method_type = "UnionPay"
[[nuvei.debit]]
  payment_method_type = "Mastercard"
[[nuvei.debit]]
  payment_method_type = "Visa"
[[nuvei.debit]]
  payment_method_type = "Interac"
[[nuvei.debit]]
  payment_method_type = "AmericanExpress"
[[nuvei.debit]]
  payment_method_type = "JCB"
[[nuvei.debit]]
  payment_method_type = "DinersClub"
[[nuvei.debit]]
  payment_method_type = "Discover"
[[nuvei.debit]]
  payment_method_type = "CartesBancaires"
[[nuvei.debit]]
  payment_method_type = "UnionPay"
[[nuvei.pay_later]]
  payment_method_type = "klarna"
[[nuvei.pay_later]]
  payment_method_type = "afterpay_clearpay"
[[nuvei.bank_redirect]]
  payment_method_type = "ideal"
[[nuvei.bank_redirect]]
  payment_method_type = "giropay"
[[nuvei.bank_redirect]]
  payment_method_type = "sofort"
[[nuvei.bank_redirect]]
  payment_method_type = "eps"
[[nuvei.wallet]]
  payment_method_type = "apple_pay"
[[nuvei.wallet]]
  payment_method_type = "google_pay"
[[nuvei.wallet]]
  payment_method_type = "paypal"
[nuvei.connector_auth.SignatureKey]
api_key="Merchant ID"
key1="Merchant Site ID"
api_secret="Merchant Secret"
[nuvei.connector_webhook_details]
merchant_secret="Source verification key"

[[nuvei.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[nuvei.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[nuvei.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[nuvei.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[nuvei.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[nuvei.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[nuvei.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[nuvei.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[nuvei.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]



[opennode]
[[opennode.crypto]]
  payment_method_type = "crypto_currency"
[opennode.connector_auth.HeaderKey]
api_key="API Key"
[opennode.connector_webhook_details]
merchant_secret="Source verification key"

[prophetpay]
[[prophetpay.card_redirect]]
  payment_method_type = "card_redirect"
[prophetpay.connector_auth.SignatureKey]
api_key="Username"
key1="Token"
api_secret="Profile"

[payme]
[[payme.credit]]
  payment_method_type = "Mastercard"
[[payme.credit]]
  payment_method_type = "Visa"
[[payme.credit]]
  payment_method_type = "Interac"
[[payme.credit]]
  payment_method_type = "AmericanExpress"
[[payme.credit]]
  payment_method_type = "JCB"
[[payme.credit]]
  payment_method_type = "DinersClub"
[[payme.credit]]
  payment_method_type = "Discover"
[[payme.credit]]
  payment_method_type = "CartesBancaires"
[[payme.credit]]
  payment_method_type = "UnionPay"
[[payme.debit]]
  payment_method_type = "Mastercard"
[[payme.debit]]
  payment_method_type = "Visa"
[[payme.debit]]
  payment_method_type = "Interac"
[[payme.debit]]
  payment_method_type = "AmericanExpress"
[[payme.debit]]
  payment_method_type = "JCB"
[[payme.debit]]
  payment_method_type = "DinersClub"
[[payme.debit]]
  payment_method_type = "Discover"
[[payme.debit]]
  payment_method_type = "CartesBancaires"
[[payme.debit]]
  payment_method_type = "UnionPay"
[payme.connector_auth.BodyKey]
api_key="Seller Payme Id"
key1="Payme Public Key"
[payme.connector_webhook_details]
merchant_secret="Payme Client Secret"
additional_secret="Payme Client Key"

[paypal]
[[paypal.credit]]
  payment_method_type = "Mastercard"
[[paypal.credit]]
  payment_method_type = "Visa"
[[paypal.credit]]
  payment_method_type = "Interac"
[[paypal.credit]]
  payment_method_type = "AmericanExpress"
[[paypal.credit]]
  payment_method_type = "JCB"
[[paypal.credit]]
  payment_method_type = "DinersClub"
[[paypal.credit]]
  payment_method_type = "Discover"
[[paypal.credit]]
  payment_method_type = "CartesBancaires"
[[paypal.credit]]
  payment_method_type = "UnionPay"
[[paypal.debit]]
  payment_method_type = "Mastercard"
[[paypal.debit]]
  payment_method_type = "Visa"
[[paypal.debit]]
  payment_method_type = "Interac"
[[paypal.debit]]
  payment_method_type = "AmericanExpress"
[[paypal.debit]]
  payment_method_type = "JCB"
[[paypal.debit]]
  payment_method_type = "DinersClub"
[[paypal.debit]]
  payment_method_type = "Discover"
[[paypal.debit]]
  payment_method_type = "CartesBancaires"
[[paypal.debit]]
  payment_method_type = "UnionPay"
[[paypal.wallet]]
  payment_method_type = "paypal"
  payment_experience = "invoke_sdk_client"
[[paypal.wallet]]
  payment_method_type = "paypal"
  payment_experience = "redirect_to_url"
[[paypal.bank_redirect]]
  payment_method_type = "ideal"
[[paypal.bank_redirect]]
  payment_method_type = "giropay"
[[paypal.bank_redirect]]
  payment_method_type = "sofort"
[[paypal.bank_redirect]]
  payment_method_type = "eps"
is_verifiable = true
[paypal.connector_auth.BodyKey]
api_key="Client Secret"
key1="Client ID"
[paypal.connector_webhook_details]
merchant_secret="Source verification key"
[paypal.metadata.paypal_sdk]
client_id="Client ID"

[paypal_payout]
[[paypal_payout.wallet]]
  payment_method_type = "paypal"
[[paypal_payout.wallet]]
  payment_method_type = "venmo"
[paypal_payout.connector_auth.BodyKey]
api_key="Client Secret"
key1="Client ID"

[payu]
[[payu.credit]]
  payment_method_type = "Mastercard"
[[payu.credit]]
  payment_method_type = "Visa"
[[payu.credit]]
  payment_method_type = "Interac"
[[payu.credit]]
  payment_method_type = "AmericanExpress"
[[payu.credit]]
  payment_method_type = "JCB"
[[payu.credit]]
  payment_method_type = "DinersClub"
[[payu.credit]]
  payment_method_type = "Discover"
[[payu.credit]]
  payment_method_type = "CartesBancaires"
[[payu.credit]]
  payment_method_type = "UnionPay"
[[payu.debit]]
  payment_method_type = "Mastercard"
[[payu.debit]]
  payment_method_type = "Visa"
[[payu.debit]]
  payment_method_type = "Interac"
[[payu.debit]]
  payment_method_type = "AmericanExpress"
[[payu.debit]]
  payment_method_type = "JCB"
[[payu.debit]]
  payment_method_type = "DinersClub"
[[payu.debit]]
  payment_method_type = "Discover"
[[payu.debit]]
  payment_method_type = "CartesBancaires"
[[payu.debit]]
  payment_method_type = "UnionPay"
[[payu.wallet]]
  payment_method_type = "google_pay"
[payu.connector_auth.BodyKey]
api_key="API Key"
key1="Merchant POS ID"
[payu.connector_webhook_details]
merchant_secret="Source verification key"

[[payu.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[payu.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[payu.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[payu.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[payu.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[payu.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[placetopay]
[[placetopay.credit]]
  payment_method_type = "Mastercard"
[[placetopay.credit]]
  payment_method_type = "Visa"
[[placetopay.credit]]
  payment_method_type = "Interac"
[[placetopay.credit]]
  payment_method_type = "AmericanExpress"
[[placetopay.credit]]
  payment_method_type = "JCB"
[[placetopay.credit]]
  payment_method_type = "DinersClub"
[[placetopay.credit]]
  payment_method_type = "Discover"
[[placetopay.credit]]
  payment_method_type = "CartesBancaires"
[[placetopay.credit]]
  payment_method_type = "UnionPay"
[[placetopay.debit]]
  payment_method_type = "Mastercard"
[[placetopay.debit]]
  payment_method_type = "Visa"
[[placetopay.debit]]
  payment_method_type = "Interac"
[[placetopay.debit]]
  payment_method_type = "AmericanExpress"
[[placetopay.debit]]
  payment_method_type = "JCB"
[[placetopay.debit]]
  payment_method_type = "DinersClub"
[[placetopay.debit]]
  payment_method_type = "Discover"
[[placetopay.debit]]
  payment_method_type = "CartesBancaires"
[[placetopay.debit]]
  payment_method_type = "UnionPay"
[placetopay.connector_auth.BodyKey]
api_key="Login"
key1="Trankey"

[plaid]
[[plaid.open_banking]]
  payment_method_type = "open_banking_pis"
[plaid.connector_auth.BodyKey]
api_key="client_id"
key1="secret"
[plaid.additional_merchant_data.open_banking_recipient_data]
name="open_banking_recipient_data"
label="Open Banking Recipient Data"
placeholder="Enter Open Banking Recipient Data"
required=true
type="Select"
options=["account_data","connector_recipient_id","wallet_id"]
[plaid.additional_merchant_data.account_data]
name="account_data"
label="Account Data"
placeholder="Enter account_data"
required=true
type="Select"
options=["iban","bacs"]
[plaid.additional_merchant_data.connector_recipient_id]
name="connector_recipient_id"
label="Connector Recipient Id"
placeholder="Enter connector recipient id"
required=true
type="Text"
[plaid.additional_merchant_data.wallet_id]
name="wallet_id"
label="Wallet Id"
placeholder="Enter wallet id"
required=true
type="Text"

[[plaid.additional_merchant_data.iban]]
name="iban"
label="Iban"
placeholder="Enter iban"
required=true
type="Text"
[[plaid.additional_merchant_data.iban]]
name="iban.name"
label="Name"
placeholder="Enter name"
required=true
type="Text"

[[plaid.additional_merchant_data.bacs]]
name="sort_code"
label="Sort Code"
placeholder="Enter sort code"
required=true
type="Text"
[[plaid.additional_merchant_data.bacs]]
name="account_number"
label="Bank scheme"
placeholder="Enter account number"
required=true
type="Text"
[[plaid.additional_merchant_data.bacs]]
name="bacs.name"
label="Name"
placeholder="Enter name"
required=true
type="Text"

[powertranz]
[[powertranz.credit]]
  payment_method_type = "Mastercard"
[[powertranz.credit]]
  payment_method_type = "Visa"
[[powertranz.credit]]
  payment_method_type = "Interac"
[[powertranz.credit]]
  payment_method_type = "AmericanExpress"
[[powertranz.credit]]
  payment_method_type = "JCB"
[[powertranz.credit]]
  payment_method_type = "DinersClub"
[[powertranz.credit]]
  payment_method_type = "Discover"
[[powertranz.credit]]
  payment_method_type = "CartesBancaires"
[[powertranz.credit]]
  payment_method_type = "UnionPay"
[[powertranz.debit]]
  payment_method_type = "Mastercard"
[[powertranz.debit]]
  payment_method_type = "Visa"
[[powertranz.debit]]
  payment_method_type = "Interac"
[[powertranz.debit]]
  payment_method_type = "AmericanExpress"
[[powertranz.debit]]
  payment_method_type = "JCB"
[[powertranz.debit]]
  payment_method_type = "DinersClub"
[[powertranz.debit]]
  payment_method_type = "Discover"
[[powertranz.debit]]
  payment_method_type = "CartesBancaires"
[[powertranz.debit]]
  payment_method_type = "UnionPay"
[powertranz.connector_auth.BodyKey]
key1 = "PowerTranz Id"
api_key="PowerTranz Password"
[powertranz.connector_webhook_details]
merchant_secret="Source verification key"

[rapyd]
[[rapyd.credit]]
  payment_method_type = "Mastercard"
[[rapyd.credit]]
  payment_method_type = "Visa"
[[rapyd.credit]]
  payment_method_type = "Interac"
[[rapyd.credit]]
  payment_method_type = "AmericanExpress"
[[rapyd.credit]]
  payment_method_type = "JCB"
[[rapyd.credit]]
  payment_method_type = "DinersClub"
[[rapyd.credit]]
  payment_method_type = "Discover"
[[rapyd.credit]]
  payment_method_type = "CartesBancaires"
[[rapyd.credit]]
  payment_method_type = "UnionPay"
[[rapyd.debit]]
  payment_method_type = "Mastercard"
[[rapyd.debit]]
  payment_method_type = "Visa"
[[rapyd.debit]]
  payment_method_type = "Interac"
[[rapyd.debit]]
  payment_method_type = "AmericanExpress"
[[rapyd.debit]]
  payment_method_type = "JCB"
[[rapyd.debit]]
  payment_method_type = "DinersClub"
[[rapyd.debit]]
  payment_method_type = "Discover"
[[rapyd.debit]]
  payment_method_type = "CartesBancaires"
[[rapyd.debit]]
  payment_method_type = "UnionPay"
[[rapyd.wallet]]
  payment_method_type = "apple_pay"
[rapyd.connector_auth.BodyKey]
api_key="Access Key"
key1="API Secret"
[rapyd.connector_webhook_details]
merchant_secret="Source verification key"

[[rapyd.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[rapyd.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[rapyd.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[shift4]
[[shift4.credit]]
  payment_method_type = "Mastercard"
[[shift4.credit]]
  payment_method_type = "Visa"
[[shift4.credit]]
  payment_method_type = "Interac"
[[shift4.credit]]
  payment_method_type = "AmericanExpress"
[[shift4.credit]]
  payment_method_type = "JCB"
[[shift4.credit]]
  payment_method_type = "DinersClub"
[[shift4.credit]]
  payment_method_type = "Discover"
[[shift4.credit]]
  payment_method_type = "CartesBancaires"
[[shift4.credit]]
  payment_method_type = "UnionPay"
[[shift4.debit]]
  payment_method_type = "Mastercard"
[[shift4.debit]]
  payment_method_type = "Visa"
[[shift4.debit]]
  payment_method_type = "Interac"
[[shift4.debit]]
  payment_method_type = "AmericanExpress"
[[shift4.debit]]
  payment_method_type = "JCB"
[[shift4.debit]]
  payment_method_type = "DinersClub"
[[shift4.debit]]
  payment_method_type = "Discover"
[[shift4.debit]]
  payment_method_type = "CartesBancaires"
[[shift4.debit]]
  payment_method_type = "UnionPay"
[[shift4.bank_redirect]]
  payment_method_type = "ideal"
[[shift4.bank_redirect]]
  payment_method_type = "giropay"
[[shift4.bank_redirect]]
  payment_method_type = "sofort"
[[shift4.bank_redirect]]
  payment_method_type = "eps"
[shift4.connector_auth.HeaderKey]
api_key="API Key"
[shift4.connector_webhook_details]
merchant_secret="Source verification key"

[stripe]
[[stripe.credit]]
  payment_method_type = "Mastercard"
[[stripe.credit]]
  payment_method_type = "Visa"
[[stripe.credit]]
  payment_method_type = "Interac"
[[stripe.credit]]
  payment_method_type = "AmericanExpress"
[[stripe.credit]]
  payment_method_type = "JCB"
[[stripe.credit]]
  payment_method_type = "DinersClub"
[[stripe.credit]]
  payment_method_type = "Discover"
[[stripe.credit]]
  payment_method_type = "CartesBancaires"
[[stripe.credit]]
  payment_method_type = "UnionPay"
[[stripe.debit]]
  payment_method_type = "Mastercard"
[[stripe.debit]]
  payment_method_type = "Visa"
[[stripe.debit]]
  payment_method_type = "Interac"
[[stripe.debit]]
  payment_method_type = "AmericanExpress"
[[stripe.debit]]
  payment_method_type = "JCB"
[[stripe.debit]]
  payment_method_type = "DinersClub"
[[stripe.debit]]
  payment_method_type = "Discover"
[[stripe.debit]]
  payment_method_type = "CartesBancaires"
[[stripe.debit]]
  payment_method_type = "UnionPay"
[[stripe.pay_later]]
  payment_method_type = "klarna"
[[stripe.pay_later]]
  payment_method_type = "affirm"
[[stripe.pay_later]]
  payment_method_type = "afterpay_clearpay"
[[stripe.bank_redirect]]
  payment_method_type = "ideal"
[[stripe.bank_redirect]]
  payment_method_type = "giropay"
[[stripe.bank_redirect]]
  payment_method_type = "eps"
[[stripe.bank_redirect]]
  payment_method_type = "bancontact_card"
[[stripe.bank_redirect]]
  payment_method_type = "przelewy24"
[[stripe.bank_debit]]
  payment_method_type = "ach"
[[stripe.bank_debit]]
  payment_method_type = "bacs"
[[stripe.bank_debit]]
  payment_method_type = "becs"
[[stripe.bank_debit]]
  payment_method_type = "sepa"
[[stripe.bank_transfer]]
  payment_method_type = "ach"
[[stripe.bank_transfer]]
  payment_method_type = "bacs"
[[stripe.bank_transfer]]
  payment_method_type = "sepa"
[[stripe.bank_transfer]]
  payment_method_type = "multibanco"
[[stripe.wallet]]
  payment_method_type = "amazon_pay"
[[stripe.wallet]]
  payment_method_type = "apple_pay"
[[stripe.wallet]]
  payment_method_type = "google_pay"
[[stripe.wallet]]
  payment_method_type = "we_chat_pay"
[[stripe.wallet]]
  payment_method_type = "ali_pay"
[[stripe.wallet]]
  payment_method_type = "cashapp"
[[stripe.wallet]]
  payment_method_type = "revolut_pay"
is_verifiable = true
[stripe.connector_auth.HeaderKey]
api_key="Secret Key"
[stripe.connector_webhook_details]
merchant_secret="Source verification key"

[[stripe.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[stripe.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[stripe.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector","Hyperswitch"]

[[stripe.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[stripe.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[stripe.metadata.google_pay]]
name="stripe:publishableKey"
label="Stripe Publishable Key"
placeholder="Enter Stripe Publishable Key"
required=true
type="Text"
[[stripe.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[stripe.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="stripe:publishableKey"
label="Stripe Publishable Key"
placeholder="Enter Stripe Publishable Key"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[stripe.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[stax]
[[stax.credit]]
  payment_method_type = "Mastercard"
[[stax.credit]]
  payment_method_type = "Visa"
[[stax.credit]]
  payment_method_type = "Interac"
[[stax.credit]]
  payment_method_type = "AmericanExpress"
[[stax.credit]]
  payment_method_type = "JCB"
[[stax.credit]]
  payment_method_type = "DinersClub"
[[stax.credit]]
  payment_method_type = "Discover"
[[stax.credit]]
  payment_method_type = "CartesBancaires"
[[stax.credit]]
  payment_method_type = "UnionPay"
[[stax.debit]]
  payment_method_type = "Mastercard"
[[stax.debit]]
  payment_method_type = "Visa"
[[stax.debit]]
  payment_method_type = "Interac"
[[stax.debit]]
  payment_method_type = "AmericanExpress"
[[stax.debit]]
  payment_method_type = "JCB"
[[stax.debit]]
  payment_method_type = "DinersClub"
[[stax.debit]]
  payment_method_type = "Discover"
[[stax.debit]]
  payment_method_type = "CartesBancaires"
[[stax.debit]]
  payment_method_type = "UnionPay"
[[stax.bank_debit]]
  payment_method_type = "ach"
[stax.connector_auth.HeaderKey]
api_key="Api Key"
[stax.connector_webhook_details]
merchant_secret="Source verification key"

[square]
[[square.credit]]
  payment_method_type = "Mastercard"
[[square.credit]]
  payment_method_type = "Visa"
[[square.credit]]
  payment_method_type = "Interac"
[[square.credit]]
  payment_method_type = "AmericanExpress"
[[square.credit]]
  payment_method_type = "JCB"
[[square.credit]]
  payment_method_type = "DinersClub"
[[square.credit]]
  payment_method_type = "Discover"
[[square.credit]]
  payment_method_type = "CartesBancaires"
[[square.credit]]
  payment_method_type = "UnionPay"
[[square.debit]]
  payment_method_type = "Mastercard"
[[square.debit]]
  payment_method_type = "Visa"
[[square.debit]]
  payment_method_type = "Interac"
[[square.debit]]
  payment_method_type = "AmericanExpress"
[[square.debit]]
  payment_method_type = "JCB"
[[square.debit]]
  payment_method_type = "DinersClub"
[[square.debit]]
  payment_method_type = "Discover"
[[square.debit]]
  payment_method_type = "CartesBancaires"
[[square.debit]]
  payment_method_type = "UnionPay"
[square.connector_auth.BodyKey]
api_key = "Square API Key"
key1 = "Square Client Id"
[square.connector_webhook_details]
merchant_secret="Source verification key"

[trustpay]
[[trustpay.credit]]
  payment_method_type = "Mastercard"
[[trustpay.credit]]
  payment_method_type = "Visa"
[[trustpay.credit]]
  payment_method_type = "Interac"
[[trustpay.credit]]
  payment_method_type = "AmericanExpress"
[[trustpay.credit]]
  payment_method_type = "JCB"
[[trustpay.credit]]
  payment_method_type = "DinersClub"
[[trustpay.credit]]
  payment_method_type = "Discover"
[[trustpay.credit]]
  payment_method_type = "CartesBancaires"
[[trustpay.credit]]
  payment_method_type = "UnionPay"
[[trustpay.debit]]
  payment_method_type = "Mastercard"
[[trustpay.debit]]
  payment_method_type = "Visa"
[[trustpay.debit]]
  payment_method_type = "Interac"
[[trustpay.debit]]
  payment_method_type = "AmericanExpress"
[[trustpay.debit]]
  payment_method_type = "JCB"
[[trustpay.debit]]
  payment_method_type = "DinersClub"
[[trustpay.debit]]
  payment_method_type = "Discover"
[[trustpay.debit]]
  payment_method_type = "CartesBancaires"
[[trustpay.debit]]
  payment_method_type = "UnionPay"
[[trustpay.bank_redirect]]
  payment_method_type = "ideal"
[[trustpay.bank_redirect]]
  payment_method_type = "giropay"
[[trustpay.bank_redirect]]
  payment_method_type = "sofort"
[[trustpay.bank_redirect]]
  payment_method_type = "eps"
[[trustpay.bank_redirect]]
  payment_method_type = "blik"
[[trustpay.wallet]]
  payment_method_type = "apple_pay"
[[trustpay.wallet]]
  payment_method_type = "google_pay"
[[trustpay.bank_transfer]]
  payment_method_type = "sepa_bank_transfer"
[[trustpay.bank_transfer]]
  payment_method_type = "instant_bank_transfer"
[[trustpay.bank_transfer]]
  payment_method_type = "instant_bank_transfer_finland"
[[trustpay.bank_transfer]]
  payment_method_type = "instant_bank_transfer_poland"
[trustpay.connector_auth.SignatureKey]
api_key="API Key"
key1="Project ID"
api_secret="Secret Key"
[trustpay.connector_webhook_details]
merchant_secret="Source verification key"

[[trustpay.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[trustpay.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[trustpay.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[trustpay.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[trustpay.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[trustpay.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[trustpay.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[trustpay.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[trustpay.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[tsys]
[[tsys.credit]]
  payment_method_type = "Mastercard"
[[tsys.credit]]
  payment_method_type = "Visa"
[[tsys.credit]]
  payment_method_type = "Interac"
[[tsys.credit]]
  payment_method_type = "AmericanExpress"
[[tsys.credit]]
  payment_method_type = "JCB"
[[tsys.credit]]
  payment_method_type = "DinersClub"
[[tsys.credit]]
  payment_method_type = "Discover"
[[tsys.credit]]
  payment_method_type = "CartesBancaires"
[[tsys.credit]]
  payment_method_type = "UnionPay"
[[tsys.debit]]
  payment_method_type = "Mastercard"
[[tsys.debit]]
  payment_method_type = "Visa"
[[tsys.debit]]
  payment_method_type = "Interac"
[[tsys.debit]]
  payment_method_type = "AmericanExpress"
[[tsys.debit]]
  payment_method_type = "JCB"
[[tsys.debit]]
  payment_method_type = "DinersClub"
[[tsys.debit]]
  payment_method_type = "Discover"
[[tsys.debit]]
  payment_method_type = "CartesBancaires"
[[tsys.debit]]
  payment_method_type = "UnionPay"
[tsys.connector_auth.SignatureKey]
api_key="Device Id"
key1="Transaction Key"
api_secret="Developer Id"
[tsys.connector_webhook_details]
merchant_secret="Source verification key"

[volt]
[[volt.bank_redirect]]
  payment_method_type = "open_banking_uk"
[volt.connector_auth.MultiAuthKey]
api_key = "Username"
api_secret = "Password"
key1 = "Client ID"
key2 = "Client Secret"
[volt.connector_webhook_details]
merchant_secret="Source verification key"

[worldline]
[[worldline.credit]]
  payment_method_type = "Mastercard"
[[worldline.credit]]
  payment_method_type = "Visa"
[[worldline.credit]]
  payment_method_type = "Interac"
[[worldline.credit]]
  payment_method_type = "AmericanExpress"
[[worldline.credit]]
  payment_method_type = "JCB"
[[worldline.credit]]
  payment_method_type = "DinersClub"
[[worldline.credit]]
  payment_method_type = "Discover"
[[worldline.credit]]
  payment_method_type = "CartesBancaires"
[[worldline.credit]]
  payment_method_type = "UnionPay"
[[worldline.debit]]
  payment_method_type = "Mastercard"
[[worldline.debit]]
  payment_method_type = "Visa"
[[worldline.debit]]
  payment_method_type = "Interac"
[[worldline.debit]]
  payment_method_type = "AmericanExpress"
[[worldline.debit]]
  payment_method_type = "JCB"
[[worldline.debit]]
  payment_method_type = "DinersClub"
[[worldline.debit]]
  payment_method_type = "Discover"
[[worldline.debit]]
  payment_method_type = "CartesBancaires"
[[worldline.debit]]
  payment_method_type = "UnionPay"
[[worldline.bank_redirect]]
  payment_method_type = "ideal"
[[worldline.bank_redirect]]
  payment_method_type = "giropay"
[worldline.connector_auth.SignatureKey]
api_key="API Key ID"
key1="Merchant ID"
api_secret="Secret API Key"
[worldline.connector_webhook_details]
merchant_secret="Source verification key"

[worldpay]
[[worldpay.credit]]
  payment_method_type = "Mastercard"
[[worldpay.credit]]
  payment_method_type = "Visa"
[[worldpay.credit]]
  payment_method_type = "Interac"
[[worldpay.credit]]
  payment_method_type = "AmericanExpress"
[[worldpay.credit]]
  payment_method_type = "JCB"
[[worldpay.credit]]
  payment_method_type = "DinersClub"
[[worldpay.credit]]
  payment_method_type = "Discover"
[[worldpay.credit]]
  payment_method_type = "CartesBancaires"
[[worldpay.credit]]
  payment_method_type = "UnionPay"
[[worldpay.debit]]
  payment_method_type = "Mastercard"
[[worldpay.debit]]
  payment_method_type = "Visa"
[[worldpay.debit]]
  payment_method_type = "Interac"
[[worldpay.debit]]
  payment_method_type = "AmericanExpress"
[[worldpay.debit]]
  payment_method_type = "JCB"
[[worldpay.debit]]
  payment_method_type = "DinersClub"
[[worldpay.debit]]
  payment_method_type = "Discover"
[[worldpay.debit]]
  payment_method_type = "CartesBancaires"
[[worldpay.debit]]
  payment_method_type = "UnionPay"
[[worldpay.wallet]]
  payment_method_type = "google_pay"
[[worldpay.wallet]]
  payment_method_type = "apple_pay"
[worldpay.connector_auth.SignatureKey]
key1="Username"
api_key="Password"
api_secret="Merchant Identifier"
[worldpay.connector_webhook_details]
merchant_secret="Source verification key"
[worldpay.metadata.merchant_name]
name="merchant_name"
label="Name of the merchant to de displayed during 3DS challenge"
placeholder="Enter Name of the merchant"
required=true
type="Text"

[[worldpay.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[worldpay.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[worldpay.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Connector"]

[[worldpay.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[worldpay.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[worldpay.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[worldpay.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[worldpay.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[worldpay.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[zen]
[[zen.credit]]
  payment_method_type = "Mastercard"
[[zen.credit]]
  payment_method_type = "Visa"
[[zen.credit]]
  payment_method_type = "Interac"
[[zen.credit]]
  payment_method_type = "AmericanExpress"
[[zen.credit]]
  payment_method_type = "JCB"
[[zen.credit]]
  payment_method_type = "DinersClub"
[[zen.credit]]
  payment_method_type = "Discover"
[[zen.credit]]
  payment_method_type = "CartesBancaires"
[[zen.credit]]
  payment_method_type = "UnionPay"
[[zen.debit]]
  payment_method_type = "Mastercard"
[[zen.debit]]
  payment_method_type = "Visa"
[[zen.debit]]
  payment_method_type = "Interac"
[[zen.debit]]
  payment_method_type = "AmericanExpress"
[[zen.debit]]
  payment_method_type = "JCB"
[[zen.debit]]
  payment_method_type = "DinersClub"
[[zen.debit]]
  payment_method_type = "Discover"
[[zen.debit]]
  payment_method_type = "CartesBancaires"
[[zen.debit]]
  payment_method_type = "UnionPay"
[[zen.voucher]]
  payment_method_type = "boleto"
[[zen.voucher]]
  payment_method_type = "efecty"
[[zen.voucher]]
  payment_method_type = "pago_efectivo"
[[zen.voucher]]
  payment_method_type = "red_compra"
[[zen.voucher]]
  payment_method_type = "red_pagos"
[[zen.bank_transfer]]
  payment_method_type = "pix"
[[zen.bank_transfer]]
  payment_method_type = "pse"
[[zen.wallet]]
  payment_method_type = "apple_pay"
[[zen.wallet]]
  payment_method_type = "google_pay"
[zen.connector_auth.HeaderKey]
api_key="API Key"
[zen.connector_webhook_details]
merchant_secret="Source verification key"

[[zen.metadata.apple_pay]]
name="terminal_uuid"
label="Terminal UUID"
placeholder="Enter Terminal UUID"
required=true
type="Text"
[[zen.metadata.apple_pay]]
name="pay_wall_secret"
label="Pay Wall Secret"
placeholder="Enter Pay Wall Secret"
required=true
type="Text"

[[zen.metadata.google_pay]]
name="terminal_uuid"
label="Terminal UUID"
placeholder="Enter Terminal UUID"
required=true
type="Text"
[[zen.metadata.google_pay]]
name="pay_wall_secret"
label="Pay Wall Secret"
placeholder="Enter Pay Wall Secret"
required=true
type="Text"

[zsl]
[[zsl.bank_transfer]]
  payment_method_type = "local_bank_transfer"
[zsl.connector_auth.BodyKey]
api_key = "Key"
key1 = "Merchant ID"

[dummy_connector]
[[dummy_connector.credit]]
  payment_method_type = "Mastercard"
[[dummy_connector.credit]]
  payment_method_type = "Visa"
[[dummy_connector.credit]]
  payment_method_type = "Interac"
[[dummy_connector.credit]]
  payment_method_type = "AmericanExpress"
[[dummy_connector.credit]]
  payment_method_type = "JCB"
[[dummy_connector.credit]]
  payment_method_type = "DinersClub"
[[dummy_connector.credit]]
  payment_method_type = "Discover"
[[dummy_connector.credit]]
  payment_method_type = "CartesBancaires"
[[dummy_connector.credit]]
  payment_method_type = "UnionPay"
[[dummy_connector.debit]]
  payment_method_type = "Mastercard"
[[dummy_connector.debit]]
  payment_method_type = "Visa"
[[dummy_connector.debit]]
  payment_method_type = "Interac"
[[dummy_connector.debit]]
  payment_method_type = "AmericanExpress"
[[dummy_connector.debit]]
  payment_method_type = "JCB"
[[dummy_connector.debit]]
  payment_method_type = "DinersClub"
[[dummy_connector.debit]]
  payment_method_type = "Discover"
[[dummy_connector.debit]]
  payment_method_type = "CartesBancaires"
[[dummy_connector.debit]]
  payment_method_type = "UnionPay"
[dummy_connector.connector_auth.HeaderKey]
api_key="Api Key"

[paypal_test]
[[paypal_test.credit]]
  payment_method_type = "Mastercard"
[[paypal_test.credit]]
  payment_method_type = "Visa"
[[paypal_test.credit]]
  payment_method_type = "Interac"
[[paypal_test.credit]]
  payment_method_type = "AmericanExpress"
[[paypal_test.credit]]
  payment_method_type = "JCB"
[[paypal_test.credit]]
  payment_method_type = "DinersClub"
[[paypal_test.credit]]
  payment_method_type = "Discover"
[[paypal_test.credit]]
  payment_method_type = "CartesBancaires"
[[paypal_test.credit]]
  payment_method_type = "UnionPay"
[[paypal_test.debit]]
  payment_method_type = "Mastercard"
[[paypal_test.debit]]
  payment_method_type = "Visa"
[[paypal_test.debit]]
  payment_method_type = "Interac"
[[paypal_test.debit]]
  payment_method_type = "AmericanExpress"
[[paypal_test.debit]]
  payment_method_type = "JCB"
[[paypal_test.debit]]
  payment_method_type = "DinersClub"
[[paypal_test.debit]]
  payment_method_type = "Discover"
[[paypal_test.debit]]
  payment_method_type = "CartesBancaires"
[[paypal_test.debit]]
  payment_method_type = "UnionPay"
[[paypal_test.wallet]]
  payment_method_type = "paypal"
[paypal_test.connector_auth.HeaderKey]
api_key="Api Key"

[paystack]
[[paystack.bank_redirect]]
  payment_method_type = "eft"
[paystack.connector_auth.HeaderKey]
api_key="API Key"
[paystack.connector_webhook_details]
merchant_secret="API Key"

[stripe_test]
[[stripe_test.credit]]
  payment_method_type = "Mastercard"
[[stripe_test.credit]]
  payment_method_type = "Visa"
[[stripe_test.credit]]
  payment_method_type = "Interac"
[[stripe_test.credit]]
  payment_method_type = "AmericanExpress"
[[stripe_test.credit]]
  payment_method_type = "JCB"
[[stripe_test.credit]]
  payment_method_type = "DinersClub"
[[stripe_test.credit]]
  payment_method_type = "Discover"
[[stripe_test.credit]]
  payment_method_type = "CartesBancaires"
[[stripe_test.credit]]
  payment_method_type = "UnionPay"
[[stripe_test.debit]]
  payment_method_type = "Mastercard"
[[stripe_test.debit]]
  payment_method_type = "Visa"
[[stripe_test.debit]]
  payment_method_type = "Interac"
[[stripe_test.debit]]
  payment_method_type = "AmericanExpress"
[[stripe_test.debit]]
  payment_method_type = "JCB"
[[stripe_test.debit]]
  payment_method_type = "DinersClub"
[[stripe_test.debit]]
  payment_method_type = "Discover"
[[stripe_test.debit]]
  payment_method_type = "CartesBancaires"
[[stripe_test.debit]]
  payment_method_type = "UnionPay"
[[stripe_test.wallet]]
  payment_method_type = "google_pay"
[[stripe_test.wallet]]
  payment_method_type = "ali_pay"
[[stripe_test.wallet]]
  payment_method_type = "we_chat_pay"
[[stripe_test.pay_later]]
  payment_method_type = "klarna"
[[stripe_test.pay_later]]
  payment_method_type = "affirm"
[[stripe_test.pay_later]]
  payment_method_type = "afterpay_clearpay"
[[paypal_test.wallet]]
  payment_method_type = "paypal"
[stripe_test.connector_auth.HeaderKey]
api_key="Api Key"

[helcim]
[[helcim.credit]]
  payment_method_type = "Mastercard"
[[helcim.credit]]
  payment_method_type = "Visa"
[[helcim.credit]]
  payment_method_type = "Interac"
[[helcim.credit]]
  payment_method_type = "AmericanExpress"
[[helcim.credit]]
  payment_method_type = "JCB"
[[helcim.credit]]
  payment_method_type = "DinersClub"
[[helcim.credit]]
  payment_method_type = "Discover"
[[helcim.credit]]
  payment_method_type = "CartesBancaires"
[[helcim.credit]]
  payment_method_type = "UnionPay"
[[helcim.debit]]
  payment_method_type = "Mastercard"
[[helcim.debit]]
  payment_method_type = "Visa"
[[helcim.debit]]
  payment_method_type = "Interac"
[[helcim.debit]]
  payment_method_type = "AmericanExpress"
[[helcim.debit]]
  payment_method_type = "JCB"
[[helcim.debit]]
  payment_method_type = "DinersClub"
[[helcim.debit]]
  payment_method_type = "Discover"
[[helcim.debit]]
  payment_method_type = "CartesBancaires"
[[helcim.debit]]
  payment_method_type = "UnionPay"
[helcim.connector_auth.HeaderKey]
api_key="Api Key"




[adyen_payout]
[[adyen_payout.credit]]
  payment_method_type = "Mastercard"
[[adyen_payout.credit]]
  payment_method_type = "Visa"
[[adyen_payout.credit]]
  payment_method_type = "Interac"
[[adyen_payout.credit]]
  payment_method_type = "AmericanExpress"
[[adyen_payout.credit]]
  payment_method_type = "JCB"
[[adyen_payout.credit]]
  payment_method_type = "DinersClub"
[[adyen_payout.credit]]
  payment_method_type = "Discover"
[[adyen_payout.credit]]
  payment_method_type = "CartesBancaires"
[[adyen_payout.credit]]
  payment_method_type = "UnionPay"
[[adyen_payout.debit]]
  payment_method_type = "Mastercard"
[[adyen_payout.debit]]
  payment_method_type = "Visa"
[[adyen_payout.debit]]
  payment_method_type = "Interac"
[[adyen_payout.debit]]
  payment_method_type = "AmericanExpress"
[[adyen_payout.debit]]
  payment_method_type = "JCB"
[[adyen_payout.debit]]
  payment_method_type = "DinersClub"
[[adyen_payout.debit]]
  payment_method_type = "Discover"
[[adyen_payout.debit]]
  payment_method_type = "CartesBancaires"
[[adyen_payout.debit]]
  payment_method_type = "UnionPay"
[[adyen_payout.bank_transfer]]
  payment_method_type = "sepa"
[[adyen_payout.wallet]]
  payment_method_type = "paypal"
[adyen_payout.connector_auth.SignatureKey]
api_key = "Adyen API Key (Payout creation)"
api_secret = "Adyen Key (Payout submission)"
key1 = "Adyen Account Id"

[adyen_payout.metadata.endpoint_prefix]
name="endpoint_prefix"
label="Live endpoint prefix"
placeholder="Enter Live endpoint prefix"
required=true
type="Text"

[stripe_payout]
[[stripe_payout.bank_transfer]]
  payment_method_type = "ach"
[stripe_payout.connector_auth.HeaderKey]
api_key = "Stripe API Key"

[nomupay_payout]
[[nomupay_payout.bank_transfer]]
  payment_method_type = "sepa"
[nomupay_payout.connector_auth.BodyKey]
api_key = "Nomupay kid"
key1 = "Nomupay eid"
[nomupay_payout.metadata.private_key]
name="Private key for signature generation"
label="Enter your private key"
placeholder="------BEGIN PRIVATE KEY-------"
required=true
type="Text"

[wise_payout]
[[wise_payout.bank_transfer]]
  payment_method_type = "ach"
[[wise_payout.bank_transfer]]
  payment_method_type = "bacs"
[[wise_payout.bank_transfer]]
  payment_method_type = "sepa"
[wise_payout.connector_auth.BodyKey]
api_key = "Wise API Key"
key1 = "Wise Account Id"

[threedsecureio]
[threedsecureio.connector_auth.HeaderKey]
api_key="Api Key"

[threedsecureio.metadata.mcc]
name="mcc"
label="MCC"
placeholder="Enter MCC"
required=true
type="Text"
[threedsecureio.metadata.merchant_country_code]
name="merchant_country_code"
label="3 digit numeric country code"
placeholder="Enter 3 digit numeric country code"
required=true
type="Text"
[threedsecureio.metadata.merchant_name]
name="merchant_name"
label="Name of the merchant"
placeholder="Enter Name of the merchant"
required=true
type="Text"
[threedsecureio.metadata.pull_mechanism_for_external_3ds_enabled]
name="pull_mechanism_for_external_3ds_enabled"
label="Pull Mechanism Enabled"
placeholder="Enter Pull Mechanism Enabled"
required=false
type="Toggle"
[threedsecureio.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer BIN"
placeholder="Enter Acquirer BIN"
required=true
type="Text"
[threedsecureio.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant ID"
placeholder="Enter Acquirer Merchant ID"
required=true
type="Text"
[threedsecureio.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=false
type="Text"

[netcetera]
[netcetera.connector_auth.CertificateAuth]
certificate="Base64 encoded PEM formatted certificate chain"
private_key="Base64 encoded PEM formatted private key"

[netcetera.metadata.endpoint_prefix]
name="endpoint_prefix"
label="Live endpoint prefix"
placeholder="string that will replace '{prefix}' in this base url 'https://{prefix}.3ds-server.prev.netcetera-cloud-payment.ch'"
required=true
type="Text"
[netcetera.metadata.mcc]
name="mcc"
label="MCC"
placeholder="Enter MCC"
required=false
type="Text"
[netcetera.metadata.merchant_country_code]
name="merchant_country_code"
label="3 digit numeric country code"
placeholder="Enter 3 digit numeric country code"
required=false
type="Text"
[netcetera.metadata.merchant_name]
name="merchant_name"
label="Name of the merchant"
placeholder="Enter Name of the merchant"
required=false
type="Text"
[netcetera.metadata.three_ds_requestor_name]
name="three_ds_requestor_name"
label="ThreeDS requestor name"
placeholder="Enter ThreeDS requestor name"
required=false
type="Text"
[netcetera.metadata.three_ds_requestor_id]
name="three_ds_requestor_id"
label="ThreeDS request id"
placeholder="Enter ThreeDS request id"
required=false
type="Text"
[netcetera.metadata.merchant_configuration_id]
name="merchant_configuration_id"
label="Merchant Configuration ID"
placeholder="Enter Merchant Configuration ID"
required=false
type="Text"


[taxjar]
[taxjar.connector_auth.HeaderKey]
api_key="Sandbox Token"

[billwerk]
[[billwerk.credit]]
  payment_method_type = "Mastercard"
[[billwerk.credit]]
  payment_method_type = "Visa"
[[billwerk.credit]]
  payment_method_type = "Interac"
[[billwerk.credit]]
  payment_method_type = "AmericanExpress"
[[billwerk.credit]]
  payment_method_type = "JCB"
[[billwerk.credit]]
  payment_method_type = "DinersClub"
[[billwerk.credit]]
  payment_method_type = "Discover"
[[billwerk.credit]]
  payment_method_type = "CartesBancaires"
[[billwerk.credit]]
  payment_method_type = "UnionPay"
[[billwerk.debit]]
  payment_method_type = "Mastercard"
[[billwerk.debit]]
  payment_method_type = "Visa"
[[billwerk.debit]]
  payment_method_type = "Interac"
[[billwerk.debit]]
  payment_method_type = "AmericanExpress"
[[billwerk.debit]]
  payment_method_type = "JCB"
[[billwerk.debit]]
  payment_method_type = "DinersClub"
[[billwerk.debit]]
  payment_method_type = "Discover"
[[billwerk.debit]]
  payment_method_type = "CartesBancaires"
[[billwerk.debit]]
  payment_method_type = "UnionPay"
[billwerk.connector_auth.BodyKey]
api_key="Private Api Key"
key1="Public Api Key"

[datatrans]
[[datatrans.credit]]
  payment_method_type = "Mastercard"
[[datatrans.credit]]
  payment_method_type = "Visa"
[[datatrans.credit]]
  payment_method_type = "Interac"
[[datatrans.credit]]
  payment_method_type = "AmericanExpress"
[[datatrans.credit]]
  payment_method_type = "JCB"
[[datatrans.credit]]
  payment_method_type = "DinersClub"
[[datatrans.credit]]
  payment_method_type = "Discover"
[[datatrans.credit]]
  payment_method_type = "CartesBancaires"
[[datatrans.credit]]
  payment_method_type = "UnionPay"
[[datatrans.debit]]
  payment_method_type = "Mastercard"
[[datatrans.debit]]
  payment_method_type = "Visa"
[[datatrans.debit]]
  payment_method_type = "Interac"
[[datatrans.debit]]
  payment_method_type = "AmericanExpress"
[[datatrans.debit]]
  payment_method_type = "JCB"
[[datatrans.debit]]
  payment_method_type = "DinersClub"
[[datatrans.debit]]
  payment_method_type = "Discover"
[[datatrans.debit]]
  payment_method_type = "CartesBancaires"
[[datatrans.debit]]
  payment_method_type = "UnionPay"
[datatrans.connector_auth.BodyKey]
api_key = "Passcode"
key1 = "datatrans MerchantId"
[datatrans.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer Bin"
placeholder="Enter Acquirer Bin"
required=false
type="Text"
[datatrans.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant ID"
placeholder="Enter Acquirer Merchant ID"
required=false
type="Text"
[datatrans.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=false
type="Text"


[paybox]
[[paybox.credit]]
  payment_method_type = "Mastercard"
[[paybox.credit]]
  payment_method_type = "Visa"
[[paybox.credit]]
  payment_method_type = "Interac"
[[paybox.credit]]
  payment_method_type = "AmericanExpress"
[[paybox.credit]]
  payment_method_type = "JCB"
[[paybox.credit]]
  payment_method_type = "DinersClub"
[[paybox.credit]]
  payment_method_type = "Discover"
[[paybox.credit]]
  payment_method_type = "CartesBancaires"
[[paybox.credit]]
  payment_method_type = "UnionPay"
[[paybox.debit]]
  payment_method_type = "Mastercard"
[[paybox.debit]]
  payment_method_type = "Visa"
[[paybox.debit]]
  payment_method_type = "Interac"
[[paybox.debit]]
  payment_method_type = "AmericanExpress"
[[paybox.debit]]
  payment_method_type = "JCB"
[[paybox.debit]]
  payment_method_type = "DinersClub"
[[paybox.debit]]
  payment_method_type = "Discover"
[[paybox.debit]]
  payment_method_type = "CartesBancaires"
[paybox.connector_auth.MultiAuthKey]
api_key="SITE Key"
key1="Rang Identifier"
api_secret="CLE Secret"
key2 ="Merchant Id"

[wellsfargo]
[[wellsfargo.credit]]
  payment_method_type = "Mastercard"
[[wellsfargo.credit]]
  payment_method_type = "Visa"
[[wellsfargo.credit]]
  payment_method_type = "Interac"
[[wellsfargo.credit]]
  payment_method_type = "AmericanExpress"
[[wellsfargo.credit]]
  payment_method_type = "JCB"
[[wellsfargo.credit]]
  payment_method_type = "DinersClub"
[[wellsfargo.credit]]
  payment_method_type = "Discover"
[[wellsfargo.credit]]
  payment_method_type = "CartesBancaires"
[[wellsfargo.credit]]
  payment_method_type = "UnionPay"
[[wellsfargo.debit]]
  payment_method_type = "Mastercard"
[[wellsfargo.debit]]
  payment_method_type = "Visa"
[[wellsfargo.debit]]
  payment_method_type = "Interac"
[[wellsfargo.debit]]
  payment_method_type = "AmericanExpress"
[[wellsfargo.debit]]
  payment_method_type = "JCB"
[[wellsfargo.debit]]
  payment_method_type = "DinersClub"
[[wellsfargo.debit]]
  payment_method_type = "Discover"
[[wellsfargo.debit]]
  payment_method_type = "CartesBancaires"
[[wellsfargo.debit]]
  payment_method_type = "UnionPay"
[wellsfargo.connector_auth.SignatureKey]
api_key="Key"
key1="Merchant ID"
api_secret="Shared Secret"


[fiuu]
[[fiuu.credit]]
  payment_method_type = "Mastercard"
[[fiuu.credit]]
  payment_method_type = "Visa"
[[fiuu.credit]]
  payment_method_type = "Interac"
[[fiuu.credit]]
  payment_method_type = "AmericanExpress"
[[fiuu.credit]]
  payment_method_type = "JCB"
[[fiuu.credit]]
  payment_method_type = "DinersClub"
[[fiuu.credit]]
  payment_method_type = "Discover"
[[fiuu.credit]]
  payment_method_type = "CartesBancaires"
[[fiuu.credit]]
  payment_method_type = "UnionPay"
[[fiuu.debit]]
  payment_method_type = "Mastercard"
[[fiuu.debit]]
  payment_method_type = "Visa"
[[fiuu.debit]]
  payment_method_type = "Interac"
[[fiuu.debit]]
  payment_method_type = "AmericanExpress"
[[fiuu.debit]]
  payment_method_type = "JCB"
[[fiuu.debit]]
  payment_method_type = "DinersClub"
[[fiuu.debit]]
  payment_method_type = "Discover"
[[fiuu.debit]]
  payment_method_type = "CartesBancaires"
[[fiuu.debit]]
  payment_method_type = "UnionPay"
[[fiuu.real_time_payment]]
  payment_method_type = "duit_now"
[[fiuu.wallet]]
  payment_method_type = "google_pay"
[[fiuu.wallet]]
  payment_method_type = "apple_pay"
[[fiuu.bank_redirect]]
  payment_method_type = "online_banking_fpx"
[fiuu.connector_auth.SignatureKey]
api_key="Verify Key"
key1="Merchant ID"
api_secret="Secret Key"

[[fiuu.metadata.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[fiuu.metadata.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[fiuu.metadata.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[fiuu.metadata.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]

[[fiuu.connector_wallets_details.google_pay]]
name="merchant_name"
label="Google Pay Merchant Name"
placeholder="Enter Google Pay Merchant Name"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="merchant_id"
label="Google Pay Merchant Id"
placeholder="Enter Google Pay Merchant Id"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="gateway_merchant_id"
label="Google Pay Merchant Key"
placeholder="Enter Google Pay Merchant Key"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="public_key"
label="Google Pay Public Key"
placeholder="Enter Google Pay Public Key"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="private_key"
label="Google Pay Private Key"
placeholder="Enter Google Pay Private Key"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="recipient_id"
label="Recipient Id"
placeholder="Enter Recipient Id"
required=true
type="Text"
[[fiuu.connector_wallets_details.google_pay]]
name="allowed_auth_methods"
label="Allowed Auth Methods"
placeholder="Enter Allowed Auth Methods"
required=true
type="MultiSelect"
options=["PAN_ONLY", "CRYPTOGRAM_3DS"]


[[fiuu.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[fiuu.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[fiuu.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[fiuu.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[fiuu.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Select"
options=["web","ios"]
[[fiuu.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[fiuu.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[fiuu.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Hyperswitch"]

[fiuu.connector_webhook_details]
merchant_secret="Source verification key"

[tokenio]
[[tokenio.open_banking]]
  payment_method_type = "open_banking_pis"
[tokenio.connector_webhook_details]
merchant_secret="Tokenio Public Key"
[tokenio.connector_auth.MultiAuthKey]
api_key="Key Id"
api_secret="Private Key"
key1="Merchant Id"
key2="Key Algorithm"
[tokenio.additional_merchant_data.open_banking_recipient_data]
name="open_banking_recipient_data"
label="Open Banking Recipient Data"
placeholder="Enter Open Banking Recipient Data"
required=true
type="Select"
options=["account_data"]
[tokenio.additional_merchant_data.account_data]
name="account_data"
label="Bank scheme"
placeholder="Enter account_data"
required=true
type="Select"
options=["iban","bacs","faster_payments","sepa","sepa_instant","elixir","bankgiro","plusgiro"]

[[tokenio.additional_merchant_data.iban]]
name = "iban"
label = "IBAN"
placeholder = "Enter IBAN"
required = true
type = "Text"

[[tokenio.additional_merchant_data.iban]]
name = "iban_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# BACS Configuration (array of InputData)
[[tokenio.additional_merchant_data.bacs]]
name = "bacs_sort_code"
label = "Sort Code"
placeholder = "Enter sort code (e.g., 12-34-56)"
required = true
type = "Text"

[[tokenio.additional_merchant_data.bacs]]
name = "bacs_account_number"
label = "Account Number"
placeholder = "Enter account number"
required = true
type = "Text"

[[tokenio.additional_merchant_data.bacs]]
name = "bacs_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# Faster Payments Configuration (array of InputData)
[[tokenio.additional_merchant_data.faster_payments]]
name = "faster_payments_sort_code"
label = "Sort Code"
placeholder = "Enter sort code (e.g., 12-34-56)"
required = true
type = "Text"

[[tokenio.additional_merchant_data.faster_payments]]
name = "faster_payments_account_number"
label = "Account Number"
placeholder = "Enter account number"
required = true
type = "Text"

[[tokenio.additional_merchant_data.faster_payments]]
name = "faster_payments_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# SEPA Configuration (array of InputData)
[[tokenio.additional_merchant_data.sepa]]
name = "sepa_iban"
label = "IBAN"
placeholder = "Enter IBAN"
required = true
type = "Text"

[[tokenio.additional_merchant_data.sepa]]
name = "sepa_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# SEPA Instant Configuration (array of InputData)
[[tokenio.additional_merchant_data.sepa_instant]]
name = "sepa_instant_iban"
label = "IBAN"
placeholder = "Enter IBAN"
required = true
type = "Text"

[[tokenio.additional_merchant_data.sepa_instant]]
name = "sepa_instant_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# Elixir Configuration (array of InputData)
[[tokenio.additional_merchant_data.elixir]]
name = "elixir_account_number"
label = "Account Number"
placeholder = "Enter account number"
required = true
type = "Text"

[[tokenio.additional_merchant_data.elixir]]
name = "elixir_iban"
label = "IBAN"
placeholder = "Enter IBAN"
required = true
type = "Text"

[[tokenio.additional_merchant_data.elixir]]
name = "elixir_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# Bankgiro Configuration (array of InputData)
[[tokenio.additional_merchant_data.bankgiro]]
name = "bankgiro_number"
label = "Bankgiro Number"
placeholder = "Enter bankgiro number"
required = true
type = "Text"

[[tokenio.additional_merchant_data.bankgiro]]
name = "bankgiro_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

# Plusgiro Configuration (array of InputData)
[[tokenio.additional_merchant_data.plusgiro]]
name = "plusgiro_number"
label = "Plusgiro Number"
placeholder = "Enter plusgiro number"
required = true
type = "Text"

[[tokenio.additional_merchant_data.plusgiro]]
name = "plusgiro_name"
label = "Account Holder Name"
placeholder = "Enter account holder name"
required = true
type = "Text"

[elavon]
[[elavon.credit]]
  payment_method_type = "Mastercard"
[[elavon.credit]]
  payment_method_type = "Visa"
[[elavon.credit]]
  payment_method_type = "Interac"
[[elavon.credit]]
  payment_method_type = "AmericanExpress"
[[elavon.credit]]
  payment_method_type = "JCB"
[[elavon.credit]]
  payment_method_type = "DinersClub"
[[elavon.credit]]
  payment_method_type = "Discover"
[[elavon.credit]]
  payment_method_type = "CartesBancaires"
[[elavon.credit]]
  payment_method_type = "UnionPay"
[[elavon.debit]]
  payment_method_type = "Mastercard"
[[elavon.debit]]
  payment_method_type = "Visa"
[[elavon.debit]]
  payment_method_type = "Interac"
[[elavon.debit]]
  payment_method_type = "AmericanExpress"
[[elavon.debit]]
  payment_method_type = "JCB"
[[elavon.debit]]
  payment_method_type = "DinersClub"
[[elavon.debit]]
  payment_method_type = "Discover"
[[elavon.debit]]
  payment_method_type = "CartesBancaires"
[[elavon.debit]]
  payment_method_type = "UnionPay"
[elavon.connector_auth.SignatureKey]
api_key="Account Id"
key1="User ID"
api_secret="Pin"

[ctp_mastercard]
[ctp_mastercard.connector_auth.HeaderKey]
api_key="API Key"

[ctp_mastercard.metadata.dpa_id]
name="dpa_id"
label="DPA Id"
placeholder="Enter DPA Id"
required=true
type="Text"

[ctp_mastercard.metadata.dpa_name]
name="dpa_name"
label="DPA Name"
placeholder="Enter DPA Name"
required=true
type="Text"

[ctp_mastercard.metadata.locale]
name="locale"
label="Locale"
placeholder="Enter locale"
required=true
type="Text"



[ctp_mastercard.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquire Bin"
placeholder="Enter Acquirer Bin"
required=true
type="Text"

[ctp_mastercard.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquire Merchant Id"
placeholder="Enter Acquirer Merchant Id"
required=true
type="Text"

[ctp_mastercard.metadata.merchant_category_code]
name="merchant_category_code"
label="Merchant Category Code"
placeholder="Enter Merchant Category Code"
required=true
type="Text"

[ctp_mastercard.metadata.merchant_country_code]
name="merchant_country_code"
label="Merchant Country Code"
placeholder="Enter Merchant Country Code"
required=true
type="Text"

[xendit]
[[xendit.credit]]
  payment_method_type = "Mastercard"
[[xendit.credit]]
  payment_method_type = "Visa"
[[xendit.credit]]
  payment_method_type = "Interac"
[[xendit.credit]]
  payment_method_type = "AmericanExpress"
[[xendit.credit]]
  payment_method_type = "JCB"
[[xendit.credit]]
  payment_method_type = "DinersClub"
[[xendit.credit]]
  payment_method_type = "Discover"
[[xendit.credit]]
  payment_method_type = "CartesBancaires"
[[xendit.credit]]
  payment_method_type = "UnionPay"
[[xendit.debit]]
  payment_method_type = "Mastercard"
[[xendit.debit]]
  payment_method_type = "Visa"
[[xendit.debit]]
  payment_method_type = "Interac"
[[xendit.debit]]
  payment_method_type = "AmericanExpress"
[[xendit.debit]]
  payment_method_type = "JCB"
[[xendit.debit]]
  payment_method_type = "DinersClub"
[[xendit.debit]]
  payment_method_type = "Discover"
[[xendit.debit]]
  payment_method_type = "CartesBancaires"
[[xendit.debit]]
  payment_method_type = "UnionPay"
[xendit.connector_auth.HeaderKey]
api_key="API Key"
[xendit.connector_webhook_details]
merchant_secret="Webhook Verification Token"

[inespay]
[[inespay.bank_debit]]
  payment_method_type = "sepa"
[inespay.connector_auth.BodyKey]
api_key="API Key"
key1="API Token"
[inespay.connector_webhook_details]
merchant_secret="API Key"

[juspaythreedsserver]
[juspaythreedsserver.metadata.mcc]
name="mcc"
label="MCC"
placeholder="Enter MCC"
required=false
type="Text"

[juspaythreedsserver.metadata.merchant_country_code]
name="merchant_country_code"
label="3 digit numeric country code"
placeholder="Enter 3 digit numeric country code"
required=false
type="Text"
[juspaythreedsserver.metadata.merchant_name]
name="merchant_name"
label="Name of the merchant"
placeholder="Enter Name of the merchant"
required=false
type="Text"
[juspaythreedsserver.metadata.three_ds_requestor_name]
name="three_ds_requestor_name"
label="ThreeDS requestor name"
placeholder="Enter ThreeDS requestor name"
required=false
type="Text"
[juspaythreedsserver.metadata.three_ds_requestor_id]
name="three_ds_requestor_id"
label="ThreeDS request id"
placeholder="Enter ThreeDS request id"
required=false
type="Text"
[juspaythreedsserver.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquirer Bin"
placeholder="Enter Acquirer Bin"
required=true
type="Text"
[juspaythreedsserver.metadata.acquirer_country_code]
name="acquirer_country_code"
label="Acquirer Country Code"
placeholder="Enter Acquirer Country Code"
required=true
type="Text"
[juspaythreedsserver.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquirer Merchant Id"
placeholder="Enter Acquirer Merchant Id"
required=true
type="Text"
[juspaythreedsserver.connector_auth.HeaderKey]
api_key="API Key"


[hipay]
[[hipay.credit]]
  payment_method_type = "Mastercard"
[[hipay.credit]]
  payment_method_type = "Visa"
[[hipay.credit]]
  payment_method_type = "Interac"
[[hipay.credit]]
  payment_method_type = "AmericanExpress"
[[hipay.credit]]
  payment_method_type = "JCB"
[[hipay.credit]]
  payment_method_type = "DinersClub"
[[hipay.credit]]
  payment_method_type = "Discover"
[[hipay.credit]]
  payment_method_type = "CartesBancaires"
[[hipay.credit]]
  payment_method_type = "UnionPay"
[[hipay.debit]]
  payment_method_type = "Mastercard"
[[hipay.debit]]
  payment_method_type = "Visa"
[[hipay.debit]]
  payment_method_type = "Interac"
[[hipay.debit]]
  payment_method_type = "AmericanExpress"
[[hipay.debit]]
  payment_method_type = "JCB"
[[hipay.debit]]
  payment_method_type = "DinersClub"
[[hipay.debit]]
  payment_method_type = "Discover"
[[hipay.debit]]
  payment_method_type = "CartesBancaires"
[[hipay.debit]]
  payment_method_type = "UnionPay"
[hipay.connector_auth.BodyKey]
api_key="API Login ID"
key1="API password"

[ctp_visa]
[ctp_visa.connector_auth.NoKey]


[ctp_visa.metadata.dpa_id]
name="dpa_id"
label="DPA Id"
placeholder="Enter DPA Id"
required=true
type="Text"

[ctp_visa.metadata.dpa_name]
name="dpa_name"
label="DPA Name"
placeholder="Enter DPA Name"
required=true
type="Text"

[ctp_visa.metadata.locale]
name="locale"
label="Locale"
placeholder="Enter locale"
required=true
type="Text"


[ctp_visa.metadata.acquirer_bin]
name="acquirer_bin"
label="Acquire Bin"
placeholder="Enter Acquirer Bin"
required=true
type="Text"

[ctp_visa.metadata.acquirer_merchant_id]
name="acquirer_merchant_id"
label="Acquire Merchant Id"
placeholder="Enter Acquirer Merchant Id"
required=true
type="Text"

[ctp_visa.metadata.merchant_category_code]
name="merchant_category_code"
label="Merchant Category Code"
placeholder="Enter Merchant Category Code"
required=true
type="Text"

[ctp_visa.metadata.merchant_country_code]
name="merchant_country_code"
label="Merchant Country Code"
placeholder="Enter Merchant Country Code"
required=true
type="Text"

[ctp_visa.metadata.dpa_client_id]
name="dpa_client_id"
label="DPA Client ID"
placeholder="Enter DPA Client ID"
required=true
type="Text"

[redsys]
[[redsys.credit]]
payment_method_type = "Mastercard"
[[redsys.credit]]
payment_method_type = "Visa"
[[redsys.credit]]
payment_method_type = "AmericanExpress"
[[redsys.credit]]
payment_method_type = "JCB"
[[redsys.credit]]
payment_method_type = "DinersClub"
[[redsys.credit]]
payment_method_type = "UnionPay"
[[redsys.debit]]
payment_method_type = "Mastercard"
[[redsys.debit]]
payment_method_type = "Visa"
[[redsys.debit]]
payment_method_type = "AmericanExpress"
[[redsys.debit]]
payment_method_type = "JCB"
[[redsys.debit]]
payment_method_type = "DinersClub"
[[redsys.debit]]
payment_method_type = "UnionPay"
[redsys.connector_auth.SignatureKey]
api_key = "Merchant ID"
key1 = "Terminal ID"
api_secret = "Secret Key"

[facilitapay]
[[facilitapay.bank_transfer]]
  payment_method_type = "pix"
[facilitapay.connector_auth.BodyKey]
  api_key="Password"
  key1="Username"

[archipel]
[archipel.connector_auth.HeaderKey]
api_key="Enter CA Certificate PEM"
[[archipel.credit]]
payment_method_type = "Mastercard"
[[archipel.credit]]
payment_method_type = "Visa"
[[archipel.credit]]
payment_method_type = "AmericanExpress"
[[archipel.credit]]
payment_method_type = "DinersClub"
[[archipel.credit]]
payment_method_type = "Discover"
[[archipel.credit]]
payment_method_type = "CartesBancaires"
[[archipel.debit]]
payment_method_type = "Mastercard"
[[archipel.debit]]
payment_method_type = "Visa"
[[archipel.debit]]
payment_method_type = "AmericanExpress"
[[archipel.debit]]
payment_method_type = "DinersClub"
[[archipel.debit]]
payment_method_type = "Discover"
[[archipel.debit]]
payment_method_type = "CartesBancaires"
[[archipel.wallet]]
payment_method_type = "apple_pay"
[archipel.metadata.tenant_id]
name="tenant_id"
label="Tenant ID"
placeholder="Enter Archipel tenantID"
required=true
type="Text"
[archipel.metadata.platform_url]
name="platform_url"
label="Platform Endpoint Prefix"
placeholder="E.g. *********:8080"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="certificate"
label="Merchant Certificate (Base64 Encoded)"
placeholder="Enter Merchant Certificate (Base64 Encoded)"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="certificate_keys"
label="Merchant PrivateKey (Base64 Encoded)"
placeholder="Enter Merchant PrivateKey (Base64 Encoded)"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="merchant_identifier"
label="Apple Merchant Identifier"
placeholder="Enter Apple Merchant Identifier"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="display_name"
label="Display Name"
placeholder="Enter Display Name"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="initiative"
label="Domain"
placeholder="Enter Domain"
required=true
type="Select"
options=["web","ios"]
[[archipel.metadata.apple_pay]]
name="initiative_context"
label="Domain Name"
placeholder="Enter Domain Name"
required=true
type="Text"
[[archipel.metadata.apple_pay]]
name="merchant_business_country"
label="Merchant Business Country"
placeholder="Enter Merchant Business Country"
required=true
type="Select"
options=[]
[[archipel.metadata.apple_pay]]
name="payment_processing_details_at"
label="Payment Processing Details At"
placeholder="Enter Payment Processing Details At"
required=true
type="Radio"
options=["Hyperswitch"]

[archipel.metadata.acquirer_bin]
name = "acquirer_bin"
label = "Acquirer Bin"
placeholder = "Enter Acquirer Bin"
required = false
type = "Text"
[archipel.metadata.acquirer_merchant_id]
name = "acquirer_merchant_id"
label = "Acquirer Merchant ID"
placeholder = "Enter Acquirer Merchant ID"
required = false
type = "Text"


[worldpayxml]
[[worldpayxml.credit]]
  payment_method_type = "Mastercard"
[[worldpayxml.credit]]
  payment_method_type = "Visa"
[[worldpayxml.credit]]
  payment_method_type = "Interac"
[[worldpayxml.credit]]
  payment_method_type = "AmericanExpress"
[[worldpayxml.credit]]
  payment_method_type = "JCB"
[[worldpayxml.credit]]
  payment_method_type = "DinersClub"
[[worldpayxml.credit]]
  payment_method_type = "Discover"
[[worldpayxml.credit]]
  payment_method_type = "CartesBancaires"
[[worldpayxml.credit]]
  payment_method_type = "UnionPay"
[[worldpayxml.debit]]
  payment_method_type = "Mastercard"
[[worldpayxml.debit]]
  payment_method_type = "Visa"
[[worldpayxml.debit]]
  payment_method_type = "Interac"
[[worldpayxml.debit]]
  payment_method_type = "AmericanExpress"
[[worldpayxml.debit]]
  payment_method_type = "JCB"
[[worldpayxml.debit]]
  payment_method_type = "DinersClub"
[[worldpayxml.debit]]
  payment_method_type = "Discover"
[[worldpayxml.debit]]
  payment_method_type = "CartesBancaires"
[[worldpayxml.debit]]
  payment_method_type = "UnionPay"

[worldpayxml.connector_auth.SignatureKey]
api_secret="Merchant Code"
api_key="API Username"
key1="API Password"

[worldpayvantiv]
[[worldpayvantiv.credit]]
  payment_method_type = "Mastercard"
[[worldpayvantiv.credit]]
  payment_method_type = "Visa"
[[worldpayvantiv.credit]]
  payment_method_type = "AmericanExpress"
[[worldpayvantiv.credit]]
  payment_method_type = "JCB"
[[worldpayvantiv.credit]]
  payment_method_type = "DinersClub"
[[worldpayvantiv.credit]]
  payment_method_type = "Discover"
[[worldpayvantiv.debit]]
  payment_method_type = "Mastercard"
[[worldpayvantiv.debit]]
  payment_method_type = "Visa"
[[worldpayvantiv.debit]]
  payment_method_type = "AmericanExpress"
[[worldpayvantiv.debit]]
  payment_method_type = "JCB"
[[worldpayvantiv.debit]]
  payment_method_type = "DinersClub"
[[worldpayvantiv.debit]]
  payment_method_type = "Discover"

[worldpayvantiv.connector_auth.SignatureKey]
api_key="Username"
api_secret="Password"
key1="Merchant ID"

[worldpayvantiv.metadata.report_group]
name="report_group"
label="Default Report Group"
placeholder="Enter Default Report Group"
required=true
type="Text"

[worldpayvantiv.metadata.merchant_config_currency]
name="merchant_config_currency"
label="Currency"
placeholder="Enter Currency"
required=true
type="Select"
options=[]