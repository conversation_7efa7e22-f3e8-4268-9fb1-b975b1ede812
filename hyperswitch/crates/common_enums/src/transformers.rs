use std::fmt::{Display, Formatter};

use serde::{Deserialize, Serialize};

use crate::enums::{
    AttemptStatus, Country, CountryAlpha2, CountryAlpha3, IntentStatus, PaymentMethod,
    PaymentMethodType,
};

impl Display for NumericCountryCodeParseError {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "Invalid Country Code")
    }
}

impl CountryAlpha2 {
    pub const fn from_alpha2_to_alpha3(code: Self) -> CountryAlpha3 {
        match code {
            Self::AF => CountryAlpha3::AFG,
            Self::AX => CountryAlpha3::ALA,
            Self::AL => CountryAlpha3::ALB,
            Self::DZ => CountryAlpha3::DZA,
            Self::AS => CountryAlpha3::ASM,
            Self::AD => CountryAlpha3::AND,
            Self::AO => CountryAlpha3::AGO,
            Self::AI => CountryAlpha3::AIA,
            Self::AQ => CountryAlpha3::ATA,
            Self::AG => CountryAlpha3::ATG,
            Self::AR => CountryAlpha3::ARG,
            Self::AM => CountryAlpha3::ARM,
            Self::AW => CountryAlpha3::ABW,
            Self::AU => CountryAlpha3::AUS,
            Self::AT => CountryAlpha3::AUT,
            Self::AZ => CountryAlpha3::AZE,
            Self::BS => CountryAlpha3::BHS,
            Self::BH => CountryAlpha3::BHR,
            Self::BD => CountryAlpha3::BGD,
            Self::BB => CountryAlpha3::BRB,
            Self::BY => CountryAlpha3::BLR,
            Self::BE => CountryAlpha3::BEL,
            Self::BZ => CountryAlpha3::BLZ,
            Self::BJ => CountryAlpha3::BEN,
            Self::BM => CountryAlpha3::BMU,
            Self::BT => CountryAlpha3::BTN,
            Self::BO => CountryAlpha3::BOL,
            Self::BQ => CountryAlpha3::BES,
            Self::BA => CountryAlpha3::BIH,
            Self::BW => CountryAlpha3::BWA,
            Self::BV => CountryAlpha3::BVT,
            Self::BR => CountryAlpha3::BRA,
            Self::IO => CountryAlpha3::IOT,
            Self::BN => CountryAlpha3::BRN,
            Self::BG => CountryAlpha3::BGR,
            Self::BF => CountryAlpha3::BFA,
            Self::BI => CountryAlpha3::BDI,
            Self::CV => CountryAlpha3::CPV,
            Self::KH => CountryAlpha3::KHM,
            Self::CM => CountryAlpha3::CMR,
            Self::CA => CountryAlpha3::CAN,
            Self::KY => CountryAlpha3::CYM,
            Self::CF => CountryAlpha3::CAF,
            Self::TD => CountryAlpha3::TCD,
            Self::CL => CountryAlpha3::CHL,
            Self::CN => CountryAlpha3::CHN,
            Self::CX => CountryAlpha3::CXR,
            Self::CC => CountryAlpha3::CCK,
            Self::CO => CountryAlpha3::COL,
            Self::KM => CountryAlpha3::COM,
            Self::CG => CountryAlpha3::COG,
            Self::CD => CountryAlpha3::COD,
            Self::CK => CountryAlpha3::COK,
            Self::CR => CountryAlpha3::CRI,
            Self::CI => CountryAlpha3::CIV,
            Self::HR => CountryAlpha3::HRV,
            Self::CU => CountryAlpha3::CUB,
            Self::CW => CountryAlpha3::CUW,
            Self::CY => CountryAlpha3::CYP,
            Self::CZ => CountryAlpha3::CZE,
            Self::DK => CountryAlpha3::DNK,
            Self::DJ => CountryAlpha3::DJI,
            Self::DM => CountryAlpha3::DMA,
            Self::DO => CountryAlpha3::DOM,
            Self::EC => CountryAlpha3::ECU,
            Self::EG => CountryAlpha3::EGY,
            Self::SV => CountryAlpha3::SLV,
            Self::GQ => CountryAlpha3::GNQ,
            Self::ER => CountryAlpha3::ERI,
            Self::EE => CountryAlpha3::EST,
            Self::ET => CountryAlpha3::ETH,
            Self::FK => CountryAlpha3::FLK,
            Self::FO => CountryAlpha3::FRO,
            Self::FJ => CountryAlpha3::FJI,
            Self::FI => CountryAlpha3::FIN,
            Self::FR => CountryAlpha3::FRA,
            Self::GF => CountryAlpha3::GUF,
            Self::PF => CountryAlpha3::PYF,
            Self::TF => CountryAlpha3::ATF,
            Self::GA => CountryAlpha3::GAB,
            Self::GM => CountryAlpha3::GMB,
            Self::GE => CountryAlpha3::GEO,
            Self::DE => CountryAlpha3::DEU,
            Self::GH => CountryAlpha3::GHA,
            Self::GI => CountryAlpha3::GIB,
            Self::GR => CountryAlpha3::GRC,
            Self::GL => CountryAlpha3::GRL,
            Self::GD => CountryAlpha3::GRD,
            Self::GP => CountryAlpha3::GLP,
            Self::GU => CountryAlpha3::GUM,
            Self::GT => CountryAlpha3::GTM,
            Self::GG => CountryAlpha3::GGY,
            Self::GN => CountryAlpha3::GIN,
            Self::GW => CountryAlpha3::GNB,
            Self::GY => CountryAlpha3::GUY,
            Self::HT => CountryAlpha3::HTI,
            Self::HM => CountryAlpha3::HMD,
            Self::VA => CountryAlpha3::VAT,
            Self::HN => CountryAlpha3::HND,
            Self::HK => CountryAlpha3::HKG,
            Self::HU => CountryAlpha3::HUN,
            Self::IS => CountryAlpha3::ISL,
            Self::IN => CountryAlpha3::IND,
            Self::ID => CountryAlpha3::IDN,
            Self::IR => CountryAlpha3::IRN,
            Self::IQ => CountryAlpha3::IRQ,
            Self::IE => CountryAlpha3::IRL,
            Self::IM => CountryAlpha3::IMN,
            Self::IL => CountryAlpha3::ISR,
            Self::IT => CountryAlpha3::ITA,
            Self::JM => CountryAlpha3::JAM,
            Self::JP => CountryAlpha3::JPN,
            Self::JE => CountryAlpha3::JEY,
            Self::JO => CountryAlpha3::JOR,
            Self::KZ => CountryAlpha3::KAZ,
            Self::KE => CountryAlpha3::KEN,
            Self::KI => CountryAlpha3::KIR,
            Self::KP => CountryAlpha3::PRK,
            Self::KR => CountryAlpha3::KOR,
            Self::KW => CountryAlpha3::KWT,
            Self::KG => CountryAlpha3::KGZ,
            Self::LA => CountryAlpha3::LAO,
            Self::LV => CountryAlpha3::LVA,
            Self::LB => CountryAlpha3::LBN,
            Self::LS => CountryAlpha3::LSO,
            Self::LR => CountryAlpha3::LBR,
            Self::LY => CountryAlpha3::LBY,
            Self::LI => CountryAlpha3::LIE,
            Self::LT => CountryAlpha3::LTU,
            Self::LU => CountryAlpha3::LUX,
            Self::MO => CountryAlpha3::MAC,
            Self::MK => CountryAlpha3::MKD,
            Self::MG => CountryAlpha3::MDG,
            Self::MW => CountryAlpha3::MWI,
            Self::MY => CountryAlpha3::MYS,
            Self::MV => CountryAlpha3::MDV,
            Self::ML => CountryAlpha3::MLI,
            Self::MT => CountryAlpha3::MLT,
            Self::MH => CountryAlpha3::MHL,
            Self::MQ => CountryAlpha3::MTQ,
            Self::MR => CountryAlpha3::MRT,
            Self::MU => CountryAlpha3::MUS,
            Self::YT => CountryAlpha3::MYT,
            Self::MX => CountryAlpha3::MEX,
            Self::FM => CountryAlpha3::FSM,
            Self::MD => CountryAlpha3::MDA,
            Self::MC => CountryAlpha3::MCO,
            Self::MN => CountryAlpha3::MNG,
            Self::ME => CountryAlpha3::MNE,
            Self::MS => CountryAlpha3::MSR,
            Self::MA => CountryAlpha3::MAR,
            Self::MZ => CountryAlpha3::MOZ,
            Self::MM => CountryAlpha3::MMR,
            Self::NA => CountryAlpha3::NAM,
            Self::NR => CountryAlpha3::NRU,
            Self::NP => CountryAlpha3::NPL,
            Self::NL => CountryAlpha3::NLD,
            Self::NC => CountryAlpha3::NCL,
            Self::NZ => CountryAlpha3::NZL,
            Self::NI => CountryAlpha3::NIC,
            Self::NE => CountryAlpha3::NER,
            Self::NG => CountryAlpha3::NGA,
            Self::NU => CountryAlpha3::NIU,
            Self::NF => CountryAlpha3::NFK,
            Self::MP => CountryAlpha3::MNP,
            Self::NO => CountryAlpha3::NOR,
            Self::OM => CountryAlpha3::OMN,
            Self::PK => CountryAlpha3::PAK,
            Self::PW => CountryAlpha3::PLW,
            Self::PS => CountryAlpha3::PSE,
            Self::PA => CountryAlpha3::PAN,
            Self::PG => CountryAlpha3::PNG,
            Self::PY => CountryAlpha3::PRY,
            Self::PE => CountryAlpha3::PER,
            Self::PH => CountryAlpha3::PHL,
            Self::PN => CountryAlpha3::PCN,
            Self::PL => CountryAlpha3::POL,
            Self::PT => CountryAlpha3::PRT,
            Self::PR => CountryAlpha3::PRI,
            Self::QA => CountryAlpha3::QAT,
            Self::RE => CountryAlpha3::REU,
            Self::RO => CountryAlpha3::ROU,
            Self::RU => CountryAlpha3::RUS,
            Self::RW => CountryAlpha3::RWA,
            Self::BL => CountryAlpha3::BLM,
            Self::SH => CountryAlpha3::SHN,
            Self::KN => CountryAlpha3::KNA,
            Self::LC => CountryAlpha3::LCA,
            Self::MF => CountryAlpha3::MAF,
            Self::PM => CountryAlpha3::SPM,
            Self::VC => CountryAlpha3::VCT,
            Self::WS => CountryAlpha3::WSM,
            Self::SM => CountryAlpha3::SMR,
            Self::ST => CountryAlpha3::STP,
            Self::SA => CountryAlpha3::SAU,
            Self::SN => CountryAlpha3::SEN,
            Self::RS => CountryAlpha3::SRB,
            Self::SC => CountryAlpha3::SYC,
            Self::SL => CountryAlpha3::SLE,
            Self::SG => CountryAlpha3::SGP,
            Self::SX => CountryAlpha3::SXM,
            Self::SK => CountryAlpha3::SVK,
            Self::SI => CountryAlpha3::SVN,
            Self::SB => CountryAlpha3::SLB,
            Self::SO => CountryAlpha3::SOM,
            Self::ZA => CountryAlpha3::ZAF,
            Self::GS => CountryAlpha3::SGS,
            Self::SS => CountryAlpha3::SSD,
            Self::ES => CountryAlpha3::ESP,
            Self::LK => CountryAlpha3::LKA,
            Self::SD => CountryAlpha3::SDN,
            Self::SR => CountryAlpha3::SUR,
            Self::SJ => CountryAlpha3::SJM,
            Self::SZ => CountryAlpha3::SWZ,
            Self::SE => CountryAlpha3::SWE,
            Self::CH => CountryAlpha3::CHE,
            Self::SY => CountryAlpha3::SYR,
            Self::TW => CountryAlpha3::TWN,
            Self::TJ => CountryAlpha3::TJK,
            Self::TZ => CountryAlpha3::TZA,
            Self::TH => CountryAlpha3::THA,
            Self::TL => CountryAlpha3::TLS,
            Self::TG => CountryAlpha3::TGO,
            Self::TK => CountryAlpha3::TKL,
            Self::TO => CountryAlpha3::TON,
            Self::TT => CountryAlpha3::TTO,
            Self::TN => CountryAlpha3::TUN,
            Self::TR => CountryAlpha3::TUR,
            Self::TM => CountryAlpha3::TKM,
            Self::TC => CountryAlpha3::TCA,
            Self::TV => CountryAlpha3::TUV,
            Self::UG => CountryAlpha3::UGA,
            Self::UA => CountryAlpha3::UKR,
            Self::AE => CountryAlpha3::ARE,
            Self::GB => CountryAlpha3::GBR,
            Self::US => CountryAlpha3::USA,
            Self::UM => CountryAlpha3::UMI,
            Self::UY => CountryAlpha3::URY,
            Self::UZ => CountryAlpha3::UZB,
            Self::VU => CountryAlpha3::VUT,
            Self::VE => CountryAlpha3::VEN,
            Self::VN => CountryAlpha3::VNM,
            Self::VG => CountryAlpha3::VGB,
            Self::VI => CountryAlpha3::VIR,
            Self::WF => CountryAlpha3::WLF,
            Self::EH => CountryAlpha3::ESH,
            Self::YE => CountryAlpha3::YEM,
            Self::ZM => CountryAlpha3::ZMB,
            Self::ZW => CountryAlpha3::ZWE,
        }
    }
}
impl Country {
    pub const fn from_alpha2(code: CountryAlpha2) -> Self {
        match code {
            CountryAlpha2::AF => Self::Afghanistan,
            CountryAlpha2::AX => Self::AlandIslands,
            CountryAlpha2::AL => Self::Albania,
            CountryAlpha2::DZ => Self::Algeria,
            CountryAlpha2::AS => Self::AmericanSamoa,
            CountryAlpha2::AD => Self::Andorra,
            CountryAlpha2::AO => Self::Angola,
            CountryAlpha2::AI => Self::Anguilla,
            CountryAlpha2::AQ => Self::Antarctica,
            CountryAlpha2::AG => Self::AntiguaAndBarbuda,
            CountryAlpha2::AR => Self::Argentina,
            CountryAlpha2::AM => Self::Armenia,
            CountryAlpha2::AW => Self::Aruba,
            CountryAlpha2::AU => Self::Australia,
            CountryAlpha2::AT => Self::Austria,
            CountryAlpha2::AZ => Self::Azerbaijan,
            CountryAlpha2::BS => Self::Bahamas,
            CountryAlpha2::BH => Self::Bahrain,
            CountryAlpha2::BD => Self::Bangladesh,
            CountryAlpha2::BB => Self::Barbados,
            CountryAlpha2::BY => Self::Belarus,
            CountryAlpha2::BE => Self::Belgium,
            CountryAlpha2::BZ => Self::Belize,
            CountryAlpha2::BJ => Self::Benin,
            CountryAlpha2::BM => Self::Bermuda,
            CountryAlpha2::BT => Self::Bhutan,
            CountryAlpha2::BO => Self::BoliviaPlurinationalState,
            CountryAlpha2::BQ => Self::BonaireSintEustatiusAndSaba,
            CountryAlpha2::BA => Self::BosniaAndHerzegovina,
            CountryAlpha2::BW => Self::Botswana,
            CountryAlpha2::BV => Self::BouvetIsland,
            CountryAlpha2::BR => Self::Brazil,
            CountryAlpha2::IO => Self::BritishIndianOceanTerritory,
            CountryAlpha2::BN => Self::BruneiDarussalam,
            CountryAlpha2::BG => Self::Bulgaria,
            CountryAlpha2::BF => Self::BurkinaFaso,
            CountryAlpha2::BI => Self::Burundi,
            CountryAlpha2::CV => Self::CaboVerde,
            CountryAlpha2::KH => Self::Cambodia,
            CountryAlpha2::CM => Self::Cameroon,
            CountryAlpha2::CA => Self::Canada,
            CountryAlpha2::KY => Self::CaymanIslands,
            CountryAlpha2::CF => Self::CentralAfricanRepublic,
            CountryAlpha2::TD => Self::Chad,
            CountryAlpha2::CL => Self::Chile,
            CountryAlpha2::CN => Self::China,
            CountryAlpha2::CX => Self::ChristmasIsland,
            CountryAlpha2::CC => Self::CocosKeelingIslands,
            CountryAlpha2::CO => Self::Colombia,
            CountryAlpha2::KM => Self::Comoros,
            CountryAlpha2::CG => Self::Congo,
            CountryAlpha2::CD => Self::CongoDemocraticRepublic,
            CountryAlpha2::CK => Self::CookIslands,
            CountryAlpha2::CR => Self::CostaRica,
            CountryAlpha2::CI => Self::CotedIvoire,
            CountryAlpha2::HR => Self::Croatia,
            CountryAlpha2::CU => Self::Cuba,
            CountryAlpha2::CW => Self::Curacao,
            CountryAlpha2::CY => Self::Cyprus,
            CountryAlpha2::CZ => Self::Czechia,
            CountryAlpha2::DK => Self::Denmark,
            CountryAlpha2::DJ => Self::Djibouti,
            CountryAlpha2::DM => Self::Dominica,
            CountryAlpha2::DO => Self::DominicanRepublic,
            CountryAlpha2::EC => Self::Ecuador,
            CountryAlpha2::EG => Self::Egypt,
            CountryAlpha2::SV => Self::ElSalvador,
            CountryAlpha2::GQ => Self::EquatorialGuinea,
            CountryAlpha2::ER => Self::Eritrea,
            CountryAlpha2::EE => Self::Estonia,
            CountryAlpha2::ET => Self::Ethiopia,
            CountryAlpha2::FK => Self::FalklandIslandsMalvinas,
            CountryAlpha2::FO => Self::FaroeIslands,
            CountryAlpha2::FJ => Self::Fiji,
            CountryAlpha2::FI => Self::Finland,
            CountryAlpha2::FR => Self::France,
            CountryAlpha2::GF => Self::FrenchGuiana,
            CountryAlpha2::PF => Self::FrenchPolynesia,
            CountryAlpha2::TF => Self::FrenchSouthernTerritories,
            CountryAlpha2::GA => Self::Gabon,
            CountryAlpha2::GM => Self::Gambia,
            CountryAlpha2::GE => Self::Georgia,
            CountryAlpha2::DE => Self::Germany,
            CountryAlpha2::GH => Self::Ghana,
            CountryAlpha2::GI => Self::Gibraltar,
            CountryAlpha2::GR => Self::Greece,
            CountryAlpha2::GL => Self::Greenland,
            CountryAlpha2::GD => Self::Grenada,
            CountryAlpha2::GP => Self::Guadeloupe,
            CountryAlpha2::GU => Self::Guam,
            CountryAlpha2::GT => Self::Guatemala,
            CountryAlpha2::GG => Self::Guernsey,
            CountryAlpha2::GN => Self::Guinea,
            CountryAlpha2::GW => Self::GuineaBissau,
            CountryAlpha2::GY => Self::Guyana,
            CountryAlpha2::HT => Self::Haiti,
            CountryAlpha2::HM => Self::HeardIslandAndMcDonaldIslands,
            CountryAlpha2::VA => Self::HolySee,
            CountryAlpha2::HN => Self::Honduras,
            CountryAlpha2::HK => Self::HongKong,
            CountryAlpha2::HU => Self::Hungary,
            CountryAlpha2::IS => Self::Iceland,
            CountryAlpha2::IN => Self::India,
            CountryAlpha2::ID => Self::Indonesia,
            CountryAlpha2::IR => Self::IranIslamicRepublic,
            CountryAlpha2::IQ => Self::Iraq,
            CountryAlpha2::IE => Self::Ireland,
            CountryAlpha2::IM => Self::IsleOfMan,
            CountryAlpha2::IL => Self::Israel,
            CountryAlpha2::IT => Self::Italy,
            CountryAlpha2::JM => Self::Jamaica,
            CountryAlpha2::JP => Self::Japan,
            CountryAlpha2::JE => Self::Jersey,
            CountryAlpha2::JO => Self::Jordan,
            CountryAlpha2::KZ => Self::Kazakhstan,
            CountryAlpha2::KE => Self::Kenya,
            CountryAlpha2::KI => Self::Kiribati,
            CountryAlpha2::KP => Self::KoreaDemocraticPeoplesRepublic,
            CountryAlpha2::KR => Self::KoreaRepublic,
            CountryAlpha2::KW => Self::Kuwait,
            CountryAlpha2::KG => Self::Kyrgyzstan,
            CountryAlpha2::LA => Self::LaoPeoplesDemocraticRepublic,
            CountryAlpha2::LV => Self::Latvia,
            CountryAlpha2::LB => Self::Lebanon,
            CountryAlpha2::LS => Self::Lesotho,
            CountryAlpha2::LR => Self::Liberia,
            CountryAlpha2::LY => Self::Libya,
            CountryAlpha2::LI => Self::Liechtenstein,
            CountryAlpha2::LT => Self::Lithuania,
            CountryAlpha2::LU => Self::Luxembourg,
            CountryAlpha2::MO => Self::Macao,
            CountryAlpha2::MK => Self::MacedoniaTheFormerYugoslavRepublic,
            CountryAlpha2::MG => Self::Madagascar,
            CountryAlpha2::MW => Self::Malawi,
            CountryAlpha2::MY => Self::Malaysia,
            CountryAlpha2::MV => Self::Maldives,
            CountryAlpha2::ML => Self::Mali,
            CountryAlpha2::MT => Self::Malta,
            CountryAlpha2::MH => Self::MarshallIslands,
            CountryAlpha2::MQ => Self::Martinique,
            CountryAlpha2::MR => Self::Mauritania,
            CountryAlpha2::MU => Self::Mauritius,
            CountryAlpha2::YT => Self::Mayotte,
            CountryAlpha2::MX => Self::Mexico,
            CountryAlpha2::FM => Self::MicronesiaFederatedStates,
            CountryAlpha2::MD => Self::MoldovaRepublic,
            CountryAlpha2::MC => Self::Monaco,
            CountryAlpha2::MN => Self::Mongolia,
            CountryAlpha2::ME => Self::Montenegro,
            CountryAlpha2::MS => Self::Montserrat,
            CountryAlpha2::MA => Self::Morocco,
            CountryAlpha2::MZ => Self::Mozambique,
            CountryAlpha2::MM => Self::Myanmar,
            CountryAlpha2::NA => Self::Namibia,
            CountryAlpha2::NR => Self::Nauru,
            CountryAlpha2::NP => Self::Nepal,
            CountryAlpha2::NL => Self::Netherlands,
            CountryAlpha2::NC => Self::NewCaledonia,
            CountryAlpha2::NZ => Self::NewZealand,
            CountryAlpha2::NI => Self::Nicaragua,
            CountryAlpha2::NE => Self::Niger,
            CountryAlpha2::NG => Self::Nigeria,
            CountryAlpha2::NU => Self::Niue,
            CountryAlpha2::NF => Self::NorfolkIsland,
            CountryAlpha2::MP => Self::NorthernMarianaIslands,
            CountryAlpha2::NO => Self::Norway,
            CountryAlpha2::OM => Self::Oman,
            CountryAlpha2::PK => Self::Pakistan,
            CountryAlpha2::PW => Self::Palau,
            CountryAlpha2::PS => Self::PalestineState,
            CountryAlpha2::PA => Self::Panama,
            CountryAlpha2::PG => Self::PapuaNewGuinea,
            CountryAlpha2::PY => Self::Paraguay,
            CountryAlpha2::PE => Self::Peru,
            CountryAlpha2::PH => Self::Philippines,
            CountryAlpha2::PN => Self::Pitcairn,
            CountryAlpha2::PL => Self::Poland,
            CountryAlpha2::PT => Self::Portugal,
            CountryAlpha2::PR => Self::PuertoRico,
            CountryAlpha2::QA => Self::Qatar,
            CountryAlpha2::RE => Self::Reunion,
            CountryAlpha2::RO => Self::Romania,
            CountryAlpha2::RU => Self::RussianFederation,
            CountryAlpha2::RW => Self::Rwanda,
            CountryAlpha2::BL => Self::SaintBarthelemy,
            CountryAlpha2::SH => Self::SaintHelenaAscensionAndTristandaCunha,
            CountryAlpha2::KN => Self::SaintKittsAndNevis,
            CountryAlpha2::LC => Self::SaintLucia,
            CountryAlpha2::MF => Self::SaintMartinFrenchpart,
            CountryAlpha2::PM => Self::SaintPierreAndMiquelon,
            CountryAlpha2::VC => Self::SaintVincentAndTheGrenadines,
            CountryAlpha2::WS => Self::Samoa,
            CountryAlpha2::SM => Self::SanMarino,
            CountryAlpha2::ST => Self::SaoTomeAndPrincipe,
            CountryAlpha2::SA => Self::SaudiArabia,
            CountryAlpha2::SN => Self::Senegal,
            CountryAlpha2::RS => Self::Serbia,
            CountryAlpha2::SC => Self::Seychelles,
            CountryAlpha2::SL => Self::SierraLeone,
            CountryAlpha2::SG => Self::Singapore,
            CountryAlpha2::SX => Self::SintMaartenDutchpart,
            CountryAlpha2::SK => Self::Slovakia,
            CountryAlpha2::SI => Self::Slovenia,
            CountryAlpha2::SB => Self::SolomonIslands,
            CountryAlpha2::SO => Self::Somalia,
            CountryAlpha2::ZA => Self::SouthAfrica,
            CountryAlpha2::GS => Self::SouthGeorgiaAndTheSouthSandwichIslands,
            CountryAlpha2::SS => Self::SouthSudan,
            CountryAlpha2::ES => Self::Spain,
            CountryAlpha2::LK => Self::SriLanka,
            CountryAlpha2::SD => Self::Sudan,
            CountryAlpha2::SR => Self::Suriname,
            CountryAlpha2::SJ => Self::SvalbardAndJanMayen,
            CountryAlpha2::SZ => Self::Swaziland,
            CountryAlpha2::SE => Self::Sweden,
            CountryAlpha2::CH => Self::Switzerland,
            CountryAlpha2::SY => Self::SyrianArabRepublic,
            CountryAlpha2::TW => Self::TaiwanProvinceOfChina,
            CountryAlpha2::TJ => Self::Tajikistan,
            CountryAlpha2::TZ => Self::TanzaniaUnitedRepublic,
            CountryAlpha2::TH => Self::Thailand,
            CountryAlpha2::TL => Self::TimorLeste,
            CountryAlpha2::TG => Self::Togo,
            CountryAlpha2::TK => Self::Tokelau,
            CountryAlpha2::TO => Self::Tonga,
            CountryAlpha2::TT => Self::TrinidadAndTobago,
            CountryAlpha2::TN => Self::Tunisia,
            CountryAlpha2::TR => Self::Turkey,
            CountryAlpha2::TM => Self::Turkmenistan,
            CountryAlpha2::TC => Self::TurksAndCaicosIslands,
            CountryAlpha2::TV => Self::Tuvalu,
            CountryAlpha2::UG => Self::Uganda,
            CountryAlpha2::UA => Self::Ukraine,
            CountryAlpha2::AE => Self::UnitedArabEmirates,
            CountryAlpha2::GB => Self::UnitedKingdomOfGreatBritainAndNorthernIreland,
            CountryAlpha2::US => Self::UnitedStatesOfAmerica,
            CountryAlpha2::UM => Self::UnitedStatesMinorOutlyingIslands,
            CountryAlpha2::UY => Self::Uruguay,
            CountryAlpha2::UZ => Self::Uzbekistan,
            CountryAlpha2::VU => Self::Vanuatu,
            CountryAlpha2::VE => Self::VenezuelaBolivarianRepublic,
            CountryAlpha2::VN => Self::Vietnam,
            CountryAlpha2::VG => Self::VirginIslandsBritish,
            CountryAlpha2::VI => Self::VirginIslandsUS,
            CountryAlpha2::WF => Self::WallisAndFutuna,
            CountryAlpha2::EH => Self::WesternSahara,
            CountryAlpha2::YE => Self::Yemen,
            CountryAlpha2::ZM => Self::Zambia,
            CountryAlpha2::ZW => Self::Zimbabwe,
        }
    }
    pub const fn to_alpha2(self) -> CountryAlpha2 {
        match self {
            Self::Afghanistan => CountryAlpha2::AF,
            Self::AlandIslands => CountryAlpha2::AX,
            Self::Albania => CountryAlpha2::AL,
            Self::Algeria => CountryAlpha2::DZ,
            Self::AmericanSamoa => CountryAlpha2::AS,
            Self::Andorra => CountryAlpha2::AD,
            Self::Angola => CountryAlpha2::AO,
            Self::Anguilla => CountryAlpha2::AI,
            Self::Antarctica => CountryAlpha2::AQ,
            Self::AntiguaAndBarbuda => CountryAlpha2::AG,
            Self::Argentina => CountryAlpha2::AR,
            Self::Armenia => CountryAlpha2::AM,
            Self::Aruba => CountryAlpha2::AW,
            Self::Australia => CountryAlpha2::AU,
            Self::Austria => CountryAlpha2::AT,
            Self::Azerbaijan => CountryAlpha2::AZ,
            Self::Bahamas => CountryAlpha2::BS,
            Self::Bahrain => CountryAlpha2::BH,
            Self::Bangladesh => CountryAlpha2::BD,
            Self::Barbados => CountryAlpha2::BB,
            Self::Belarus => CountryAlpha2::BY,
            Self::Belgium => CountryAlpha2::BE,
            Self::Belize => CountryAlpha2::BZ,
            Self::Benin => CountryAlpha2::BJ,
            Self::Bermuda => CountryAlpha2::BM,
            Self::Bhutan => CountryAlpha2::BT,
            Self::BoliviaPlurinationalState => CountryAlpha2::BO,
            Self::BonaireSintEustatiusAndSaba => CountryAlpha2::BQ,
            Self::BosniaAndHerzegovina => CountryAlpha2::BA,
            Self::Botswana => CountryAlpha2::BW,
            Self::BouvetIsland => CountryAlpha2::BV,
            Self::Brazil => CountryAlpha2::BR,
            Self::BritishIndianOceanTerritory => CountryAlpha2::IO,
            Self::BruneiDarussalam => CountryAlpha2::BN,
            Self::Bulgaria => CountryAlpha2::BG,
            Self::BurkinaFaso => CountryAlpha2::BF,
            Self::Burundi => CountryAlpha2::BI,
            Self::CaboVerde => CountryAlpha2::CV,
            Self::Cambodia => CountryAlpha2::KH,
            Self::Cameroon => CountryAlpha2::CM,
            Self::Canada => CountryAlpha2::CA,
            Self::CaymanIslands => CountryAlpha2::KY,
            Self::CentralAfricanRepublic => CountryAlpha2::CF,
            Self::Chad => CountryAlpha2::TD,
            Self::Chile => CountryAlpha2::CL,
            Self::China => CountryAlpha2::CN,
            Self::ChristmasIsland => CountryAlpha2::CX,
            Self::CocosKeelingIslands => CountryAlpha2::CC,
            Self::Colombia => CountryAlpha2::CO,
            Self::Comoros => CountryAlpha2::KM,
            Self::Congo => CountryAlpha2::CG,
            Self::CongoDemocraticRepublic => CountryAlpha2::CD,
            Self::CookIslands => CountryAlpha2::CK,
            Self::CostaRica => CountryAlpha2::CR,
            Self::CotedIvoire => CountryAlpha2::CI,
            Self::Croatia => CountryAlpha2::HR,
            Self::Cuba => CountryAlpha2::CU,
            Self::Curacao => CountryAlpha2::CW,
            Self::Cyprus => CountryAlpha2::CY,
            Self::Czechia => CountryAlpha2::CZ,
            Self::Denmark => CountryAlpha2::DK,
            Self::Djibouti => CountryAlpha2::DJ,
            Self::Dominica => CountryAlpha2::DM,
            Self::DominicanRepublic => CountryAlpha2::DO,
            Self::Ecuador => CountryAlpha2::EC,
            Self::Egypt => CountryAlpha2::EG,
            Self::ElSalvador => CountryAlpha2::SV,
            Self::EquatorialGuinea => CountryAlpha2::GQ,
            Self::Eritrea => CountryAlpha2::ER,
            Self::Estonia => CountryAlpha2::EE,
            Self::Ethiopia => CountryAlpha2::ET,
            Self::FalklandIslandsMalvinas => CountryAlpha2::FK,
            Self::FaroeIslands => CountryAlpha2::FO,
            Self::Fiji => CountryAlpha2::FJ,
            Self::Finland => CountryAlpha2::FI,
            Self::France => CountryAlpha2::FR,
            Self::FrenchGuiana => CountryAlpha2::GF,
            Self::FrenchPolynesia => CountryAlpha2::PF,
            Self::FrenchSouthernTerritories => CountryAlpha2::TF,
            Self::Gabon => CountryAlpha2::GA,
            Self::Gambia => CountryAlpha2::GM,
            Self::Georgia => CountryAlpha2::GE,
            Self::Germany => CountryAlpha2::DE,
            Self::Ghana => CountryAlpha2::GH,
            Self::Gibraltar => CountryAlpha2::GI,
            Self::Greece => CountryAlpha2::GR,
            Self::Greenland => CountryAlpha2::GL,
            Self::Grenada => CountryAlpha2::GD,
            Self::Guadeloupe => CountryAlpha2::GP,
            Self::Guam => CountryAlpha2::GU,
            Self::Guatemala => CountryAlpha2::GT,
            Self::Guernsey => CountryAlpha2::GG,
            Self::Guinea => CountryAlpha2::GN,
            Self::GuineaBissau => CountryAlpha2::GW,
            Self::Guyana => CountryAlpha2::GY,
            Self::Haiti => CountryAlpha2::HT,
            Self::HeardIslandAndMcDonaldIslands => CountryAlpha2::HM,
            Self::HolySee => CountryAlpha2::VA,
            Self::Honduras => CountryAlpha2::HN,
            Self::HongKong => CountryAlpha2::HK,
            Self::Hungary => CountryAlpha2::HU,
            Self::Iceland => CountryAlpha2::IS,
            Self::India => CountryAlpha2::IN,
            Self::Indonesia => CountryAlpha2::ID,
            Self::IranIslamicRepublic => CountryAlpha2::IR,
            Self::Iraq => CountryAlpha2::IQ,
            Self::Ireland => CountryAlpha2::IE,
            Self::IsleOfMan => CountryAlpha2::IM,
            Self::Israel => CountryAlpha2::IL,
            Self::Italy => CountryAlpha2::IT,
            Self::Jamaica => CountryAlpha2::JM,
            Self::Japan => CountryAlpha2::JP,
            Self::Jersey => CountryAlpha2::JE,
            Self::Jordan => CountryAlpha2::JO,
            Self::Kazakhstan => CountryAlpha2::KZ,
            Self::Kenya => CountryAlpha2::KE,
            Self::Kiribati => CountryAlpha2::KI,
            Self::KoreaDemocraticPeoplesRepublic => CountryAlpha2::KP,
            Self::KoreaRepublic => CountryAlpha2::KR,
            Self::Kuwait => CountryAlpha2::KW,
            Self::Kyrgyzstan => CountryAlpha2::KG,
            Self::LaoPeoplesDemocraticRepublic => CountryAlpha2::LA,
            Self::Latvia => CountryAlpha2::LV,
            Self::Lebanon => CountryAlpha2::LB,
            Self::Lesotho => CountryAlpha2::LS,
            Self::Liberia => CountryAlpha2::LR,
            Self::Libya => CountryAlpha2::LY,
            Self::Liechtenstein => CountryAlpha2::LI,
            Self::Lithuania => CountryAlpha2::LT,
            Self::Luxembourg => CountryAlpha2::LU,
            Self::Macao => CountryAlpha2::MO,
            Self::MacedoniaTheFormerYugoslavRepublic => CountryAlpha2::MK,
            Self::Madagascar => CountryAlpha2::MG,
            Self::Malawi => CountryAlpha2::MW,
            Self::Malaysia => CountryAlpha2::MY,
            Self::Maldives => CountryAlpha2::MV,
            Self::Mali => CountryAlpha2::ML,
            Self::Malta => CountryAlpha2::MT,
            Self::MarshallIslands => CountryAlpha2::MH,
            Self::Martinique => CountryAlpha2::MQ,
            Self::Mauritania => CountryAlpha2::MR,
            Self::Mauritius => CountryAlpha2::MU,
            Self::Mayotte => CountryAlpha2::YT,
            Self::Mexico => CountryAlpha2::MX,
            Self::MicronesiaFederatedStates => CountryAlpha2::FM,
            Self::MoldovaRepublic => CountryAlpha2::MD,
            Self::Monaco => CountryAlpha2::MC,
            Self::Mongolia => CountryAlpha2::MN,
            Self::Montenegro => CountryAlpha2::ME,
            Self::Montserrat => CountryAlpha2::MS,
            Self::Morocco => CountryAlpha2::MA,
            Self::Mozambique => CountryAlpha2::MZ,
            Self::Myanmar => CountryAlpha2::MM,
            Self::Namibia => CountryAlpha2::NA,
            Self::Nauru => CountryAlpha2::NR,
            Self::Nepal => CountryAlpha2::NP,
            Self::Netherlands => CountryAlpha2::NL,
            Self::NewCaledonia => CountryAlpha2::NC,
            Self::NewZealand => CountryAlpha2::NZ,
            Self::Nicaragua => CountryAlpha2::NI,
            Self::Niger => CountryAlpha2::NE,
            Self::Nigeria => CountryAlpha2::NG,
            Self::Niue => CountryAlpha2::NU,
            Self::NorfolkIsland => CountryAlpha2::NF,
            Self::NorthernMarianaIslands => CountryAlpha2::MP,
            Self::Norway => CountryAlpha2::NO,
            Self::Oman => CountryAlpha2::OM,
            Self::Pakistan => CountryAlpha2::PK,
            Self::Palau => CountryAlpha2::PW,
            Self::PalestineState => CountryAlpha2::PS,
            Self::Panama => CountryAlpha2::PA,
            Self::PapuaNewGuinea => CountryAlpha2::PG,
            Self::Paraguay => CountryAlpha2::PY,
            Self::Peru => CountryAlpha2::PE,
            Self::Philippines => CountryAlpha2::PH,
            Self::Pitcairn => CountryAlpha2::PN,
            Self::Poland => CountryAlpha2::PL,
            Self::Portugal => CountryAlpha2::PT,
            Self::PuertoRico => CountryAlpha2::PR,
            Self::Qatar => CountryAlpha2::QA,
            Self::Reunion => CountryAlpha2::RE,
            Self::Romania => CountryAlpha2::RO,
            Self::RussianFederation => CountryAlpha2::RU,
            Self::Rwanda => CountryAlpha2::RW,
            Self::SaintBarthelemy => CountryAlpha2::BL,
            Self::SaintHelenaAscensionAndTristandaCunha => CountryAlpha2::SH,
            Self::SaintKittsAndNevis => CountryAlpha2::KN,
            Self::SaintLucia => CountryAlpha2::LC,
            Self::SaintMartinFrenchpart => CountryAlpha2::MF,
            Self::SaintPierreAndMiquelon => CountryAlpha2::PM,
            Self::SaintVincentAndTheGrenadines => CountryAlpha2::VC,
            Self::Samoa => CountryAlpha2::WS,
            Self::SanMarino => CountryAlpha2::SM,
            Self::SaoTomeAndPrincipe => CountryAlpha2::ST,
            Self::SaudiArabia => CountryAlpha2::SA,
            Self::Senegal => CountryAlpha2::SN,
            Self::Serbia => CountryAlpha2::RS,
            Self::Seychelles => CountryAlpha2::SC,
            Self::SierraLeone => CountryAlpha2::SL,
            Self::Singapore => CountryAlpha2::SG,
            Self::SintMaartenDutchpart => CountryAlpha2::SX,
            Self::Slovakia => CountryAlpha2::SK,
            Self::Slovenia => CountryAlpha2::SI,
            Self::SolomonIslands => CountryAlpha2::SB,
            Self::Somalia => CountryAlpha2::SO,
            Self::SouthAfrica => CountryAlpha2::ZA,
            Self::SouthGeorgiaAndTheSouthSandwichIslands => CountryAlpha2::GS,
            Self::SouthSudan => CountryAlpha2::SS,
            Self::Spain => CountryAlpha2::ES,
            Self::SriLanka => CountryAlpha2::LK,
            Self::Sudan => CountryAlpha2::SD,
            Self::Suriname => CountryAlpha2::SR,
            Self::SvalbardAndJanMayen => CountryAlpha2::SJ,
            Self::Swaziland => CountryAlpha2::SZ,
            Self::Sweden => CountryAlpha2::SE,
            Self::Switzerland => CountryAlpha2::CH,
            Self::SyrianArabRepublic => CountryAlpha2::SY,
            Self::TaiwanProvinceOfChina => CountryAlpha2::TW,
            Self::Tajikistan => CountryAlpha2::TJ,
            Self::TanzaniaUnitedRepublic => CountryAlpha2::TZ,
            Self::Thailand => CountryAlpha2::TH,
            Self::TimorLeste => CountryAlpha2::TL,
            Self::Togo => CountryAlpha2::TG,
            Self::Tokelau => CountryAlpha2::TK,
            Self::Tonga => CountryAlpha2::TO,
            Self::TrinidadAndTobago => CountryAlpha2::TT,
            Self::Tunisia => CountryAlpha2::TN,
            Self::Turkey => CountryAlpha2::TR,
            Self::Turkmenistan => CountryAlpha2::TM,
            Self::TurksAndCaicosIslands => CountryAlpha2::TC,
            Self::Tuvalu => CountryAlpha2::TV,
            Self::Uganda => CountryAlpha2::UG,
            Self::Ukraine => CountryAlpha2::UA,
            Self::UnitedArabEmirates => CountryAlpha2::AE,
            Self::UnitedKingdomOfGreatBritainAndNorthernIreland => CountryAlpha2::GB,
            Self::UnitedStatesOfAmerica => CountryAlpha2::US,
            Self::UnitedStatesMinorOutlyingIslands => CountryAlpha2::UM,
            Self::Uruguay => CountryAlpha2::UY,
            Self::Uzbekistan => CountryAlpha2::UZ,
            Self::Vanuatu => CountryAlpha2::VU,
            Self::VenezuelaBolivarianRepublic => CountryAlpha2::VE,
            Self::Vietnam => CountryAlpha2::VN,
            Self::VirginIslandsBritish => CountryAlpha2::VG,
            Self::VirginIslandsUS => CountryAlpha2::VI,
            Self::WallisAndFutuna => CountryAlpha2::WF,
            Self::WesternSahara => CountryAlpha2::EH,
            Self::Yemen => CountryAlpha2::YE,
            Self::Zambia => CountryAlpha2::ZM,
            Self::Zimbabwe => CountryAlpha2::ZW,
        }
    }
    pub const fn from_alpha3(code: CountryAlpha3) -> Self {
        match code {
            CountryAlpha3::AFG => Self::Afghanistan,
            CountryAlpha3::ALA => Self::AlandIslands,
            CountryAlpha3::ALB => Self::Albania,
            CountryAlpha3::DZA => Self::Algeria,
            CountryAlpha3::ASM => Self::AmericanSamoa,
            CountryAlpha3::AND => Self::Andorra,
            CountryAlpha3::AGO => Self::Angola,
            CountryAlpha3::AIA => Self::Anguilla,
            CountryAlpha3::ATA => Self::Antarctica,
            CountryAlpha3::ATG => Self::AntiguaAndBarbuda,
            CountryAlpha3::ARG => Self::Argentina,
            CountryAlpha3::ARM => Self::Armenia,
            CountryAlpha3::ABW => Self::Aruba,
            CountryAlpha3::AUS => Self::Australia,
            CountryAlpha3::AUT => Self::Austria,
            CountryAlpha3::AZE => Self::Azerbaijan,
            CountryAlpha3::BHS => Self::Bahamas,
            CountryAlpha3::BHR => Self::Bahrain,
            CountryAlpha3::BGD => Self::Bangladesh,
            CountryAlpha3::BRB => Self::Barbados,
            CountryAlpha3::BLR => Self::Belarus,
            CountryAlpha3::BEL => Self::Belgium,
            CountryAlpha3::BLZ => Self::Belize,
            CountryAlpha3::BEN => Self::Benin,
            CountryAlpha3::BMU => Self::Bermuda,
            CountryAlpha3::BTN => Self::Bhutan,
            CountryAlpha3::BOL => Self::BoliviaPlurinationalState,
            CountryAlpha3::BES => Self::BonaireSintEustatiusAndSaba,
            CountryAlpha3::BIH => Self::BosniaAndHerzegovina,
            CountryAlpha3::BWA => Self::Botswana,
            CountryAlpha3::BVT => Self::BouvetIsland,
            CountryAlpha3::BRA => Self::Brazil,
            CountryAlpha3::IOT => Self::BritishIndianOceanTerritory,
            CountryAlpha3::BRN => Self::BruneiDarussalam,
            CountryAlpha3::BGR => Self::Bulgaria,
            CountryAlpha3::BFA => Self::BurkinaFaso,
            CountryAlpha3::BDI => Self::Burundi,
            CountryAlpha3::CPV => Self::CaboVerde,
            CountryAlpha3::KHM => Self::Cambodia,
            CountryAlpha3::CMR => Self::Cameroon,
            CountryAlpha3::CAN => Self::Canada,
            CountryAlpha3::CYM => Self::CaymanIslands,
            CountryAlpha3::CAF => Self::CentralAfricanRepublic,
            CountryAlpha3::TCD => Self::Chad,
            CountryAlpha3::CHL => Self::Chile,
            CountryAlpha3::CHN => Self::China,
            CountryAlpha3::CXR => Self::ChristmasIsland,
            CountryAlpha3::CCK => Self::CocosKeelingIslands,
            CountryAlpha3::COL => Self::Colombia,
            CountryAlpha3::COM => Self::Comoros,
            CountryAlpha3::COG => Self::Congo,
            CountryAlpha3::COD => Self::CongoDemocraticRepublic,
            CountryAlpha3::COK => Self::CookIslands,
            CountryAlpha3::CRI => Self::CostaRica,
            CountryAlpha3::CIV => Self::CotedIvoire,
            CountryAlpha3::HRV => Self::Croatia,
            CountryAlpha3::CUB => Self::Cuba,
            CountryAlpha3::CUW => Self::Curacao,
            CountryAlpha3::CYP => Self::Cyprus,
            CountryAlpha3::CZE => Self::Czechia,
            CountryAlpha3::DNK => Self::Denmark,
            CountryAlpha3::DJI => Self::Djibouti,
            CountryAlpha3::DMA => Self::Dominica,
            CountryAlpha3::DOM => Self::DominicanRepublic,
            CountryAlpha3::ECU => Self::Ecuador,
            CountryAlpha3::EGY => Self::Egypt,
            CountryAlpha3::SLV => Self::ElSalvador,
            CountryAlpha3::GNQ => Self::EquatorialGuinea,
            CountryAlpha3::ERI => Self::Eritrea,
            CountryAlpha3::EST => Self::Estonia,
            CountryAlpha3::ETH => Self::Ethiopia,
            CountryAlpha3::FLK => Self::FalklandIslandsMalvinas,
            CountryAlpha3::FRO => Self::FaroeIslands,
            CountryAlpha3::FJI => Self::Fiji,
            CountryAlpha3::FIN => Self::Finland,
            CountryAlpha3::FRA => Self::France,
            CountryAlpha3::GUF => Self::FrenchGuiana,
            CountryAlpha3::PYF => Self::FrenchPolynesia,
            CountryAlpha3::ATF => Self::FrenchSouthernTerritories,
            CountryAlpha3::GAB => Self::Gabon,
            CountryAlpha3::GMB => Self::Gambia,
            CountryAlpha3::GEO => Self::Georgia,
            CountryAlpha3::DEU => Self::Germany,
            CountryAlpha3::GHA => Self::Ghana,
            CountryAlpha3::GIB => Self::Gibraltar,
            CountryAlpha3::GRC => Self::Greece,
            CountryAlpha3::GRL => Self::Greenland,
            CountryAlpha3::GRD => Self::Grenada,
            CountryAlpha3::GLP => Self::Guadeloupe,
            CountryAlpha3::GUM => Self::Guam,
            CountryAlpha3::GTM => Self::Guatemala,
            CountryAlpha3::GGY => Self::Guernsey,
            CountryAlpha3::GIN => Self::Guinea,
            CountryAlpha3::GNB => Self::GuineaBissau,
            CountryAlpha3::GUY => Self::Guyana,
            CountryAlpha3::HTI => Self::Haiti,
            CountryAlpha3::HMD => Self::HeardIslandAndMcDonaldIslands,
            CountryAlpha3::VAT => Self::HolySee,
            CountryAlpha3::HND => Self::Honduras,
            CountryAlpha3::HKG => Self::HongKong,
            CountryAlpha3::HUN => Self::Hungary,
            CountryAlpha3::ISL => Self::Iceland,
            CountryAlpha3::IND => Self::India,
            CountryAlpha3::IDN => Self::Indonesia,
            CountryAlpha3::IRN => Self::IranIslamicRepublic,
            CountryAlpha3::IRQ => Self::Iraq,
            CountryAlpha3::IRL => Self::Ireland,
            CountryAlpha3::IMN => Self::IsleOfMan,
            CountryAlpha3::ISR => Self::Israel,
            CountryAlpha3::ITA => Self::Italy,
            CountryAlpha3::JAM => Self::Jamaica,
            CountryAlpha3::JPN => Self::Japan,
            CountryAlpha3::JEY => Self::Jersey,
            CountryAlpha3::JOR => Self::Jordan,
            CountryAlpha3::KAZ => Self::Kazakhstan,
            CountryAlpha3::KEN => Self::Kenya,
            CountryAlpha3::KIR => Self::Kiribati,
            CountryAlpha3::PRK => Self::KoreaDemocraticPeoplesRepublic,
            CountryAlpha3::KOR => Self::KoreaRepublic,
            CountryAlpha3::KWT => Self::Kuwait,
            CountryAlpha3::KGZ => Self::Kyrgyzstan,
            CountryAlpha3::LAO => Self::LaoPeoplesDemocraticRepublic,
            CountryAlpha3::LVA => Self::Latvia,
            CountryAlpha3::LBN => Self::Lebanon,
            CountryAlpha3::LSO => Self::Lesotho,
            CountryAlpha3::LBR => Self::Liberia,
            CountryAlpha3::LBY => Self::Libya,
            CountryAlpha3::LIE => Self::Liechtenstein,
            CountryAlpha3::LTU => Self::Lithuania,
            CountryAlpha3::LUX => Self::Luxembourg,
            CountryAlpha3::MAC => Self::Macao,
            CountryAlpha3::MKD => Self::MacedoniaTheFormerYugoslavRepublic,
            CountryAlpha3::MDG => Self::Madagascar,
            CountryAlpha3::MWI => Self::Malawi,
            CountryAlpha3::MYS => Self::Malaysia,
            CountryAlpha3::MDV => Self::Maldives,
            CountryAlpha3::MLI => Self::Mali,
            CountryAlpha3::MLT => Self::Malta,
            CountryAlpha3::MHL => Self::MarshallIslands,
            CountryAlpha3::MTQ => Self::Martinique,
            CountryAlpha3::MRT => Self::Mauritania,
            CountryAlpha3::MUS => Self::Mauritius,
            CountryAlpha3::MYT => Self::Mayotte,
            CountryAlpha3::MEX => Self::Mexico,
            CountryAlpha3::FSM => Self::MicronesiaFederatedStates,
            CountryAlpha3::MDA => Self::MoldovaRepublic,
            CountryAlpha3::MCO => Self::Monaco,
            CountryAlpha3::MNG => Self::Mongolia,
            CountryAlpha3::MNE => Self::Montenegro,
            CountryAlpha3::MSR => Self::Montserrat,
            CountryAlpha3::MAR => Self::Morocco,
            CountryAlpha3::MOZ => Self::Mozambique,
            CountryAlpha3::MMR => Self::Myanmar,
            CountryAlpha3::NAM => Self::Namibia,
            CountryAlpha3::NRU => Self::Nauru,
            CountryAlpha3::NPL => Self::Nepal,
            CountryAlpha3::NLD => Self::Netherlands,
            CountryAlpha3::NCL => Self::NewCaledonia,
            CountryAlpha3::NZL => Self::NewZealand,
            CountryAlpha3::NIC => Self::Nicaragua,
            CountryAlpha3::NER => Self::Niger,
            CountryAlpha3::NGA => Self::Nigeria,
            CountryAlpha3::NIU => Self::Niue,
            CountryAlpha3::NFK => Self::NorfolkIsland,
            CountryAlpha3::MNP => Self::NorthernMarianaIslands,
            CountryAlpha3::NOR => Self::Norway,
            CountryAlpha3::OMN => Self::Oman,
            CountryAlpha3::PAK => Self::Pakistan,
            CountryAlpha3::PLW => Self::Palau,
            CountryAlpha3::PSE => Self::PalestineState,
            CountryAlpha3::PAN => Self::Panama,
            CountryAlpha3::PNG => Self::PapuaNewGuinea,
            CountryAlpha3::PRY => Self::Paraguay,
            CountryAlpha3::PER => Self::Peru,
            CountryAlpha3::PHL => Self::Philippines,
            CountryAlpha3::PCN => Self::Pitcairn,
            CountryAlpha3::POL => Self::Poland,
            CountryAlpha3::PRT => Self::Portugal,
            CountryAlpha3::PRI => Self::PuertoRico,
            CountryAlpha3::QAT => Self::Qatar,
            CountryAlpha3::REU => Self::Reunion,
            CountryAlpha3::ROU => Self::Romania,
            CountryAlpha3::RUS => Self::RussianFederation,
            CountryAlpha3::RWA => Self::Rwanda,
            CountryAlpha3::BLM => Self::SaintBarthelemy,
            CountryAlpha3::SHN => Self::SaintHelenaAscensionAndTristandaCunha,
            CountryAlpha3::KNA => Self::SaintKittsAndNevis,
            CountryAlpha3::LCA => Self::SaintLucia,
            CountryAlpha3::MAF => Self::SaintMartinFrenchpart,
            CountryAlpha3::SPM => Self::SaintPierreAndMiquelon,
            CountryAlpha3::VCT => Self::SaintVincentAndTheGrenadines,
            CountryAlpha3::WSM => Self::Samoa,
            CountryAlpha3::SMR => Self::SanMarino,
            CountryAlpha3::STP => Self::SaoTomeAndPrincipe,
            CountryAlpha3::SAU => Self::SaudiArabia,
            CountryAlpha3::SEN => Self::Senegal,
            CountryAlpha3::SRB => Self::Serbia,
            CountryAlpha3::SYC => Self::Seychelles,
            CountryAlpha3::SLE => Self::SierraLeone,
            CountryAlpha3::SGP => Self::Singapore,
            CountryAlpha3::SXM => Self::SintMaartenDutchpart,
            CountryAlpha3::SVK => Self::Slovakia,
            CountryAlpha3::SVN => Self::Slovenia,
            CountryAlpha3::SLB => Self::SolomonIslands,
            CountryAlpha3::SOM => Self::Somalia,
            CountryAlpha3::ZAF => Self::SouthAfrica,
            CountryAlpha3::SGS => Self::SouthGeorgiaAndTheSouthSandwichIslands,
            CountryAlpha3::SSD => Self::SouthSudan,
            CountryAlpha3::ESP => Self::Spain,
            CountryAlpha3::LKA => Self::SriLanka,
            CountryAlpha3::SDN => Self::Sudan,
            CountryAlpha3::SUR => Self::Suriname,
            CountryAlpha3::SJM => Self::SvalbardAndJanMayen,
            CountryAlpha3::SWZ => Self::Swaziland,
            CountryAlpha3::SWE => Self::Sweden,
            CountryAlpha3::CHE => Self::Switzerland,
            CountryAlpha3::SYR => Self::SyrianArabRepublic,
            CountryAlpha3::TWN => Self::TaiwanProvinceOfChina,
            CountryAlpha3::TJK => Self::Tajikistan,
            CountryAlpha3::TZA => Self::TanzaniaUnitedRepublic,
            CountryAlpha3::THA => Self::Thailand,
            CountryAlpha3::TLS => Self::TimorLeste,
            CountryAlpha3::TGO => Self::Togo,
            CountryAlpha3::TKL => Self::Tokelau,
            CountryAlpha3::TON => Self::Tonga,
            CountryAlpha3::TTO => Self::TrinidadAndTobago,
            CountryAlpha3::TUN => Self::Tunisia,
            CountryAlpha3::TUR => Self::Turkey,
            CountryAlpha3::TKM => Self::Turkmenistan,
            CountryAlpha3::TCA => Self::TurksAndCaicosIslands,
            CountryAlpha3::TUV => Self::Tuvalu,
            CountryAlpha3::UGA => Self::Uganda,
            CountryAlpha3::UKR => Self::Ukraine,
            CountryAlpha3::ARE => Self::UnitedArabEmirates,
            CountryAlpha3::GBR => Self::UnitedKingdomOfGreatBritainAndNorthernIreland,
            CountryAlpha3::USA => Self::UnitedStatesOfAmerica,
            CountryAlpha3::UMI => Self::UnitedStatesMinorOutlyingIslands,
            CountryAlpha3::URY => Self::Uruguay,
            CountryAlpha3::UZB => Self::Uzbekistan,
            CountryAlpha3::VUT => Self::Vanuatu,
            CountryAlpha3::VEN => Self::VenezuelaBolivarianRepublic,
            CountryAlpha3::VNM => Self::Vietnam,
            CountryAlpha3::VGB => Self::VirginIslandsBritish,
            CountryAlpha3::VIR => Self::VirginIslandsUS,
            CountryAlpha3::WLF => Self::WallisAndFutuna,
            CountryAlpha3::ESH => Self::WesternSahara,
            CountryAlpha3::YEM => Self::Yemen,
            CountryAlpha3::ZMB => Self::Zambia,
            CountryAlpha3::ZWE => Self::Zimbabwe,
        }
    }
    pub const fn to_alpha3(self) -> CountryAlpha3 {
        match self {
            Self::Afghanistan => CountryAlpha3::AFG,
            Self::AlandIslands => CountryAlpha3::ALA,
            Self::Albania => CountryAlpha3::ALB,
            Self::Algeria => CountryAlpha3::DZA,
            Self::AmericanSamoa => CountryAlpha3::ASM,
            Self::Andorra => CountryAlpha3::AND,
            Self::Angola => CountryAlpha3::AGO,
            Self::Anguilla => CountryAlpha3::AIA,
            Self::Antarctica => CountryAlpha3::ATA,
            Self::AntiguaAndBarbuda => CountryAlpha3::ATG,
            Self::Argentina => CountryAlpha3::ARG,
            Self::Armenia => CountryAlpha3::ARM,
            Self::Aruba => CountryAlpha3::ABW,
            Self::Australia => CountryAlpha3::AUS,
            Self::Austria => CountryAlpha3::AUT,
            Self::Azerbaijan => CountryAlpha3::AZE,
            Self::Bahamas => CountryAlpha3::BHS,
            Self::Bahrain => CountryAlpha3::BHR,
            Self::Bangladesh => CountryAlpha3::BGD,
            Self::Barbados => CountryAlpha3::BRB,
            Self::Belarus => CountryAlpha3::BLR,
            Self::Belgium => CountryAlpha3::BEL,
            Self::Belize => CountryAlpha3::BLZ,
            Self::Benin => CountryAlpha3::BEN,
            Self::Bermuda => CountryAlpha3::BMU,
            Self::Bhutan => CountryAlpha3::BTN,
            Self::BoliviaPlurinationalState => CountryAlpha3::BOL,
            Self::BonaireSintEustatiusAndSaba => CountryAlpha3::BES,
            Self::BosniaAndHerzegovina => CountryAlpha3::BIH,
            Self::Botswana => CountryAlpha3::BWA,
            Self::BouvetIsland => CountryAlpha3::BVT,
            Self::Brazil => CountryAlpha3::BRA,
            Self::BritishIndianOceanTerritory => CountryAlpha3::IOT,
            Self::BruneiDarussalam => CountryAlpha3::BRN,
            Self::Bulgaria => CountryAlpha3::BGR,
            Self::BurkinaFaso => CountryAlpha3::BFA,
            Self::Burundi => CountryAlpha3::BDI,
            Self::CaboVerde => CountryAlpha3::CPV,
            Self::Cambodia => CountryAlpha3::KHM,
            Self::Cameroon => CountryAlpha3::CMR,
            Self::Canada => CountryAlpha3::CAN,
            Self::CaymanIslands => CountryAlpha3::CYM,
            Self::CentralAfricanRepublic => CountryAlpha3::CAF,
            Self::Chad => CountryAlpha3::TCD,
            Self::Chile => CountryAlpha3::CHL,
            Self::China => CountryAlpha3::CHN,
            Self::ChristmasIsland => CountryAlpha3::CXR,
            Self::CocosKeelingIslands => CountryAlpha3::CCK,
            Self::Colombia => CountryAlpha3::COL,
            Self::Comoros => CountryAlpha3::COM,
            Self::Congo => CountryAlpha3::COG,
            Self::CongoDemocraticRepublic => CountryAlpha3::COD,
            Self::CookIslands => CountryAlpha3::COK,
            Self::CostaRica => CountryAlpha3::CRI,
            Self::CotedIvoire => CountryAlpha3::CIV,
            Self::Croatia => CountryAlpha3::HRV,
            Self::Cuba => CountryAlpha3::CUB,
            Self::Curacao => CountryAlpha3::CUW,
            Self::Cyprus => CountryAlpha3::CYP,
            Self::Czechia => CountryAlpha3::CZE,
            Self::Denmark => CountryAlpha3::DNK,
            Self::Djibouti => CountryAlpha3::DJI,
            Self::Dominica => CountryAlpha3::DMA,
            Self::DominicanRepublic => CountryAlpha3::DOM,
            Self::Ecuador => CountryAlpha3::ECU,
            Self::Egypt => CountryAlpha3::EGY,
            Self::ElSalvador => CountryAlpha3::SLV,
            Self::EquatorialGuinea => CountryAlpha3::GNQ,
            Self::Eritrea => CountryAlpha3::ERI,
            Self::Estonia => CountryAlpha3::EST,
            Self::Ethiopia => CountryAlpha3::ETH,
            Self::FalklandIslandsMalvinas => CountryAlpha3::FLK,
            Self::FaroeIslands => CountryAlpha3::FRO,
            Self::Fiji => CountryAlpha3::FJI,
            Self::Finland => CountryAlpha3::FIN,
            Self::France => CountryAlpha3::FRA,
            Self::FrenchGuiana => CountryAlpha3::GUF,
            Self::FrenchPolynesia => CountryAlpha3::PYF,
            Self::FrenchSouthernTerritories => CountryAlpha3::ATF,
            Self::Gabon => CountryAlpha3::GAB,
            Self::Gambia => CountryAlpha3::GMB,
            Self::Georgia => CountryAlpha3::GEO,
            Self::Germany => CountryAlpha3::DEU,
            Self::Ghana => CountryAlpha3::GHA,
            Self::Gibraltar => CountryAlpha3::GIB,
            Self::Greece => CountryAlpha3::GRC,
            Self::Greenland => CountryAlpha3::GRL,
            Self::Grenada => CountryAlpha3::GRD,
            Self::Guadeloupe => CountryAlpha3::GLP,
            Self::Guam => CountryAlpha3::GUM,
            Self::Guatemala => CountryAlpha3::GTM,
            Self::Guernsey => CountryAlpha3::GGY,
            Self::Guinea => CountryAlpha3::GIN,
            Self::GuineaBissau => CountryAlpha3::GNB,
            Self::Guyana => CountryAlpha3::GUY,
            Self::Haiti => CountryAlpha3::HTI,
            Self::HeardIslandAndMcDonaldIslands => CountryAlpha3::HMD,
            Self::HolySee => CountryAlpha3::VAT,
            Self::Honduras => CountryAlpha3::HND,
            Self::HongKong => CountryAlpha3::HKG,
            Self::Hungary => CountryAlpha3::HUN,
            Self::Iceland => CountryAlpha3::ISL,
            Self::India => CountryAlpha3::IND,
            Self::Indonesia => CountryAlpha3::IDN,
            Self::IranIslamicRepublic => CountryAlpha3::IRN,
            Self::Iraq => CountryAlpha3::IRQ,
            Self::Ireland => CountryAlpha3::IRL,
            Self::IsleOfMan => CountryAlpha3::IMN,
            Self::Israel => CountryAlpha3::ISR,
            Self::Italy => CountryAlpha3::ITA,
            Self::Jamaica => CountryAlpha3::JAM,
            Self::Japan => CountryAlpha3::JPN,
            Self::Jersey => CountryAlpha3::JEY,
            Self::Jordan => CountryAlpha3::JOR,
            Self::Kazakhstan => CountryAlpha3::KAZ,
            Self::Kenya => CountryAlpha3::KEN,
            Self::Kiribati => CountryAlpha3::KIR,
            Self::KoreaDemocraticPeoplesRepublic => CountryAlpha3::PRK,
            Self::KoreaRepublic => CountryAlpha3::KOR,
            Self::Kuwait => CountryAlpha3::KWT,
            Self::Kyrgyzstan => CountryAlpha3::KGZ,
            Self::LaoPeoplesDemocraticRepublic => CountryAlpha3::LAO,
            Self::Latvia => CountryAlpha3::LVA,
            Self::Lebanon => CountryAlpha3::LBN,
            Self::Lesotho => CountryAlpha3::LSO,
            Self::Liberia => CountryAlpha3::LBR,
            Self::Libya => CountryAlpha3::LBY,
            Self::Liechtenstein => CountryAlpha3::LIE,
            Self::Lithuania => CountryAlpha3::LTU,
            Self::Luxembourg => CountryAlpha3::LUX,
            Self::Macao => CountryAlpha3::MAC,
            Self::MacedoniaTheFormerYugoslavRepublic => CountryAlpha3::MKD,
            Self::Madagascar => CountryAlpha3::MDG,
            Self::Malawi => CountryAlpha3::MWI,
            Self::Malaysia => CountryAlpha3::MYS,
            Self::Maldives => CountryAlpha3::MDV,
            Self::Mali => CountryAlpha3::MLI,
            Self::Malta => CountryAlpha3::MLT,
            Self::MarshallIslands => CountryAlpha3::MHL,
            Self::Martinique => CountryAlpha3::MTQ,
            Self::Mauritania => CountryAlpha3::MRT,
            Self::Mauritius => CountryAlpha3::MUS,
            Self::Mayotte => CountryAlpha3::MYT,
            Self::Mexico => CountryAlpha3::MEX,
            Self::MicronesiaFederatedStates => CountryAlpha3::FSM,
            Self::MoldovaRepublic => CountryAlpha3::MDA,
            Self::Monaco => CountryAlpha3::MCO,
            Self::Mongolia => CountryAlpha3::MNG,
            Self::Montenegro => CountryAlpha3::MNE,
            Self::Montserrat => CountryAlpha3::MSR,
            Self::Morocco => CountryAlpha3::MAR,
            Self::Mozambique => CountryAlpha3::MOZ,
            Self::Myanmar => CountryAlpha3::MMR,
            Self::Namibia => CountryAlpha3::NAM,
            Self::Nauru => CountryAlpha3::NRU,
            Self::Nepal => CountryAlpha3::NPL,
            Self::Netherlands => CountryAlpha3::NLD,
            Self::NewCaledonia => CountryAlpha3::NCL,
            Self::NewZealand => CountryAlpha3::NZL,
            Self::Nicaragua => CountryAlpha3::NIC,
            Self::Niger => CountryAlpha3::NER,
            Self::Nigeria => CountryAlpha3::NGA,
            Self::Niue => CountryAlpha3::NIU,
            Self::NorfolkIsland => CountryAlpha3::NFK,
            Self::NorthernMarianaIslands => CountryAlpha3::MNP,
            Self::Norway => CountryAlpha3::NOR,
            Self::Oman => CountryAlpha3::OMN,
            Self::Pakistan => CountryAlpha3::PAK,
            Self::Palau => CountryAlpha3::PLW,
            Self::PalestineState => CountryAlpha3::PSE,
            Self::Panama => CountryAlpha3::PAN,
            Self::PapuaNewGuinea => CountryAlpha3::PNG,
            Self::Paraguay => CountryAlpha3::PRY,
            Self::Peru => CountryAlpha3::PER,
            Self::Philippines => CountryAlpha3::PHL,
            Self::Pitcairn => CountryAlpha3::PCN,
            Self::Poland => CountryAlpha3::POL,
            Self::Portugal => CountryAlpha3::PRT,
            Self::PuertoRico => CountryAlpha3::PRI,
            Self::Qatar => CountryAlpha3::QAT,
            Self::Reunion => CountryAlpha3::REU,
            Self::Romania => CountryAlpha3::ROU,
            Self::RussianFederation => CountryAlpha3::RUS,
            Self::Rwanda => CountryAlpha3::RWA,
            Self::SaintBarthelemy => CountryAlpha3::BLM,
            Self::SaintHelenaAscensionAndTristandaCunha => CountryAlpha3::SHN,
            Self::SaintKittsAndNevis => CountryAlpha3::KNA,
            Self::SaintLucia => CountryAlpha3::LCA,
            Self::SaintMartinFrenchpart => CountryAlpha3::MAF,
            Self::SaintPierreAndMiquelon => CountryAlpha3::SPM,
            Self::SaintVincentAndTheGrenadines => CountryAlpha3::VCT,
            Self::Samoa => CountryAlpha3::WSM,
            Self::SanMarino => CountryAlpha3::SMR,
            Self::SaoTomeAndPrincipe => CountryAlpha3::STP,
            Self::SaudiArabia => CountryAlpha3::SAU,
            Self::Senegal => CountryAlpha3::SEN,
            Self::Serbia => CountryAlpha3::SRB,
            Self::Seychelles => CountryAlpha3::SYC,
            Self::SierraLeone => CountryAlpha3::SLE,
            Self::Singapore => CountryAlpha3::SGP,
            Self::SintMaartenDutchpart => CountryAlpha3::SXM,
            Self::Slovakia => CountryAlpha3::SVK,
            Self::Slovenia => CountryAlpha3::SVN,
            Self::SolomonIslands => CountryAlpha3::SLB,
            Self::Somalia => CountryAlpha3::SOM,
            Self::SouthAfrica => CountryAlpha3::ZAF,
            Self::SouthGeorgiaAndTheSouthSandwichIslands => CountryAlpha3::SGS,
            Self::SouthSudan => CountryAlpha3::SSD,
            Self::Spain => CountryAlpha3::ESP,
            Self::SriLanka => CountryAlpha3::LKA,
            Self::Sudan => CountryAlpha3::SDN,
            Self::Suriname => CountryAlpha3::SUR,
            Self::SvalbardAndJanMayen => CountryAlpha3::SJM,
            Self::Swaziland => CountryAlpha3::SWZ,
            Self::Sweden => CountryAlpha3::SWE,
            Self::Switzerland => CountryAlpha3::CHE,
            Self::SyrianArabRepublic => CountryAlpha3::SYR,
            Self::TaiwanProvinceOfChina => CountryAlpha3::TWN,
            Self::Tajikistan => CountryAlpha3::TJK,
            Self::TanzaniaUnitedRepublic => CountryAlpha3::TZA,
            Self::Thailand => CountryAlpha3::THA,
            Self::TimorLeste => CountryAlpha3::TLS,
            Self::Togo => CountryAlpha3::TGO,
            Self::Tokelau => CountryAlpha3::TKL,
            Self::Tonga => CountryAlpha3::TON,
            Self::TrinidadAndTobago => CountryAlpha3::TTO,
            Self::Tunisia => CountryAlpha3::TUN,
            Self::Turkey => CountryAlpha3::TUR,
            Self::Turkmenistan => CountryAlpha3::TKM,
            Self::TurksAndCaicosIslands => CountryAlpha3::TCA,
            Self::Tuvalu => CountryAlpha3::TUV,
            Self::Uganda => CountryAlpha3::UGA,
            Self::Ukraine => CountryAlpha3::UKR,
            Self::UnitedArabEmirates => CountryAlpha3::ARE,
            Self::UnitedKingdomOfGreatBritainAndNorthernIreland => CountryAlpha3::GBR,
            Self::UnitedStatesOfAmerica => CountryAlpha3::USA,
            Self::UnitedStatesMinorOutlyingIslands => CountryAlpha3::UMI,
            Self::Uruguay => CountryAlpha3::URY,
            Self::Uzbekistan => CountryAlpha3::UZB,
            Self::Vanuatu => CountryAlpha3::VUT,
            Self::VenezuelaBolivarianRepublic => CountryAlpha3::VEN,
            Self::Vietnam => CountryAlpha3::VNM,
            Self::VirginIslandsBritish => CountryAlpha3::VGB,
            Self::VirginIslandsUS => CountryAlpha3::VIR,
            Self::WallisAndFutuna => CountryAlpha3::WLF,
            Self::WesternSahara => CountryAlpha3::ESH,
            Self::Yemen => CountryAlpha3::YEM,
            Self::Zambia => CountryAlpha3::ZMB,
            Self::Zimbabwe => CountryAlpha3::ZWE,
        }
    }
    pub const fn from_numeric(code: u32) -> Result<Self, NumericCountryCodeParseError> {
        match code {
            4 => Ok(Self::Afghanistan),
            248 => Ok(Self::AlandIslands),
            8 => Ok(Self::Albania),
            12 => Ok(Self::Algeria),
            16 => Ok(Self::AmericanSamoa),
            20 => Ok(Self::Andorra),
            24 => Ok(Self::Angola),
            660 => Ok(Self::Anguilla),
            10 => Ok(Self::Antarctica),
            28 => Ok(Self::AntiguaAndBarbuda),
            32 => Ok(Self::Argentina),
            51 => Ok(Self::Armenia),
            533 => Ok(Self::Aruba),
            36 => Ok(Self::Australia),
            40 => Ok(Self::Austria),
            31 => Ok(Self::Azerbaijan),
            44 => Ok(Self::Bahamas),
            48 => Ok(Self::Bahrain),
            50 => Ok(Self::Bangladesh),
            52 => Ok(Self::Barbados),
            112 => Ok(Self::Belarus),
            56 => Ok(Self::Belgium),
            84 => Ok(Self::Belize),
            204 => Ok(Self::Benin),
            60 => Ok(Self::Bermuda),
            64 => Ok(Self::Bhutan),
            68 => Ok(Self::BoliviaPlurinationalState),
            535 => Ok(Self::BonaireSintEustatiusAndSaba),
            70 => Ok(Self::BosniaAndHerzegovina),
            72 => Ok(Self::Botswana),
            74 => Ok(Self::BouvetIsland),
            76 => Ok(Self::Brazil),
            86 => Ok(Self::BritishIndianOceanTerritory),
            96 => Ok(Self::BruneiDarussalam),
            100 => Ok(Self::Bulgaria),
            854 => Ok(Self::BurkinaFaso),
            108 => Ok(Self::Burundi),
            132 => Ok(Self::CaboVerde),
            116 => Ok(Self::Cambodia),
            120 => Ok(Self::Cameroon),
            124 => Ok(Self::Canada),
            136 => Ok(Self::CaymanIslands),
            140 => Ok(Self::CentralAfricanRepublic),
            148 => Ok(Self::Chad),
            152 => Ok(Self::Chile),
            156 => Ok(Self::China),
            162 => Ok(Self::ChristmasIsland),
            166 => Ok(Self::CocosKeelingIslands),
            170 => Ok(Self::Colombia),
            174 => Ok(Self::Comoros),
            178 => Ok(Self::Congo),
            180 => Ok(Self::CongoDemocraticRepublic),
            184 => Ok(Self::CookIslands),
            188 => Ok(Self::CostaRica),
            384 => Ok(Self::CotedIvoire),
            191 => Ok(Self::Croatia),
            192 => Ok(Self::Cuba),
            531 => Ok(Self::Curacao),
            196 => Ok(Self::Cyprus),
            203 => Ok(Self::Czechia),
            208 => Ok(Self::Denmark),
            262 => Ok(Self::Djibouti),
            212 => Ok(Self::Dominica),
            214 => Ok(Self::DominicanRepublic),
            218 => Ok(Self::Ecuador),
            818 => Ok(Self::Egypt),
            222 => Ok(Self::ElSalvador),
            226 => Ok(Self::EquatorialGuinea),
            232 => Ok(Self::Eritrea),
            233 => Ok(Self::Estonia),
            231 => Ok(Self::Ethiopia),
            238 => Ok(Self::FalklandIslandsMalvinas),
            234 => Ok(Self::FaroeIslands),
            242 => Ok(Self::Fiji),
            246 => Ok(Self::Finland),
            250 => Ok(Self::France),
            254 => Ok(Self::FrenchGuiana),
            258 => Ok(Self::FrenchPolynesia),
            260 => Ok(Self::FrenchSouthernTerritories),
            266 => Ok(Self::Gabon),
            270 => Ok(Self::Gambia),
            268 => Ok(Self::Georgia),
            276 => Ok(Self::Germany),
            288 => Ok(Self::Ghana),
            292 => Ok(Self::Gibraltar),
            300 => Ok(Self::Greece),
            304 => Ok(Self::Greenland),
            308 => Ok(Self::Grenada),
            312 => Ok(Self::Guadeloupe),
            316 => Ok(Self::Guam),
            320 => Ok(Self::Guatemala),
            831 => Ok(Self::Guernsey),
            324 => Ok(Self::Guinea),
            624 => Ok(Self::GuineaBissau),
            328 => Ok(Self::Guyana),
            332 => Ok(Self::Haiti),
            334 => Ok(Self::HeardIslandAndMcDonaldIslands),
            336 => Ok(Self::HolySee),
            340 => Ok(Self::Honduras),
            344 => Ok(Self::HongKong),
            348 => Ok(Self::Hungary),
            352 => Ok(Self::Iceland),
            356 => Ok(Self::India),
            360 => Ok(Self::Indonesia),
            364 => Ok(Self::IranIslamicRepublic),
            368 => Ok(Self::Iraq),
            372 => Ok(Self::Ireland),
            833 => Ok(Self::IsleOfMan),
            376 => Ok(Self::Israel),
            380 => Ok(Self::Italy),
            388 => Ok(Self::Jamaica),
            392 => Ok(Self::Japan),
            832 => Ok(Self::Jersey),
            400 => Ok(Self::Jordan),
            398 => Ok(Self::Kazakhstan),
            404 => Ok(Self::Kenya),
            296 => Ok(Self::Kiribati),
            408 => Ok(Self::KoreaDemocraticPeoplesRepublic),
            410 => Ok(Self::KoreaRepublic),
            414 => Ok(Self::Kuwait),
            417 => Ok(Self::Kyrgyzstan),
            418 => Ok(Self::LaoPeoplesDemocraticRepublic),
            428 => Ok(Self::Latvia),
            422 => Ok(Self::Lebanon),
            426 => Ok(Self::Lesotho),
            430 => Ok(Self::Liberia),
            434 => Ok(Self::Libya),
            438 => Ok(Self::Liechtenstein),
            440 => Ok(Self::Lithuania),
            442 => Ok(Self::Luxembourg),
            446 => Ok(Self::Macao),
            807 => Ok(Self::MacedoniaTheFormerYugoslavRepublic),
            450 => Ok(Self::Madagascar),
            454 => Ok(Self::Malawi),
            458 => Ok(Self::Malaysia),
            462 => Ok(Self::Maldives),
            466 => Ok(Self::Mali),
            470 => Ok(Self::Malta),
            584 => Ok(Self::MarshallIslands),
            474 => Ok(Self::Martinique),
            478 => Ok(Self::Mauritania),
            480 => Ok(Self::Mauritius),
            175 => Ok(Self::Mayotte),
            484 => Ok(Self::Mexico),
            583 => Ok(Self::MicronesiaFederatedStates),
            498 => Ok(Self::MoldovaRepublic),
            492 => Ok(Self::Monaco),
            496 => Ok(Self::Mongolia),
            499 => Ok(Self::Montenegro),
            500 => Ok(Self::Montserrat),
            504 => Ok(Self::Morocco),
            508 => Ok(Self::Mozambique),
            104 => Ok(Self::Myanmar),
            516 => Ok(Self::Namibia),
            520 => Ok(Self::Nauru),
            524 => Ok(Self::Nepal),
            528 => Ok(Self::Netherlands),
            540 => Ok(Self::NewCaledonia),
            554 => Ok(Self::NewZealand),
            558 => Ok(Self::Nicaragua),
            562 => Ok(Self::Niger),
            566 => Ok(Self::Nigeria),
            570 => Ok(Self::Niue),
            574 => Ok(Self::NorfolkIsland),
            580 => Ok(Self::NorthernMarianaIslands),
            578 => Ok(Self::Norway),
            512 => Ok(Self::Oman),
            586 => Ok(Self::Pakistan),
            585 => Ok(Self::Palau),
            275 => Ok(Self::PalestineState),
            591 => Ok(Self::Panama),
            598 => Ok(Self::PapuaNewGuinea),
            600 => Ok(Self::Paraguay),
            604 => Ok(Self::Peru),
            608 => Ok(Self::Philippines),
            612 => Ok(Self::Pitcairn),
            616 => Ok(Self::Poland),
            620 => Ok(Self::Portugal),
            630 => Ok(Self::PuertoRico),
            634 => Ok(Self::Qatar),
            638 => Ok(Self::Reunion),
            642 => Ok(Self::Romania),
            643 => Ok(Self::RussianFederation),
            646 => Ok(Self::Rwanda),
            652 => Ok(Self::SaintBarthelemy),
            654 => Ok(Self::SaintHelenaAscensionAndTristandaCunha),
            659 => Ok(Self::SaintKittsAndNevis),
            662 => Ok(Self::SaintLucia),
            663 => Ok(Self::SaintMartinFrenchpart),
            666 => Ok(Self::SaintPierreAndMiquelon),
            670 => Ok(Self::SaintVincentAndTheGrenadines),
            882 => Ok(Self::Samoa),
            674 => Ok(Self::SanMarino),
            678 => Ok(Self::SaoTomeAndPrincipe),
            682 => Ok(Self::SaudiArabia),
            686 => Ok(Self::Senegal),
            688 => Ok(Self::Serbia),
            690 => Ok(Self::Seychelles),
            694 => Ok(Self::SierraLeone),
            702 => Ok(Self::Singapore),
            534 => Ok(Self::SintMaartenDutchpart),
            703 => Ok(Self::Slovakia),
            705 => Ok(Self::Slovenia),
            90 => Ok(Self::SolomonIslands),
            706 => Ok(Self::Somalia),
            710 => Ok(Self::SouthAfrica),
            239 => Ok(Self::SouthGeorgiaAndTheSouthSandwichIslands),
            728 => Ok(Self::SouthSudan),
            724 => Ok(Self::Spain),
            144 => Ok(Self::SriLanka),
            729 => Ok(Self::Sudan),
            740 => Ok(Self::Suriname),
            744 => Ok(Self::SvalbardAndJanMayen),
            748 => Ok(Self::Swaziland),
            752 => Ok(Self::Sweden),
            756 => Ok(Self::Switzerland),
            760 => Ok(Self::SyrianArabRepublic),
            158 => Ok(Self::TaiwanProvinceOfChina),
            762 => Ok(Self::Tajikistan),
            834 => Ok(Self::TanzaniaUnitedRepublic),
            764 => Ok(Self::Thailand),
            626 => Ok(Self::TimorLeste),
            768 => Ok(Self::Togo),
            772 => Ok(Self::Tokelau),
            776 => Ok(Self::Tonga),
            780 => Ok(Self::TrinidadAndTobago),
            788 => Ok(Self::Tunisia),
            792 => Ok(Self::Turkey),
            795 => Ok(Self::Turkmenistan),
            796 => Ok(Self::TurksAndCaicosIslands),
            798 => Ok(Self::Tuvalu),
            800 => Ok(Self::Uganda),
            804 => Ok(Self::Ukraine),
            784 => Ok(Self::UnitedArabEmirates),
            826 => Ok(Self::UnitedKingdomOfGreatBritainAndNorthernIreland),
            840 => Ok(Self::UnitedStatesOfAmerica),
            581 => Ok(Self::UnitedStatesMinorOutlyingIslands),
            858 => Ok(Self::Uruguay),
            860 => Ok(Self::Uzbekistan),
            548 => Ok(Self::Vanuatu),
            862 => Ok(Self::VenezuelaBolivarianRepublic),
            704 => Ok(Self::Vietnam),
            92 => Ok(Self::VirginIslandsBritish),
            850 => Ok(Self::VirginIslandsUS),
            876 => Ok(Self::WallisAndFutuna),
            732 => Ok(Self::WesternSahara),
            887 => Ok(Self::Yemen),
            894 => Ok(Self::Zambia),
            716 => Ok(Self::Zimbabwe),
            _ => Err(NumericCountryCodeParseError),
        }
    }
    pub const fn to_numeric(self) -> u32 {
        match self {
            Self::Afghanistan => 4,
            Self::AlandIslands => 248,
            Self::Albania => 8,
            Self::Algeria => 12,
            Self::AmericanSamoa => 16,
            Self::Andorra => 20,
            Self::Angola => 24,
            Self::Anguilla => 660,
            Self::Antarctica => 10,
            Self::AntiguaAndBarbuda => 28,
            Self::Argentina => 32,
            Self::Armenia => 51,
            Self::Aruba => 533,
            Self::Australia => 36,
            Self::Austria => 40,
            Self::Azerbaijan => 31,
            Self::Bahamas => 44,
            Self::Bahrain => 48,
            Self::Bangladesh => 50,
            Self::Barbados => 52,
            Self::Belarus => 112,
            Self::Belgium => 56,
            Self::Belize => 84,
            Self::Benin => 204,
            Self::Bermuda => 60,
            Self::Bhutan => 64,
            Self::BoliviaPlurinationalState => 68,
            Self::BonaireSintEustatiusAndSaba => 535,
            Self::BosniaAndHerzegovina => 70,
            Self::Botswana => 72,
            Self::BouvetIsland => 74,
            Self::Brazil => 76,
            Self::BritishIndianOceanTerritory => 86,
            Self::BruneiDarussalam => 96,
            Self::Bulgaria => 100,
            Self::BurkinaFaso => 854,
            Self::Burundi => 108,
            Self::CaboVerde => 132,
            Self::Cambodia => 116,
            Self::Cameroon => 120,
            Self::Canada => 124,
            Self::CaymanIslands => 136,
            Self::CentralAfricanRepublic => 140,
            Self::Chad => 148,
            Self::Chile => 152,
            Self::China => 156,
            Self::ChristmasIsland => 162,
            Self::CocosKeelingIslands => 166,
            Self::Colombia => 170,
            Self::Comoros => 174,
            Self::Congo => 178,
            Self::CongoDemocraticRepublic => 180,
            Self::CookIslands => 184,
            Self::CostaRica => 188,
            Self::CotedIvoire => 384,
            Self::Croatia => 191,
            Self::Cuba => 192,
            Self::Curacao => 531,
            Self::Cyprus => 196,
            Self::Czechia => 203,
            Self::Denmark => 208,
            Self::Djibouti => 262,
            Self::Dominica => 212,
            Self::DominicanRepublic => 214,
            Self::Ecuador => 218,
            Self::Egypt => 818,
            Self::ElSalvador => 222,
            Self::EquatorialGuinea => 226,
            Self::Eritrea => 232,
            Self::Estonia => 233,
            Self::Ethiopia => 231,
            Self::FalklandIslandsMalvinas => 238,
            Self::FaroeIslands => 234,
            Self::Fiji => 242,
            Self::Finland => 246,
            Self::France => 250,
            Self::FrenchGuiana => 254,
            Self::FrenchPolynesia => 258,
            Self::FrenchSouthernTerritories => 260,
            Self::Gabon => 266,
            Self::Gambia => 270,
            Self::Georgia => 268,
            Self::Germany => 276,
            Self::Ghana => 288,
            Self::Gibraltar => 292,
            Self::Greece => 300,
            Self::Greenland => 304,
            Self::Grenada => 308,
            Self::Guadeloupe => 312,
            Self::Guam => 316,
            Self::Guatemala => 320,
            Self::Guernsey => 831,
            Self::Guinea => 324,
            Self::GuineaBissau => 624,
            Self::Guyana => 328,
            Self::Haiti => 332,
            Self::HeardIslandAndMcDonaldIslands => 334,
            Self::HolySee => 336,
            Self::Honduras => 340,
            Self::HongKong => 344,
            Self::Hungary => 348,
            Self::Iceland => 352,
            Self::India => 356,
            Self::Indonesia => 360,
            Self::IranIslamicRepublic => 364,
            Self::Iraq => 368,
            Self::Ireland => 372,
            Self::IsleOfMan => 833,
            Self::Israel => 376,
            Self::Italy => 380,
            Self::Jamaica => 388,
            Self::Japan => 392,
            Self::Jersey => 832,
            Self::Jordan => 400,
            Self::Kazakhstan => 398,
            Self::Kenya => 404,
            Self::Kiribati => 296,
            Self::KoreaDemocraticPeoplesRepublic => 408,
            Self::KoreaRepublic => 410,
            Self::Kuwait => 414,
            Self::Kyrgyzstan => 417,
            Self::LaoPeoplesDemocraticRepublic => 418,
            Self::Latvia => 428,
            Self::Lebanon => 422,
            Self::Lesotho => 426,
            Self::Liberia => 430,
            Self::Libya => 434,
            Self::Liechtenstein => 438,
            Self::Lithuania => 440,
            Self::Luxembourg => 442,
            Self::Macao => 446,
            Self::MacedoniaTheFormerYugoslavRepublic => 807,
            Self::Madagascar => 450,
            Self::Malawi => 454,
            Self::Malaysia => 458,
            Self::Maldives => 462,
            Self::Mali => 466,
            Self::Malta => 470,
            Self::MarshallIslands => 584,
            Self::Martinique => 474,
            Self::Mauritania => 478,
            Self::Mauritius => 480,
            Self::Mayotte => 175,
            Self::Mexico => 484,
            Self::MicronesiaFederatedStates => 583,
            Self::MoldovaRepublic => 498,
            Self::Monaco => 492,
            Self::Mongolia => 496,
            Self::Montenegro => 499,
            Self::Montserrat => 500,
            Self::Morocco => 504,
            Self::Mozambique => 508,
            Self::Myanmar => 104,
            Self::Namibia => 516,
            Self::Nauru => 520,
            Self::Nepal => 524,
            Self::Netherlands => 528,
            Self::NewCaledonia => 540,
            Self::NewZealand => 554,
            Self::Nicaragua => 558,
            Self::Niger => 562,
            Self::Nigeria => 566,
            Self::Niue => 570,
            Self::NorfolkIsland => 574,
            Self::NorthernMarianaIslands => 580,
            Self::Norway => 578,
            Self::Oman => 512,
            Self::Pakistan => 586,
            Self::Palau => 585,
            Self::PalestineState => 275,
            Self::Panama => 591,
            Self::PapuaNewGuinea => 598,
            Self::Paraguay => 600,
            Self::Peru => 604,
            Self::Philippines => 608,
            Self::Pitcairn => 612,
            Self::Poland => 616,
            Self::Portugal => 620,
            Self::PuertoRico => 630,
            Self::Qatar => 634,
            Self::Reunion => 638,
            Self::Romania => 642,
            Self::RussianFederation => 643,
            Self::Rwanda => 646,
            Self::SaintBarthelemy => 652,
            Self::SaintHelenaAscensionAndTristandaCunha => 654,
            Self::SaintKittsAndNevis => 659,
            Self::SaintLucia => 662,
            Self::SaintMartinFrenchpart => 663,
            Self::SaintPierreAndMiquelon => 666,
            Self::SaintVincentAndTheGrenadines => 670,
            Self::Samoa => 882,
            Self::SanMarino => 674,
            Self::SaoTomeAndPrincipe => 678,
            Self::SaudiArabia => 682,
            Self::Senegal => 686,
            Self::Serbia => 688,
            Self::Seychelles => 690,
            Self::SierraLeone => 694,
            Self::Singapore => 702,
            Self::SintMaartenDutchpart => 534,
            Self::Slovakia => 703,
            Self::Slovenia => 705,
            Self::SolomonIslands => 90,
            Self::Somalia => 706,
            Self::SouthAfrica => 710,
            Self::SouthGeorgiaAndTheSouthSandwichIslands => 239,
            Self::SouthSudan => 728,
            Self::Spain => 724,
            Self::SriLanka => 144,
            Self::Sudan => 729,
            Self::Suriname => 740,
            Self::SvalbardAndJanMayen => 744,
            Self::Swaziland => 748,
            Self::Sweden => 752,
            Self::Switzerland => 756,
            Self::SyrianArabRepublic => 760,
            Self::TaiwanProvinceOfChina => 158,
            Self::Tajikistan => 762,
            Self::TanzaniaUnitedRepublic => 834,
            Self::Thailand => 764,
            Self::TimorLeste => 626,
            Self::Togo => 768,
            Self::Tokelau => 772,
            Self::Tonga => 776,
            Self::TrinidadAndTobago => 780,
            Self::Tunisia => 788,
            Self::Turkey => 792,
            Self::Turkmenistan => 795,
            Self::TurksAndCaicosIslands => 796,
            Self::Tuvalu => 798,
            Self::Uganda => 800,
            Self::Ukraine => 804,
            Self::UnitedArabEmirates => 784,
            Self::UnitedKingdomOfGreatBritainAndNorthernIreland => 826,
            Self::UnitedStatesOfAmerica => 840,
            Self::UnitedStatesMinorOutlyingIslands => 581,
            Self::Uruguay => 858,
            Self::Uzbekistan => 860,
            Self::Vanuatu => 548,
            Self::VenezuelaBolivarianRepublic => 862,
            Self::Vietnam => 704,
            Self::VirginIslandsBritish => 92,
            Self::VirginIslandsUS => 850,
            Self::WallisAndFutuna => 876,
            Self::WesternSahara => 732,
            Self::Yemen => 887,
            Self::Zambia => 894,
            Self::Zimbabwe => 716,
        }
    }
}

impl From<PaymentMethodType> for PaymentMethod {
    fn from(value: PaymentMethodType) -> Self {
        match value {
            PaymentMethodType::Ach => Self::BankDebit,
            PaymentMethodType::Affirm => Self::PayLater,
            PaymentMethodType::AfterpayClearpay => Self::PayLater,
            PaymentMethodType::AliPay => Self::Wallet,
            PaymentMethodType::AliPayHk => Self::Wallet,
            PaymentMethodType::Alma => Self::PayLater,
            PaymentMethodType::AmazonPay => Self::Wallet,
            PaymentMethodType::ApplePay => Self::Wallet,
            PaymentMethodType::Bacs => Self::BankDebit,
            PaymentMethodType::BancontactCard => Self::BankRedirect,
            PaymentMethodType::BcaBankTransfer => Self::BankTransfer,
            PaymentMethodType::Becs => Self::BankDebit,
            PaymentMethodType::BniVa => Self::BankTransfer,
            PaymentMethodType::BriVa => Self::BankTransfer,
            PaymentMethodType::Benefit => Self::CardRedirect,
            PaymentMethodType::Bizum => Self::BankRedirect,
            PaymentMethodType::Blik => Self::BankRedirect,
            PaymentMethodType::Alfamart => Self::Voucher,
            PaymentMethodType::CardRedirect => Self::CardRedirect,
            PaymentMethodType::CimbVa => Self::BankTransfer,
            PaymentMethodType::ClassicReward => Self::Reward,
            PaymentMethodType::Credit => Self::Card,
            #[cfg(feature = "v2")]
            PaymentMethodType::Card => Self::Card,
            PaymentMethodType::CryptoCurrency => Self::Crypto,
            PaymentMethodType::Dana => Self::Wallet,
            PaymentMethodType::DanamonVa => Self::BankTransfer,
            PaymentMethodType::Debit => Self::Card,
            PaymentMethodType::Fps => Self::RealTimePayment,
            PaymentMethodType::DuitNow => Self::RealTimePayment,
            PaymentMethodType::Eft => Self::BankRedirect,
            PaymentMethodType::Eps => Self::BankRedirect,
            PaymentMethodType::Evoucher => Self::Reward,
            PaymentMethodType::Giropay => Self::BankRedirect,
            PaymentMethodType::GooglePay => Self::Wallet,
            PaymentMethodType::GoPay => Self::Wallet,
            PaymentMethodType::Gcash => Self::Wallet,
            PaymentMethodType::Mifinity => Self::Wallet,
            PaymentMethodType::Ideal => Self::BankRedirect,
            PaymentMethodType::Klarna => Self::PayLater,
            PaymentMethodType::KakaoPay => Self::Wallet,
            PaymentMethodType::Knet => Self::CardRedirect,
            PaymentMethodType::LocalBankRedirect => Self::BankRedirect,
            PaymentMethodType::MbWay => Self::Wallet,
            PaymentMethodType::MobilePay => Self::Wallet,
            PaymentMethodType::Momo => Self::Wallet,
            PaymentMethodType::MomoAtm => Self::CardRedirect,
            PaymentMethodType::Multibanco => Self::BankTransfer,
            PaymentMethodType::MandiriVa => Self::BankTransfer,
            PaymentMethodType::Interac => Self::BankRedirect,
            PaymentMethodType::InstantBankTransfer => Self::BankTransfer,
            PaymentMethodType::InstantBankTransferFinland => Self::BankTransfer,
            PaymentMethodType::InstantBankTransferPoland => Self::BankTransfer,
            PaymentMethodType::Indomaret => Self::Voucher,
            PaymentMethodType::OnlineBankingCzechRepublic => Self::BankRedirect,
            PaymentMethodType::OnlineBankingFinland => Self::BankRedirect,
            PaymentMethodType::OnlineBankingFpx => Self::BankRedirect,
            PaymentMethodType::OnlineBankingThailand => Self::BankRedirect,
            PaymentMethodType::OnlineBankingPoland => Self::BankRedirect,
            PaymentMethodType::OnlineBankingSlovakia => Self::BankRedirect,
            PaymentMethodType::Paze => Self::Wallet,
            PaymentMethodType::PermataBankTransfer => Self::BankTransfer,
            PaymentMethodType::Pix => Self::BankTransfer,
            PaymentMethodType::Pse => Self::BankTransfer,
            PaymentMethodType::LocalBankTransfer => Self::BankTransfer,
            PaymentMethodType::PayBright => Self::PayLater,
            PaymentMethodType::Paypal => Self::Wallet,
            PaymentMethodType::PaySafeCard => Self::GiftCard,
            PaymentMethodType::Przelewy24 => Self::BankRedirect,
            PaymentMethodType::PromptPay => Self::RealTimePayment,
            PaymentMethodType::SamsungPay => Self::Wallet,
            PaymentMethodType::Sepa => Self::BankDebit,
            PaymentMethodType::SepaBankTransfer => Self::BankTransfer,
            PaymentMethodType::Sofort => Self::BankRedirect,
            PaymentMethodType::Swish => Self::BankRedirect,
            PaymentMethodType::Trustly => Self::BankRedirect,
            PaymentMethodType::Twint => Self::Wallet,
            PaymentMethodType::UpiCollect => Self::Upi,
            PaymentMethodType::UpiIntent => Self::Upi,
            PaymentMethodType::Vipps => Self::Wallet,
            PaymentMethodType::Venmo => Self::Wallet,
            PaymentMethodType::VietQr => Self::RealTimePayment,
            PaymentMethodType::Walley => Self::PayLater,
            PaymentMethodType::WeChatPay => Self::Wallet,
            PaymentMethodType::TouchNGo => Self::Wallet,
            PaymentMethodType::Atome => Self::PayLater,
            PaymentMethodType::Boleto => Self::Voucher,
            PaymentMethodType::Efecty => Self::Voucher,
            PaymentMethodType::PagoEfectivo => Self::Voucher,
            PaymentMethodType::RedCompra => Self::Voucher,
            PaymentMethodType::RedPagos => Self::Voucher,
            PaymentMethodType::Cashapp => Self::Wallet,
            PaymentMethodType::Givex => Self::GiftCard,
            PaymentMethodType::Oxxo => Self::Voucher,
            PaymentMethodType::OpenBankingUk => Self::BankRedirect,
            PaymentMethodType::SevenEleven => Self::Voucher,
            PaymentMethodType::Lawson => Self::Voucher,
            PaymentMethodType::MiniStop => Self::Voucher,
            PaymentMethodType::FamilyMart => Self::Voucher,
            PaymentMethodType::Seicomart => Self::Voucher,
            PaymentMethodType::PayEasy => Self::Voucher,
            PaymentMethodType::OpenBankingPIS => Self::OpenBanking,
            PaymentMethodType::DirectCarrierBilling => Self::MobilePayment,
            PaymentMethodType::RevolutPay => Self::Wallet,
        }
    }
}

#[derive(Debug)]
pub struct NumericCountryCodeParseError;
#[allow(dead_code)]
mod custom_serde {
    use super::*;

    pub mod alpha2_country_code {
        use std::fmt;

        use serde::de::Visitor;

        use super::*;

        // `serde::Serialize` implementation needs the function to accept `&Country`
        #[allow(clippy::trivially_copy_pass_by_ref)]
        pub fn serialize<S>(code: &Country, serializer: S) -> Result<S::Ok, S::Error>
        where
            S: serde::Serializer,
        {
            code.to_alpha2().serialize(serializer)
        }

        struct FieldVisitor;

        impl Visitor<'_> for FieldVisitor {
            type Value = CountryAlpha2;

            fn expecting(&self, formatter: &mut Formatter<'_>) -> fmt::Result {
                formatter.write_str("CountryAlpha2 as a string")
            }
        }

        pub fn deserialize<'a, D>(deserializer: D) -> Result<Country, D::Error>
        where
            D: serde::Deserializer<'a>,
        {
            CountryAlpha2::deserialize(deserializer).map(Country::from_alpha2)
        }
    }

    pub mod alpha3_country_code {
        use std::fmt;

        use serde::de::Visitor;

        use super::*;

        // `serde::Serialize` implementation needs the function to accept `&Country`
        #[allow(clippy::trivially_copy_pass_by_ref)]
        pub fn serialize<S>(code: &Country, serializer: S) -> Result<S::Ok, S::Error>
        where
            S: serde::Serializer,
        {
            code.to_alpha3().serialize(serializer)
        }

        struct FieldVisitor;

        impl Visitor<'_> for FieldVisitor {
            type Value = CountryAlpha3;

            fn expecting(&self, formatter: &mut Formatter<'_>) -> fmt::Result {
                formatter.write_str("CountryAlpha3 as a string")
            }
        }

        pub fn deserialize<'a, D>(deserializer: D) -> Result<Country, D::Error>
        where
            D: serde::Deserializer<'a>,
        {
            CountryAlpha3::deserialize(deserializer).map(Country::from_alpha3)
        }
    }

    pub mod numeric_country_code {
        use std::fmt;

        use serde::de::Visitor;

        use super::*;

        // `serde::Serialize` implementation needs the function to accept `&Country`
        #[allow(clippy::trivially_copy_pass_by_ref)]
        pub fn serialize<S>(code: &Country, serializer: S) -> Result<S::Ok, S::Error>
        where
            S: serde::Serializer,
        {
            code.to_numeric().serialize(serializer)
        }

        struct FieldVisitor;

        impl Visitor<'_> for FieldVisitor {
            type Value = u32;

            fn expecting(&self, formatter: &mut Formatter<'_>) -> fmt::Result {
                formatter.write_str("Numeric country code passed as an u32")
            }
        }

        pub fn deserialize<'a, D>(deserializer: D) -> Result<Country, D::Error>
        where
            D: serde::Deserializer<'a>,
        {
            u32::deserialize(deserializer)
                .and_then(|value| Country::from_numeric(value).map_err(serde::de::Error::custom))
        }
    }
}

impl From<Option<bool>> for super::External3dsAuthenticationRequest {
    fn from(value: Option<bool>) -> Self {
        match value {
            Some(true) => Self::Enable,
            _ => Self::Skip,
        }
    }
}

/// Get the boolean value of the `External3dsAuthenticationRequest`.
impl super::External3dsAuthenticationRequest {
    pub fn as_bool(&self) -> bool {
        match self {
            Self::Enable => true,
            Self::Skip => false,
        }
    }
}

impl super::EnablePaymentLinkRequest {
    pub fn as_bool(&self) -> bool {
        match self {
            Self::Enable => true,
            Self::Skip => false,
        }
    }
}

impl From<Option<bool>> for super::EnablePaymentLinkRequest {
    fn from(value: Option<bool>) -> Self {
        match value {
            Some(true) => Self::Enable,
            _ => Self::Skip,
        }
    }
}

impl From<Option<bool>> for super::MitExemptionRequest {
    fn from(value: Option<bool>) -> Self {
        match value {
            Some(true) => Self::Apply,
            _ => Self::Skip,
        }
    }
}

impl super::MitExemptionRequest {
    pub fn as_bool(&self) -> bool {
        match self {
            Self::Apply => true,
            Self::Skip => false,
        }
    }
}

impl From<Option<bool>> for super::PresenceOfCustomerDuringPayment {
    fn from(value: Option<bool>) -> Self {
        match value {
            Some(false) => Self::Absent,
            _ => Self::Present,
        }
    }
}

impl super::PresenceOfCustomerDuringPayment {
    pub fn as_bool(&self) -> bool {
        match self {
            Self::Present => true,
            Self::Absent => false,
        }
    }
}

impl From<AttemptStatus> for IntentStatus {
    fn from(s: AttemptStatus) -> Self {
        match s {
            AttemptStatus::Charged | AttemptStatus::AutoRefunded => Self::Succeeded,

            AttemptStatus::ConfirmationAwaited => Self::RequiresConfirmation,
            AttemptStatus::PaymentMethodAwaited => Self::RequiresPaymentMethod,

            AttemptStatus::Authorized => Self::RequiresCapture,
            AttemptStatus::AuthenticationPending | AttemptStatus::DeviceDataCollectionPending => {
                Self::RequiresCustomerAction
            }
            AttemptStatus::Unresolved => Self::RequiresMerchantAction,
            AttemptStatus::IntegrityFailure => Self::Conflicted,
            AttemptStatus::PartialCharged => Self::PartiallyCaptured,
            AttemptStatus::PartialChargedAndChargeable => Self::PartiallyCapturedAndCapturable,
            AttemptStatus::Started
            | AttemptStatus::AuthenticationSuccessful
            | AttemptStatus::Authorizing
            | AttemptStatus::CodInitiated
            | AttemptStatus::VoidInitiated
            | AttemptStatus::CaptureInitiated
            | AttemptStatus::Pending => Self::Processing,

            AttemptStatus::AuthenticationFailed
            | AttemptStatus::AuthorizationFailed
            | AttemptStatus::VoidFailed
            | AttemptStatus::RouterDeclined
            | AttemptStatus::CaptureFailed
            | AttemptStatus::Failure => Self::Failed,
            AttemptStatus::Voided => Self::Cancelled,
        }
    }
}

#[cfg(test)]
mod tests {
    #![allow(clippy::unwrap_used)]

    use super::*;

    #[derive(serde::Serialize)]
    struct Alpha2Request {
        #[serde(with = "custom_serde::alpha2_country_code")]
        pub country: Country,
    }

    #[derive(serde::Serialize)]
    struct Alpha3Request {
        #[serde(with = "custom_serde::alpha3_country_code")]
        pub country: Country,
    }

    #[derive(serde::Serialize)]
    struct NumericRequest {
        #[serde(with = "custom_serde::numeric_country_code")]
        pub country: Country,
    }

    #[derive(serde::Serialize, serde::Deserialize)]
    struct HyperswitchRequestAlpha2 {
        #[serde(with = "custom_serde::alpha2_country_code")]
        pub country: Country,
    }

    #[derive(serde::Serialize, serde::Deserialize)]
    struct HyperswitchRequestAlpha3 {
        #[serde(with = "custom_serde::alpha3_country_code")]
        pub country: Country,
    }

    #[derive(serde::Serialize, serde::Deserialize, Debug)]
    struct HyperswitchRequestNumeric {
        #[serde(with = "custom_serde::numeric_country_code")]
        pub country: Country,
    }

    #[test]
    fn test_serialize_alpha2() {
        let x_request = Alpha2Request {
            country: Country::India,
        };
        let serialized_country = serde_json::to_string(&x_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"IN"}"#);

        let x_request = Alpha2Request {
            country: Country::MacedoniaTheFormerYugoslavRepublic,
        };
        let serialized_country = serde_json::to_string(&x_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"MK"}"#);

        let x_request = Alpha2Request {
            country: Country::FrenchSouthernTerritories,
        };
        let serialized_country = serde_json::to_string(&x_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"TF"}"#);
    }

    #[test]
    fn test_serialize_alpha3() {
        let y_request = Alpha3Request {
            country: Country::India,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"IND"}"#);

        let y_request = Alpha3Request {
            country: Country::HeardIslandAndMcDonaldIslands,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"HMD"}"#);

        let y_request = Alpha3Request {
            country: Country::Argentina,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":"ARG"}"#);
    }

    #[test]
    fn test_serialize_numeric() {
        let y_request = NumericRequest {
            country: Country::India,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":356}"#);

        let y_request = NumericRequest {
            country: Country::Bermuda,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":60}"#);

        let y_request = NumericRequest {
            country: Country::GuineaBissau,
        };
        let serialized_country = serde_json::to_string(&y_request).unwrap();
        assert_eq!(serialized_country, r#"{"country":624}"#);
    }

    #[test]
    fn test_deserialize_alpha2() {
        let request_str = r#"{"country":"IN"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha2>(request_str).unwrap();
        assert_eq!(request.country, Country::India);

        let request_str = r#"{"country":"GR"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha2>(request_str).unwrap();
        assert_eq!(request.country, Country::Greece);

        let request_str = r#"{"country":"IQ"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha2>(request_str).unwrap();
        assert_eq!(request.country, Country::Iraq);
    }

    #[test]
    fn test_deserialize_alpha3() {
        let request_str = r#"{"country":"IND"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha3>(request_str).unwrap();
        assert_eq!(request.country, Country::India);

        let request_str = r#"{"country":"LVA"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha3>(request_str).unwrap();
        assert_eq!(request.country, Country::Latvia);

        let request_str = r#"{"country":"PNG"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha3>(request_str).unwrap();
        assert_eq!(request.country, Country::PapuaNewGuinea);
    }

    #[test]
    fn test_deserialize_numeric() {
        let request_str = r#"{"country":356}"#;
        let request = serde_json::from_str::<HyperswitchRequestNumeric>(request_str).unwrap();
        assert_eq!(request.country, Country::India);

        let request_str = r#"{"country":239}"#;
        let request = serde_json::from_str::<HyperswitchRequestNumeric>(request_str).unwrap();
        assert_eq!(
            request.country,
            Country::SouthGeorgiaAndTheSouthSandwichIslands
        );

        let request_str = r#"{"country":826}"#;
        let request = serde_json::from_str::<HyperswitchRequestNumeric>(request_str).unwrap();
        assert_eq!(
            request.country,
            Country::UnitedKingdomOfGreatBritainAndNorthernIreland
        );
    }

    #[test]
    fn test_deserialize_and_serialize() {
        // Deserialize the country as alpha2 code
        // Serialize the country as alpha3 code
        let request_str = r#"{"country":"IN"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha2>(request_str).unwrap();
        let alpha3_request = Alpha3Request {
            country: request.country,
        };
        let response = serde_json::to_string::<Alpha3Request>(&alpha3_request).unwrap();
        assert_eq!(response, r#"{"country":"IND"}"#)
    }

    #[test]
    fn test_serialize_and_deserialize() {
        let request_str = r#"{"country":"AX"}"#;
        let request = serde_json::from_str::<HyperswitchRequestAlpha2>(request_str).unwrap();
        let alpha3_request = Alpha3Request {
            country: request.country,
        };
        let response = serde_json::to_string::<Alpha3Request>(&alpha3_request).unwrap();
        assert_eq!(response, r#"{"country":"ALA"}"#);

        let result = serde_json::from_str::<HyperswitchRequestAlpha3>(response.as_str()).unwrap();
        assert_eq!(result.country, Country::AlandIslands);
    }

    #[test]
    fn test_deserialize_invalid_country_code() {
        let request_str = r#"{"country": 123456}"#;
        let result: Result<HyperswitchRequestNumeric, _> =
            serde_json::from_str::<HyperswitchRequestNumeric>(request_str);
        assert!(result.is_err());
    }
}
