[package]
name = "common_enums"
description = "Enums shared across the request/response types and database types"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
readme = "README.md"
license.workspace = true

[features]
dummy_connector = []
openapi = []
payouts = []
v2 = []

[dependencies]
diesel = { version = "2.2.10", features = ["postgres", "128-column-tables"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
strum = { version = "0.26", features = ["derive"] }
thiserror = "1.0.69"
utoipa = { version = "4.2.3", features = ["preserve_order", "preserve_path_order"] }

# First party crates
router_derive = { version = "0.1.0", path = "../router_derive" }
masking = { version = "0.1.0", path = "../masking" }

[dev-dependencies]
serde_json = "1.0.140"

[lints]
workspace = true
