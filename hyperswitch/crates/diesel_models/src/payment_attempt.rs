use common_types::primitive_wrappers::{
    ExtendedAuthorizationAppliedBool, RequestExtendedAuthorizationBool,
};
use common_utils::{
    id_type, pii,
    types::{ConnectorTransactionId, ConnectorTransactionIdTrait, MinorUnit},
};
use diesel::{AsChangeset, Identifiable, Insertable, Queryable, Selectable};
use serde::{Deserialize, Serialize};
use time::PrimitiveDateTime;

use crate::enums as storage_enums;
#[cfg(feature = "v1")]
use crate::schema::payment_attempt;
#[cfg(feature = "v2")]
use crate::schema_v2::payment_attempt;

common_utils::impl_to_sql_from_sql_json!(ConnectorMandateReferenceId);
#[derive(
    Clone, Debug, serde::Deserialize, serde::Serialize, Eq, PartialEq, diesel::AsExpression,
)]
#[diesel(sql_type = diesel::sql_types::Jsonb)]
pub struct ConnectorMandateReferenceId {
    pub connector_mandate_id: Option<String>,
    pub payment_method_id: Option<String>,
    pub mandate_metadata: Option<pii::SecretSerdeValue>,
    pub connector_mandate_request_reference_id: Option<String>,
}

impl ConnectorMandateReferenceId {
    pub fn get_connector_mandate_request_reference_id(&self) -> Option<String> {
        self.connector_mandate_request_reference_id.clone()
    }
}

#[cfg(feature = "v2")]
#[derive(
    Clone, Debug, Eq, PartialEq, Identifiable, Queryable, Serialize, Deserialize, Selectable,
)]
#[diesel(table_name = payment_attempt, primary_key(id), check_for_backend(diesel::pg::Pg))]
pub struct PaymentAttempt {
    pub payment_id: id_type::GlobalPaymentId,
    pub merchant_id: id_type::MerchantId,
    pub status: storage_enums::AttemptStatus,
    pub connector: Option<String>,
    pub error_message: Option<String>,
    pub surcharge_amount: Option<MinorUnit>,
    pub payment_method_id: Option<id_type::GlobalPaymentMethodId>,
    pub authentication_type: storage_enums::AuthenticationType,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub created_at: PrimitiveDateTime,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub modified_at: PrimitiveDateTime,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub last_synced: Option<PrimitiveDateTime>,
    pub cancellation_reason: Option<String>,
    pub amount_to_capture: Option<MinorUnit>,
    pub browser_info: Option<common_utils::types::BrowserInformation>,
    pub error_code: Option<String>,
    pub payment_token: Option<String>,
    pub connector_metadata: Option<pii::SecretSerdeValue>,
    pub payment_experience: Option<storage_enums::PaymentExperience>,
    pub payment_method_data: Option<pii::SecretSerdeValue>,
    pub preprocessing_step_id: Option<String>,
    pub error_reason: Option<String>,
    pub multiple_capture_count: Option<i16>,
    pub connector_response_reference_id: Option<String>,
    pub amount_capturable: MinorUnit,
    pub updated_by: String,
    pub merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    pub encoded_data: Option<masking::Secret<String>>,
    pub unified_code: Option<String>,
    pub unified_message: Option<String>,
    pub net_amount: MinorUnit,
    pub external_three_ds_authentication_attempted: Option<bool>,
    pub authentication_connector: Option<String>,
    pub authentication_id: Option<String>,
    pub fingerprint_id: Option<String>,
    pub client_source: Option<String>,
    pub client_version: Option<String>,
    pub customer_acceptance: Option<pii::SecretSerdeValue>,
    pub profile_id: id_type::ProfileId,
    pub organization_id: id_type::OrganizationId,
    pub card_network: Option<String>,
    pub shipping_cost: Option<MinorUnit>,
    pub order_tax_amount: Option<MinorUnit>,
    pub request_extended_authorization: Option<RequestExtendedAuthorizationBool>,
    pub extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
    pub capture_before: Option<PrimitiveDateTime>,
    pub card_discovery: Option<storage_enums::CardDiscovery>,
    pub charges: Option<common_types::payments::ConnectorChargeResponseData>,
    pub processor_merchant_id: Option<id_type::MerchantId>,
    pub created_by: Option<String>,
    pub payment_method_type_v2: storage_enums::PaymentMethod,
    pub connector_payment_id: Option<ConnectorTransactionId>,
    pub payment_method_subtype: storage_enums::PaymentMethodType,
    pub routing_result: Option<serde_json::Value>,
    pub authentication_applied: Option<common_enums::AuthenticationType>,
    pub external_reference_id: Option<String>,
    pub tax_on_surcharge: Option<MinorUnit>,
    pub payment_method_billing_address: Option<common_utils::encryption::Encryption>,
    pub redirection_data: Option<RedirectForm>,
    pub connector_payment_data: Option<String>,
    pub connector_token_details: Option<ConnectorTokenDetails>,
    pub id: id_type::GlobalAttemptId,
    pub feature_metadata: Option<PaymentAttemptFeatureMetadata>,
    /// This field can be returned for both approved and refused Mastercard payments.
    /// This code provides additional information about the type of transaction or the reason why the payment failed.
    /// If the payment failed, the network advice code gives guidance on if and when you can retry the payment.
    pub network_advice_code: Option<String>,
    /// For card errors resulting from a card issuer decline, a brand specific 2, 3, or 4 digit code which indicates the reason the authorization failed.
    pub network_decline_code: Option<String>,
    /// A string indicating how to proceed with an network error if payment gateway provide one. This is used to understand the network error code better.
    pub network_error_message: Option<String>,
    pub connector_request_reference_id: Option<String>,
}

#[cfg(feature = "v1")]
#[derive(
    Clone, Debug, Eq, PartialEq, Identifiable, Queryable, Serialize, Deserialize, Selectable,
)]
#[diesel(table_name = payment_attempt, primary_key(attempt_id, merchant_id), check_for_backend(diesel::pg::Pg))]
pub struct PaymentAttempt {
    pub payment_id: id_type::PaymentId,
    pub merchant_id: id_type::MerchantId,
    pub attempt_id: String,
    pub status: storage_enums::AttemptStatus,
    pub amount: MinorUnit,
    pub currency: Option<storage_enums::Currency>,
    pub save_to_locker: Option<bool>,
    pub connector: Option<String>,
    pub error_message: Option<String>,
    pub offer_amount: Option<MinorUnit>,
    pub surcharge_amount: Option<MinorUnit>,
    pub tax_amount: Option<MinorUnit>,
    pub payment_method_id: Option<String>,
    pub payment_method: Option<storage_enums::PaymentMethod>,
    pub connector_transaction_id: Option<ConnectorTransactionId>,
    pub capture_method: Option<storage_enums::CaptureMethod>,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub capture_on: Option<PrimitiveDateTime>,
    pub confirm: bool,
    pub authentication_type: Option<storage_enums::AuthenticationType>,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub created_at: PrimitiveDateTime,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub modified_at: PrimitiveDateTime,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub last_synced: Option<PrimitiveDateTime>,
    pub cancellation_reason: Option<String>,
    pub amount_to_capture: Option<MinorUnit>,
    pub mandate_id: Option<String>,
    pub browser_info: Option<serde_json::Value>,
    pub error_code: Option<String>,
    pub payment_token: Option<String>,
    pub connector_metadata: Option<serde_json::Value>,
    pub payment_experience: Option<storage_enums::PaymentExperience>,
    pub payment_method_type: Option<storage_enums::PaymentMethodType>,
    pub payment_method_data: Option<serde_json::Value>,
    pub business_sub_label: Option<String>,
    pub straight_through_algorithm: Option<serde_json::Value>,
    pub preprocessing_step_id: Option<String>,
    // providing a location to store mandate details intermediately for transaction
    pub mandate_details: Option<storage_enums::MandateDataType>,
    pub error_reason: Option<String>,
    pub multiple_capture_count: Option<i16>,
    // reference to the payment at connector side
    pub connector_response_reference_id: Option<String>,
    pub amount_capturable: MinorUnit,
    pub updated_by: String,
    pub merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    pub authentication_data: Option<serde_json::Value>,
    pub encoded_data: Option<String>,
    pub unified_code: Option<String>,
    pub unified_message: Option<String>,
    pub net_amount: Option<MinorUnit>,
    pub external_three_ds_authentication_attempted: Option<bool>,
    pub authentication_connector: Option<String>,
    pub authentication_id: Option<String>,
    pub mandate_data: Option<storage_enums::MandateDetails>,
    pub fingerprint_id: Option<String>,
    pub payment_method_billing_address_id: Option<String>,
    pub charge_id: Option<String>,
    pub client_source: Option<String>,
    pub client_version: Option<String>,
    pub customer_acceptance: Option<pii::SecretSerdeValue>,
    pub profile_id: id_type::ProfileId,
    pub organization_id: id_type::OrganizationId,
    pub card_network: Option<String>,
    pub shipping_cost: Option<MinorUnit>,
    pub order_tax_amount: Option<MinorUnit>,
    /// INFO: This field is deprecated and replaced by processor_transaction_data
    pub connector_transaction_data: Option<String>,
    pub connector_mandate_detail: Option<ConnectorMandateReferenceId>,
    pub request_extended_authorization: Option<RequestExtendedAuthorizationBool>,
    pub extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
    pub capture_before: Option<PrimitiveDateTime>,
    pub processor_transaction_data: Option<String>,
    pub card_discovery: Option<storage_enums::CardDiscovery>,
    pub charges: Option<common_types::payments::ConnectorChargeResponseData>,
    pub issuer_error_code: Option<String>,
    pub issuer_error_message: Option<String>,
    pub processor_merchant_id: Option<id_type::MerchantId>,
    pub created_by: Option<String>,
    pub setup_future_usage_applied: Option<storage_enums::FutureUsage>,
}

#[cfg(feature = "v1")]
impl ConnectorTransactionIdTrait for PaymentAttempt {
    fn get_optional_connector_transaction_id(&self) -> Option<&String> {
        match self
            .connector_transaction_id
            .as_ref()
            .map(|txn_id| txn_id.get_txn_id(self.processor_transaction_data.as_ref()))
            .transpose()
        {
            Ok(txn_id) => txn_id,

            // In case hashed data is missing from DB, use the hashed ID as connector transaction ID
            Err(_) => self
                .connector_transaction_id
                .as_ref()
                .map(|txn_id| txn_id.get_id()),
        }
    }
}

#[cfg(feature = "v2")]
impl ConnectorTransactionIdTrait for PaymentAttempt {
    fn get_optional_connector_transaction_id(&self) -> Option<&String> {
        match self
            .connector_payment_id
            .as_ref()
            .map(|txn_id| txn_id.get_txn_id(self.connector_payment_data.as_ref()))
            .transpose()
        {
            Ok(txn_id) => txn_id,

            // In case hashed data is missing from DB, use the hashed ID as connector payment ID
            Err(_) => self
                .connector_payment_id
                .as_ref()
                .map(|txn_id| txn_id.get_id()),
        }
    }
}

#[cfg(feature = "v2")]
#[derive(
    Clone, Debug, PartialEq, Eq, serde::Deserialize, serde::Serialize, diesel::AsExpression,
)]
#[diesel(sql_type = diesel::sql_types::Jsonb)]
pub struct ConnectorTokenDetails {
    pub connector_mandate_id: Option<String>,
    pub connector_token_request_reference_id: Option<String>,
}

#[cfg(feature = "v2")]
common_utils::impl_to_sql_from_sql_json!(ConnectorTokenDetails);

#[cfg(feature = "v2")]
impl ConnectorTokenDetails {
    pub fn get_connector_token_request_reference_id(&self) -> Option<String> {
        self.connector_token_request_reference_id.clone()
    }
}

#[derive(Clone, Debug, Eq, PartialEq, Queryable, Serialize, Deserialize)]
pub struct PaymentListFilters {
    pub connector: Vec<String>,
    pub currency: Vec<storage_enums::Currency>,
    pub status: Vec<storage_enums::IntentStatus>,
    pub payment_method: Vec<storage_enums::PaymentMethod>,
}

#[cfg(feature = "v2")]
#[derive(Clone, Debug, Insertable, router_derive::DebugAsDisplay, Serialize, Deserialize)]
#[diesel(table_name = payment_attempt)]
pub struct PaymentAttemptNew {
    pub payment_id: id_type::GlobalPaymentId,
    pub merchant_id: id_type::MerchantId,
    pub status: storage_enums::AttemptStatus,
    pub error_message: Option<String>,
    pub surcharge_amount: Option<MinorUnit>,
    pub tax_on_surcharge: Option<MinorUnit>,
    pub payment_method_id: Option<id_type::GlobalPaymentMethodId>,
    pub authentication_type: storage_enums::AuthenticationType,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub created_at: PrimitiveDateTime,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub modified_at: PrimitiveDateTime,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub last_synced: Option<PrimitiveDateTime>,
    pub cancellation_reason: Option<String>,
    pub amount_to_capture: Option<MinorUnit>,
    pub browser_info: Option<common_utils::types::BrowserInformation>,
    pub payment_token: Option<String>,
    pub error_code: Option<String>,
    pub connector_metadata: Option<pii::SecretSerdeValue>,
    pub payment_experience: Option<storage_enums::PaymentExperience>,
    pub payment_method_data: Option<pii::SecretSerdeValue>,
    pub preprocessing_step_id: Option<String>,
    pub error_reason: Option<String>,
    pub connector_response_reference_id: Option<String>,
    pub multiple_capture_count: Option<i16>,
    pub amount_capturable: MinorUnit,
    pub updated_by: String,
    pub merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    pub redirection_data: Option<RedirectForm>,
    pub encoded_data: Option<masking::Secret<String>>,
    pub unified_code: Option<String>,
    pub unified_message: Option<String>,
    pub net_amount: MinorUnit,
    pub external_three_ds_authentication_attempted: Option<bool>,
    pub authentication_connector: Option<String>,
    pub authentication_id: Option<String>,
    pub fingerprint_id: Option<String>,
    pub payment_method_billing_address: Option<common_utils::encryption::Encryption>,
    pub client_source: Option<String>,
    pub client_version: Option<String>,
    pub customer_acceptance: Option<pii::SecretSerdeValue>,
    pub profile_id: id_type::ProfileId,
    pub organization_id: id_type::OrganizationId,
    pub card_network: Option<String>,
    pub shipping_cost: Option<MinorUnit>,
    pub order_tax_amount: Option<MinorUnit>,
    pub charges: Option<common_types::payments::ConnectorChargeResponseData>,
    pub feature_metadata: Option<PaymentAttemptFeatureMetadata>,
    pub payment_method_type_v2: storage_enums::PaymentMethod,
    pub connector_payment_id: Option<ConnectorTransactionId>,
    pub payment_method_subtype: storage_enums::PaymentMethodType,
    pub id: id_type::GlobalAttemptId,
    pub connector_token_details: Option<ConnectorTokenDetails>,
    pub card_discovery: Option<storage_enums::CardDiscovery>,
    pub request_extended_authorization: Option<RequestExtendedAuthorizationBool>,
    pub extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
    pub capture_before: Option<PrimitiveDateTime>,
    pub connector: Option<String>,
    pub network_decline_code: Option<String>,
    pub network_advice_code: Option<String>,
    pub network_error_message: Option<String>,
    pub processor_merchant_id: Option<id_type::MerchantId>,
    pub created_by: Option<String>,
    pub connector_request_reference_id: Option<String>,
}

#[cfg(feature = "v1")]
#[derive(Clone, Debug, Insertable, router_derive::DebugAsDisplay, Serialize, Deserialize)]
#[diesel(table_name = payment_attempt)]
pub struct PaymentAttemptNew {
    pub payment_id: id_type::PaymentId,
    pub merchant_id: id_type::MerchantId,
    pub attempt_id: String,
    pub status: storage_enums::AttemptStatus,
    pub amount: MinorUnit,
    pub currency: Option<storage_enums::Currency>,
    // pub auto_capture: Option<bool>,
    pub save_to_locker: Option<bool>,
    pub connector: Option<String>,
    pub error_message: Option<String>,
    pub offer_amount: Option<MinorUnit>,
    pub surcharge_amount: Option<MinorUnit>,
    pub tax_amount: Option<MinorUnit>,
    pub payment_method_id: Option<String>,
    pub payment_method: Option<storage_enums::PaymentMethod>,
    pub capture_method: Option<storage_enums::CaptureMethod>,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub capture_on: Option<PrimitiveDateTime>,
    pub confirm: bool,
    pub authentication_type: Option<storage_enums::AuthenticationType>,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub created_at: PrimitiveDateTime,
    #[serde(with = "common_utils::custom_serde::iso8601")]
    pub modified_at: PrimitiveDateTime,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub last_synced: Option<PrimitiveDateTime>,
    pub cancellation_reason: Option<String>,
    pub amount_to_capture: Option<MinorUnit>,
    pub mandate_id: Option<String>,
    pub browser_info: Option<serde_json::Value>,
    pub payment_token: Option<String>,
    pub error_code: Option<String>,
    pub connector_metadata: Option<serde_json::Value>,
    pub payment_experience: Option<storage_enums::PaymentExperience>,
    pub payment_method_type: Option<storage_enums::PaymentMethodType>,
    pub payment_method_data: Option<serde_json::Value>,
    pub business_sub_label: Option<String>,
    pub straight_through_algorithm: Option<serde_json::Value>,
    pub preprocessing_step_id: Option<String>,
    pub mandate_details: Option<storage_enums::MandateDataType>,
    pub error_reason: Option<String>,
    pub connector_response_reference_id: Option<String>,
    pub multiple_capture_count: Option<i16>,
    pub amount_capturable: MinorUnit,
    pub updated_by: String,
    pub merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    pub authentication_data: Option<serde_json::Value>,
    pub encoded_data: Option<String>,
    pub unified_code: Option<String>,
    pub unified_message: Option<String>,
    pub net_amount: Option<MinorUnit>,
    pub external_three_ds_authentication_attempted: Option<bool>,
    pub authentication_connector: Option<String>,
    pub authentication_id: Option<String>,
    pub mandate_data: Option<storage_enums::MandateDetails>,
    pub fingerprint_id: Option<String>,
    pub payment_method_billing_address_id: Option<String>,
    pub client_source: Option<String>,
    pub client_version: Option<String>,
    pub customer_acceptance: Option<pii::SecretSerdeValue>,
    pub profile_id: id_type::ProfileId,
    pub organization_id: id_type::OrganizationId,
    pub card_network: Option<String>,
    pub shipping_cost: Option<MinorUnit>,
    pub order_tax_amount: Option<MinorUnit>,
    pub connector_mandate_detail: Option<ConnectorMandateReferenceId>,
    pub request_extended_authorization: Option<RequestExtendedAuthorizationBool>,
    pub extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
    #[serde(default, with = "common_utils::custom_serde::iso8601::option")]
    pub capture_before: Option<PrimitiveDateTime>,
    pub card_discovery: Option<storage_enums::CardDiscovery>,
    pub processor_merchant_id: Option<id_type::MerchantId>,
    pub created_by: Option<String>,
    pub setup_future_usage_applied: Option<storage_enums::FutureUsage>,
}

#[cfg(feature = "v1")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PaymentAttemptUpdate {
    Update {
        amount: MinorUnit,
        currency: storage_enums::Currency,
        status: storage_enums::AttemptStatus,
        authentication_type: Option<storage_enums::AuthenticationType>,
        payment_method: Option<storage_enums::PaymentMethod>,
        payment_token: Option<String>,
        payment_method_data: Option<serde_json::Value>,
        payment_method_type: Option<storage_enums::PaymentMethodType>,
        payment_experience: Option<storage_enums::PaymentExperience>,
        business_sub_label: Option<String>,
        amount_to_capture: Option<MinorUnit>,
        capture_method: Option<storage_enums::CaptureMethod>,
        surcharge_amount: Option<MinorUnit>,
        tax_amount: Option<MinorUnit>,
        fingerprint_id: Option<String>,
        payment_method_billing_address_id: Option<String>,
        updated_by: String,
    },
    UpdateTrackers {
        payment_token: Option<String>,
        connector: Option<String>,
        straight_through_algorithm: Option<serde_json::Value>,
        amount_capturable: Option<MinorUnit>,
        surcharge_amount: Option<MinorUnit>,
        tax_amount: Option<MinorUnit>,
        updated_by: String,
        merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    },
    AuthenticationTypeUpdate {
        authentication_type: storage_enums::AuthenticationType,
        updated_by: String,
    },
    ConfirmUpdate {
        amount: MinorUnit,
        currency: storage_enums::Currency,
        status: storage_enums::AttemptStatus,
        authentication_type: Option<storage_enums::AuthenticationType>,
        capture_method: Option<storage_enums::CaptureMethod>,
        payment_method: Option<storage_enums::PaymentMethod>,
        browser_info: Option<serde_json::Value>,
        connector: Option<String>,
        payment_token: Option<String>,
        payment_method_data: Option<serde_json::Value>,
        payment_method_type: Option<storage_enums::PaymentMethodType>,
        payment_experience: Option<storage_enums::PaymentExperience>,
        business_sub_label: Option<String>,
        straight_through_algorithm: Option<serde_json::Value>,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        amount_capturable: Option<MinorUnit>,
        surcharge_amount: Option<MinorUnit>,
        tax_amount: Option<MinorUnit>,
        fingerprint_id: Option<String>,
        updated_by: String,
        merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
        payment_method_id: Option<String>,
        external_three_ds_authentication_attempted: Option<bool>,
        authentication_connector: Option<String>,
        authentication_id: Option<String>,
        payment_method_billing_address_id: Option<String>,
        client_source: Option<String>,
        client_version: Option<String>,
        customer_acceptance: Option<pii::SecretSerdeValue>,
        shipping_cost: Option<MinorUnit>,
        order_tax_amount: Option<MinorUnit>,
        connector_mandate_detail: Option<ConnectorMandateReferenceId>,
        card_discovery: Option<storage_enums::CardDiscovery>,
    },
    VoidUpdate {
        status: storage_enums::AttemptStatus,
        cancellation_reason: Option<String>,
        updated_by: String,
    },
    PaymentMethodDetailsUpdate {
        payment_method_id: Option<String>,
        updated_by: String,
    },
    ConnectorMandateDetailUpdate {
        connector_mandate_detail: Option<ConnectorMandateReferenceId>,
        updated_by: String,
    },
    BlocklistUpdate {
        status: storage_enums::AttemptStatus,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        updated_by: String,
    },
    RejectUpdate {
        status: storage_enums::AttemptStatus,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        updated_by: String,
    },
    ResponseUpdate {
        status: storage_enums::AttemptStatus,
        connector: Option<String>,
        connector_transaction_id: Option<String>,
        authentication_type: Option<storage_enums::AuthenticationType>,
        payment_method_id: Option<String>,
        mandate_id: Option<String>,
        connector_metadata: Option<serde_json::Value>,
        payment_token: Option<String>,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        error_reason: Option<Option<String>>,
        connector_response_reference_id: Option<String>,
        amount_capturable: Option<MinorUnit>,
        updated_by: String,
        authentication_data: Option<serde_json::Value>,
        encoded_data: Option<String>,
        unified_code: Option<Option<String>>,
        unified_message: Option<Option<String>>,
        capture_before: Option<PrimitiveDateTime>,
        extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
        payment_method_data: Option<serde_json::Value>,
        connector_mandate_detail: Option<ConnectorMandateReferenceId>,
        charges: Option<common_types::payments::ConnectorChargeResponseData>,
        setup_future_usage_applied: Option<storage_enums::FutureUsage>,
    },
    UnresolvedResponseUpdate {
        status: storage_enums::AttemptStatus,
        connector: Option<String>,
        connector_transaction_id: Option<String>,
        payment_method_id: Option<String>,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        error_reason: Option<Option<String>>,
        connector_response_reference_id: Option<String>,
        updated_by: String,
    },
    StatusUpdate {
        status: storage_enums::AttemptStatus,
        updated_by: String,
    },
    ErrorUpdate {
        connector: Option<String>,
        status: storage_enums::AttemptStatus,
        error_code: Option<Option<String>>,
        error_message: Option<Option<String>>,
        error_reason: Option<Option<String>>,
        amount_capturable: Option<MinorUnit>,
        updated_by: String,
        unified_code: Option<Option<String>>,
        unified_message: Option<Option<String>>,
        connector_transaction_id: Option<String>,
        payment_method_data: Option<serde_json::Value>,
        authentication_type: Option<storage_enums::AuthenticationType>,
        issuer_error_code: Option<String>,
        issuer_error_message: Option<String>,
    },
    CaptureUpdate {
        amount_to_capture: Option<MinorUnit>,
        multiple_capture_count: Option<i16>,
        updated_by: String,
    },
    AmountToCaptureUpdate {
        status: storage_enums::AttemptStatus,
        amount_capturable: MinorUnit,
        updated_by: String,
    },
    PreprocessingUpdate {
        status: storage_enums::AttemptStatus,
        payment_method_id: Option<String>,
        connector_metadata: Option<serde_json::Value>,
        preprocessing_step_id: Option<String>,
        connector_transaction_id: Option<String>,
        connector_response_reference_id: Option<String>,
        updated_by: String,
    },
    ConnectorResponse {
        authentication_data: Option<serde_json::Value>,
        encoded_data: Option<String>,
        connector_transaction_id: Option<String>,
        connector: Option<String>,
        charges: Option<common_types::payments::ConnectorChargeResponseData>,
        updated_by: String,
    },
    IncrementalAuthorizationAmountUpdate {
        amount: MinorUnit,
        amount_capturable: MinorUnit,
    },
    AuthenticationUpdate {
        status: storage_enums::AttemptStatus,
        external_three_ds_authentication_attempted: Option<bool>,
        authentication_connector: Option<String>,
        authentication_id: Option<String>,
        updated_by: String,
    },
    ManualUpdate {
        status: Option<storage_enums::AttemptStatus>,
        error_code: Option<String>,
        error_message: Option<String>,
        error_reason: Option<String>,
        updated_by: String,
        unified_code: Option<String>,
        unified_message: Option<String>,
        connector_transaction_id: Option<String>,
    },
    PostSessionTokensUpdate {
        updated_by: String,
        connector_metadata: Option<serde_json::Value>,
    },
}

#[cfg(feature = "v2")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PaymentAttemptUpdate {
    // Update {
    //     amount: MinorUnit,
    //     currency: storage_enums::Currency,
    //     status: storage_enums::AttemptStatus,
    //     authentication_type: Option<storage_enums::AuthenticationType>,
    //     payment_method: Option<storage_enums::PaymentMethod>,
    //     payment_token: Option<String>,
    //     payment_method_data: Option<serde_json::Value>,
    //     payment_method_type: Option<storage_enums::PaymentMethodType>,
    //     payment_experience: Option<storage_enums::PaymentExperience>,
    //     business_sub_label: Option<String>,
    //     amount_to_capture: Option<MinorUnit>,
    //     capture_method: Option<storage_enums::CaptureMethod>,
    //     surcharge_amount: Option<MinorUnit>,
    //     tax_amount: Option<MinorUnit>,
    //     fingerprint_id: Option<String>,
    //     payment_method_billing_address_id: Option<String>,
    //     updated_by: String,
    // },
    // UpdateTrackers {
    //     payment_token: Option<String>,
    //     connector: Option<String>,
    //     straight_through_algorithm: Option<serde_json::Value>,
    //     amount_capturable: Option<MinorUnit>,
    //     surcharge_amount: Option<MinorUnit>,
    //     tax_amount: Option<MinorUnit>,
    //     updated_by: String,
    //     merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    // },
    // AuthenticationTypeUpdate {
    //     authentication_type: storage_enums::AuthenticationType,
    //     updated_by: String,
    // },
    // ConfirmUpdate {
    //     amount: MinorUnit,
    //     currency: storage_enums::Currency,
    //     status: storage_enums::AttemptStatus,
    //     authentication_type: Option<storage_enums::AuthenticationType>,
    //     capture_method: Option<storage_enums::CaptureMethod>,
    //     payment_method: Option<storage_enums::PaymentMethod>,
    //     browser_info: Option<serde_json::Value>,
    //     connector: Option<String>,
    //     payment_token: Option<String>,
    //     payment_method_data: Option<serde_json::Value>,
    //     payment_method_type: Option<storage_enums::PaymentMethodType>,
    //     payment_experience: Option<storage_enums::PaymentExperience>,
    //     business_sub_label: Option<String>,
    //     straight_through_algorithm: Option<serde_json::Value>,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     amount_capturable: Option<MinorUnit>,
    //     surcharge_amount: Option<MinorUnit>,
    //     tax_amount: Option<MinorUnit>,
    //     fingerprint_id: Option<String>,
    //     updated_by: String,
    //     merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    //     payment_method_id: Option<String>,
    //     external_three_ds_authentication_attempted: Option<bool>,
    //     authentication_connector: Option<String>,
    //     authentication_id: Option<String>,
    //     payment_method_billing_address_id: Option<String>,
    //     client_source: Option<String>,
    //     client_version: Option<String>,
    //     customer_acceptance: Option<pii::SecretSerdeValue>,
    // },
    // VoidUpdate {
    //     status: storage_enums::AttemptStatus,
    //     cancellation_reason: Option<String>,
    //     updated_by: String,
    // },
    // PaymentMethodDetailsUpdate {
    //     payment_method_id: Option<String>,
    //     updated_by: String,
    // },
    // ConnectorMandateDetailUpdate {
    //     connector_mandate_detail: Option<ConnectorMandateReferenceId>,
    //     updated_by: String,
    // }
    // BlocklistUpdate {
    //     status: storage_enums::AttemptStatus,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     updated_by: String,
    // },
    // RejectUpdate {
    //     status: storage_enums::AttemptStatus,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     updated_by: String,
    // },
    // ResponseUpdate {
    //     status: storage_enums::AttemptStatus,
    //     connector: Option<String>,
    //     connector_transaction_id: Option<String>,
    //     authentication_type: Option<storage_enums::AuthenticationType>,
    //     payment_method_id: Option<String>,
    //     mandate_id: Option<String>,
    //     connector_metadata: Option<serde_json::Value>,
    //     payment_token: Option<String>,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     error_reason: Option<Option<String>>,
    //     connector_response_reference_id: Option<String>,
    //     amount_capturable: Option<MinorUnit>,
    //     updated_by: String,
    //     authentication_data: Option<serde_json::Value>,
    //     encoded_data: Option<String>,
    //     unified_code: Option<Option<String>>,
    //     unified_message: Option<Option<String>>,
    //     payment_method_data: Option<serde_json::Value>,
    //     charge_id: Option<String>,
    // },
    // UnresolvedResponseUpdate {
    //     status: storage_enums::AttemptStatus,
    //     connector: Option<String>,
    //     connector_transaction_id: Option<String>,
    //     payment_method_id: Option<String>,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     error_reason: Option<Option<String>>,
    //     connector_response_reference_id: Option<String>,
    //     updated_by: String,
    // },
    // StatusUpdate {
    //     status: storage_enums::AttemptStatus,
    //     updated_by: String,
    // },
    // ErrorUpdate {
    //     connector: Option<String>,
    //     status: storage_enums::AttemptStatus,
    //     error_code: Option<Option<String>>,
    //     error_message: Option<Option<String>>,
    //     error_reason: Option<Option<String>>,
    //     amount_capturable: Option<MinorUnit>,
    //     updated_by: String,
    //     unified_code: Option<Option<String>>,
    //     unified_message: Option<Option<String>>,
    //     connector_transaction_id: Option<String>,
    //     payment_method_data: Option<serde_json::Value>,
    //     authentication_type: Option<storage_enums::AuthenticationType>,
    // },
    // CaptureUpdate {
    //     amount_to_capture: Option<MinorUnit>,
    //     multiple_capture_count: Option<MinorUnit>,
    //     updated_by: String,
    // },
    // AmountToCaptureUpdate {
    //     status: storage_enums::AttemptStatus,
    //     amount_capturable: MinorUnit,
    //     updated_by: String,
    // },
    // PreprocessingUpdate {
    //     status: storage_enums::AttemptStatus,
    //     payment_method_id: Option<String>,
    //     connector_metadata: Option<serde_json::Value>,
    //     preprocessing_step_id: Option<String>,
    //     connector_transaction_id: Option<String>,
    //     connector_response_reference_id: Option<String>,
    //     updated_by: String,
    // },
    // ConnectorResponse {
    //     authentication_data: Option<serde_json::Value>,
    //     encoded_data: Option<String>,
    //     connector_transaction_id: Option<String>,
    //     connector: Option<String>,
    //     charge_id: Option<String>,
    //     updated_by: String,
    // },
    // IncrementalAuthorizationAmountUpdate {
    //     amount: MinorUnit,
    //     amount_capturable: MinorUnit,
    // },
    // AuthenticationUpdate {
    //     status: storage_enums::AttemptStatus,
    //     external_three_ds_authentication_attempted: Option<bool>,
    //     authentication_connector: Option<String>,
    //     authentication_id: Option<String>,
    //     updated_by: String,
    // },
    // ManualUpdate {
    //     status: Option<storage_enums::AttemptStatus>,
    //     error_code: Option<String>,
    //     error_message: Option<String>,
    //     error_reason: Option<String>,
    //     updated_by: String,
    //     unified_code: Option<String>,
    //     unified_message: Option<String>,
    //     connector_transaction_id: Option<String>,
    // },
}

// TODO: uncomment fields as and when required
#[cfg(feature = "v2")]
#[derive(Clone, Debug, AsChangeset, router_derive::DebugAsDisplay)]
#[diesel(table_name = payment_attempt)]
pub struct PaymentAttemptUpdateInternal {
    pub status: Option<storage_enums::AttemptStatus>,
    pub authentication_type: Option<storage_enums::AuthenticationType>,
    pub error_message: Option<String>,
    pub connector_payment_id: Option<String>,
    pub payment_method_id: Option<id_type::GlobalPaymentMethodId>,
    // cancellation_reason: Option<String>,
    pub modified_at: PrimitiveDateTime,
    pub browser_info: Option<serde_json::Value>,
    // payment_token: Option<String>,
    pub error_code: Option<String>,
    pub connector_metadata: Option<pii::SecretSerdeValue>,
    // payment_method_data: Option<serde_json::Value>,
    // payment_experience: Option<storage_enums::PaymentExperience>,
    // preprocessing_step_id: Option<String>,
    pub error_reason: Option<String>,
    // connector_response_reference_id: Option<String>,
    // multiple_capture_count: Option<i16>,
    // pub surcharge_amount: Option<MinorUnit>,
    // tax_on_surcharge: Option<MinorUnit>,
    pub amount_capturable: Option<MinorUnit>,
    pub amount_to_capture: Option<MinorUnit>,
    pub updated_by: String,
    pub merchant_connector_id: Option<id_type::MerchantConnectorAccountId>,
    pub connector: Option<String>,
    pub redirection_data: Option<RedirectForm>,
    // encoded_data: Option<String>,
    pub unified_code: Option<Option<String>>,
    pub unified_message: Option<Option<String>>,
    // external_three_ds_authentication_attempted: Option<bool>,
    // authentication_connector: Option<String>,
    // authentication_id: Option<String>,
    // fingerprint_id: Option<String>,
    // charge_id: Option<String>,
    // client_source: Option<String>,
    // client_version: Option<String>,
    // customer_acceptance: Option<pii::SecretSerdeValue>,
    // card_network: Option<String>,
    pub connector_token_details: Option<ConnectorTokenDetails>,
    pub feature_metadata: Option<PaymentAttemptFeatureMetadata>,
    pub network_decline_code: Option<String>,
    pub network_advice_code: Option<String>,
    pub network_error_message: Option<String>,
    pub connector_request_reference_id: Option<String>,
}

#[cfg(feature = "v1")]
#[derive(Clone, Debug, AsChangeset, router_derive::DebugAsDisplay)]
#[diesel(table_name = payment_attempt)]
pub struct PaymentAttemptUpdateInternal {
    pub amount: Option<MinorUnit>,
    pub net_amount: Option<MinorUnit>,
    pub currency: Option<storage_enums::Currency>,
    pub status: Option<storage_enums::AttemptStatus>,
    pub connector_transaction_id: Option<ConnectorTransactionId>,
    pub amount_to_capture: Option<MinorUnit>,
    pub connector: Option<Option<String>>,
    pub authentication_type: Option<storage_enums::AuthenticationType>,
    pub payment_method: Option<storage_enums::PaymentMethod>,
    pub error_message: Option<Option<String>>,
    pub payment_method_id: Option<String>,
    pub cancellation_reason: Option<String>,
    pub modified_at: PrimitiveDateTime,
    pub mandate_id: Option<String>,
    pub browser_info: Option<serde_json::Value>,
    pub payment_token: Option<String>,
    pub error_code: Option<Option<String>>,
    pub connector_metadata: Option<serde_json::Value>,
    pub payment_method_data: Option<serde_json::Value>,
    pub payment_method_type: Option<storage_enums::PaymentMethodType>,
    pub payment_experience: Option<storage_enums::PaymentExperience>,
    pub business_sub_label: Option<String>,
    pub straight_through_algorithm: Option<serde_json::Value>,
    pub preprocessing_step_id: Option<String>,
    pub error_reason: Option<Option<String>>,
    pub capture_method: Option<storage_enums::CaptureMethod>,
    pub connector_response_reference_id: Option<String>,
    pub multiple_capture_count: Option<i16>,
    pub surcharge_amount: Option<MinorUnit>,
    pub tax_amount: Option<MinorUnit>,
    pub amount_capturable: Option<MinorUnit>,
    pub updated_by: String,
    pub merchant_connector_id: Option<Option<id_type::MerchantConnectorAccountId>>,
    pub authentication_data: Option<serde_json::Value>,
    pub encoded_data: Option<String>,
    pub unified_code: Option<Option<String>>,
    pub unified_message: Option<Option<String>>,
    pub external_three_ds_authentication_attempted: Option<bool>,
    pub authentication_connector: Option<String>,
    pub authentication_id: Option<String>,
    pub fingerprint_id: Option<String>,
    pub payment_method_billing_address_id: Option<String>,
    pub client_source: Option<String>,
    pub client_version: Option<String>,
    pub customer_acceptance: Option<pii::SecretSerdeValue>,
    pub card_network: Option<String>,
    pub capture_before: Option<PrimitiveDateTime>,
    pub extended_authorization_applied: Option<ExtendedAuthorizationAppliedBool>,
    pub shipping_cost: Option<MinorUnit>,
    pub order_tax_amount: Option<MinorUnit>,
    pub connector_mandate_detail: Option<ConnectorMandateReferenceId>,
    pub processor_transaction_data: Option<String>,
    pub card_discovery: Option<common_enums::CardDiscovery>,
    pub charges: Option<common_types::payments::ConnectorChargeResponseData>,
    pub issuer_error_code: Option<String>,
    pub issuer_error_message: Option<String>,
    pub setup_future_usage_applied: Option<storage_enums::FutureUsage>,
}

#[cfg(feature = "v1")]
impl PaymentAttemptUpdateInternal {
    pub fn populate_derived_fields(self, source: &PaymentAttempt) -> Self {
        let mut update_internal = self;
        update_internal.net_amount = Some(
            update_internal.amount.unwrap_or(source.amount)
                + update_internal
                    .surcharge_amount
                    .or(source.surcharge_amount)
                    .unwrap_or(MinorUnit::new(0))
                + update_internal
                    .tax_amount
                    .or(source.tax_amount)
                    .unwrap_or(MinorUnit::new(0))
                + update_internal
                    .shipping_cost
                    .or(source.shipping_cost)
                    .unwrap_or(MinorUnit::new(0))
                + update_internal
                    .order_tax_amount
                    .or(source.order_tax_amount)
                    .unwrap_or(MinorUnit::new(0)),
        );
        update_internal.card_network = update_internal
            .payment_method_data
            .as_ref()
            .and_then(|data| data.as_object())
            .and_then(|card| card.get("card"))
            .and_then(|data| data.as_object())
            .and_then(|card| card.get("card_network"))
            .and_then(|network| network.as_str())
            .map(|network| network.to_string());
        update_internal
    }
}

#[cfg(feature = "v2")]
impl PaymentAttemptUpdate {
    pub fn apply_changeset(self, _source: PaymentAttempt) -> PaymentAttempt {
        todo!()
        // let PaymentAttemptUpdateInternal {
        //     net_amount,
        //     status,
        //     authentication_type,
        //     error_message,
        //     payment_method_id,
        //     cancellation_reason,
        //     modified_at: _,
        //     browser_info,
        //     payment_token,
        //     error_code,
        //     connector_metadata,
        //     payment_method_data,
        //     payment_experience,
        //     straight_through_algorithm,
        //     preprocessing_step_id,
        //     error_reason,
        //     connector_response_reference_id,
        //     multiple_capture_count,
        //     surcharge_amount,
        //     tax_amount,
        //     amount_capturable,
        //     updated_by,
        //     merchant_connector_id,
        //     authentication_data,
        //     encoded_data,
        //     unified_code,
        //     unified_message,
        //     external_three_ds_authentication_attempted,
        //     authentication_connector,
        //     authentication_id,
        //     payment_method_billing_address_id,
        //     fingerprint_id,
        //     charge_id,
        //     client_source,
        //     client_version,
        //     customer_acceptance,
        //     card_network,
        // } = PaymentAttemptUpdateInternal::from(self).populate_derived_fields(&source);
        // PaymentAttempt {
        //     net_amount: net_amount.or(source.net_amount),
        //     status: status.unwrap_or(source.status),
        //     authentication_type: authentication_type.or(source.authentication_type),
        //     error_message: error_message.unwrap_or(source.error_message),
        //     payment_method_id: payment_method_id.or(source.payment_method_id),
        //     cancellation_reason: cancellation_reason.or(source.cancellation_reason),
        //     modified_at: common_utils::date_time::now(),
        //     browser_info: browser_info.or(source.browser_info),
        //     payment_token: payment_token.or(source.payment_token),
        //     error_code: error_code.unwrap_or(source.error_code),
        //     connector_metadata: connector_metadata.or(source.connector_metadata),
        //     payment_method_data: payment_method_data.or(source.payment_method_data),
        //     payment_experience: payment_experience.or(source.payment_experience),
        //     straight_through_algorithm: straight_through_algorithm
        //         .or(source.straight_through_algorithm),
        //     preprocessing_step_id: preprocessing_step_id.or(source.preprocessing_step_id),
        //     error_reason: error_reason.unwrap_or(source.error_reason),
        //     connector_response_reference_id: connector_response_reference_id
        //         .or(source.connector_response_reference_id),
        //     multiple_capture_count: multiple_capture_count.or(source.multiple_capture_count),
        //     surcharge_amount: surcharge_amount.or(source.surcharge_amount),
        //     tax_amount: tax_amount.or(source.tax_amount),
        //     amount_capturable: amount_capturable.unwrap_or(source.amount_capturable),
        //     updated_by,
        //     merchant_connector_id: merchant_connector_id.unwrap_or(source.merchant_connector_id),
        //     authentication_data: authentication_data.or(source.authentication_data),
        //     encoded_data: encoded_data.or(source.encoded_data),
        //     unified_code: unified_code.unwrap_or(source.unified_code),
        //     unified_message: unified_message.unwrap_or(source.unified_message),
        //     external_three_ds_authentication_attempted: external_three_ds_authentication_attempted
        //         .or(source.external_three_ds_authentication_attempted),
        //     authentication_connector: authentication_connector.or(source.authentication_connector),
        //     authentication_id: authentication_id.or(source.authentication_id),
        //     payment_method_billing_address_id: payment_method_billing_address_id
        //         .or(source.payment_method_billing_address_id),
        //     fingerprint_id: fingerprint_id.or(source.fingerprint_id),
        //     charge_id: charge_id.or(source.charge_id),
        //     client_source: client_source.or(source.client_source),
        //     client_version: client_version.or(source.client_version),
        //     customer_acceptance: customer_acceptance.or(source.customer_acceptance),
        //     card_network: card_network.or(source.card_network),
        //     ..source
        // }
    }
}

#[cfg(feature = "v1")]
impl PaymentAttemptUpdate {
    pub fn apply_changeset(self, source: PaymentAttempt) -> PaymentAttempt {
        let PaymentAttemptUpdateInternal {
            amount,
            net_amount,
            currency,
            status,
            connector_transaction_id,
            amount_to_capture,
            connector,
            authentication_type,
            payment_method,
            error_message,
            payment_method_id,
            cancellation_reason,
            modified_at: _,
            mandate_id,
            browser_info,
            payment_token,
            error_code,
            connector_metadata,
            payment_method_data,
            payment_method_type,
            payment_experience,
            business_sub_label,
            straight_through_algorithm,
            preprocessing_step_id,
            error_reason,
            capture_method,
            connector_response_reference_id,
            multiple_capture_count,
            surcharge_amount,
            tax_amount,
            amount_capturable,
            updated_by,
            merchant_connector_id,
            authentication_data,
            encoded_data,
            unified_code,
            unified_message,
            external_three_ds_authentication_attempted,
            authentication_connector,
            authentication_id,
            payment_method_billing_address_id,
            fingerprint_id,
            client_source,
            client_version,
            customer_acceptance,
            card_network,
            shipping_cost,
            order_tax_amount,
            processor_transaction_data,
            connector_mandate_detail,
            capture_before,
            extended_authorization_applied,
            card_discovery,
            charges,
            issuer_error_code,
            issuer_error_message,
            setup_future_usage_applied,
        } = PaymentAttemptUpdateInternal::from(self).populate_derived_fields(&source);
        PaymentAttempt {
            amount: amount.unwrap_or(source.amount),
            net_amount: net_amount.or(source.net_amount),
            currency: currency.or(source.currency),
            status: status.unwrap_or(source.status),
            connector_transaction_id: connector_transaction_id.or(source.connector_transaction_id),
            amount_to_capture: amount_to_capture.or(source.amount_to_capture),
            connector: connector.unwrap_or(source.connector),
            authentication_type: authentication_type.or(source.authentication_type),
            payment_method: payment_method.or(source.payment_method),
            error_message: error_message.unwrap_or(source.error_message),
            payment_method_id: payment_method_id.or(source.payment_method_id),
            cancellation_reason: cancellation_reason.or(source.cancellation_reason),
            modified_at: common_utils::date_time::now(),
            mandate_id: mandate_id.or(source.mandate_id),
            browser_info: browser_info.or(source.browser_info),
            payment_token: payment_token.or(source.payment_token),
            error_code: error_code.unwrap_or(source.error_code),
            connector_metadata: connector_metadata.or(source.connector_metadata),
            payment_method_data: payment_method_data.or(source.payment_method_data),
            payment_method_type: payment_method_type.or(source.payment_method_type),
            payment_experience: payment_experience.or(source.payment_experience),
            business_sub_label: business_sub_label.or(source.business_sub_label),
            straight_through_algorithm: straight_through_algorithm
                .or(source.straight_through_algorithm),
            preprocessing_step_id: preprocessing_step_id.or(source.preprocessing_step_id),
            error_reason: error_reason.unwrap_or(source.error_reason),
            capture_method: capture_method.or(source.capture_method),
            connector_response_reference_id: connector_response_reference_id
                .or(source.connector_response_reference_id),
            multiple_capture_count: multiple_capture_count.or(source.multiple_capture_count),
            surcharge_amount: surcharge_amount.or(source.surcharge_amount),
            tax_amount: tax_amount.or(source.tax_amount),
            amount_capturable: amount_capturable.unwrap_or(source.amount_capturable),
            updated_by,
            merchant_connector_id: merchant_connector_id.unwrap_or(source.merchant_connector_id),
            authentication_data: authentication_data.or(source.authentication_data),
            encoded_data: encoded_data.or(source.encoded_data),
            unified_code: unified_code.unwrap_or(source.unified_code),
            unified_message: unified_message.unwrap_or(source.unified_message),
            external_three_ds_authentication_attempted: external_three_ds_authentication_attempted
                .or(source.external_three_ds_authentication_attempted),
            authentication_connector: authentication_connector.or(source.authentication_connector),
            authentication_id: authentication_id.or(source.authentication_id),
            payment_method_billing_address_id: payment_method_billing_address_id
                .or(source.payment_method_billing_address_id),
            fingerprint_id: fingerprint_id.or(source.fingerprint_id),
            client_source: client_source.or(source.client_source),
            client_version: client_version.or(source.client_version),
            customer_acceptance: customer_acceptance.or(source.customer_acceptance),
            card_network: card_network.or(source.card_network),
            shipping_cost: shipping_cost.or(source.shipping_cost),
            order_tax_amount: order_tax_amount.or(source.order_tax_amount),
            processor_transaction_data: processor_transaction_data
                .or(source.processor_transaction_data),
            connector_mandate_detail: connector_mandate_detail.or(source.connector_mandate_detail),
            capture_before: capture_before.or(source.capture_before),
            extended_authorization_applied: extended_authorization_applied
                .or(source.extended_authorization_applied),
            card_discovery: card_discovery.or(source.card_discovery),
            charges: charges.or(source.charges),
            issuer_error_code: issuer_error_code.or(source.issuer_error_code),
            issuer_error_message: issuer_error_message.or(source.issuer_error_message),
            setup_future_usage_applied: setup_future_usage_applied
                .or(source.setup_future_usage_applied),
            ..source
        }
    }
}

#[cfg(feature = "v2")]
impl From<PaymentAttemptUpdate> for PaymentAttemptUpdateInternal {
    fn from(_payment_attempt_update: PaymentAttemptUpdate) -> Self {
        todo!()
        // match payment_attempt_update {
        //     PaymentAttemptUpdate::Update {
        //         amount,
        //         currency,
        //         status,
        //         // connector_transaction_id,
        //         authentication_type,
        //         payment_method,
        //         payment_token,
        //         payment_method_data,
        //         payment_method_type,
        //         payment_experience,
        //         business_sub_label,
        //         amount_to_capture,
        //         capture_method,
        //         surcharge_amount,
        //         tax_amount,
        //         fingerprint_id,
        //         updated_by,
        //         payment_method_billing_address_id,
        //     } => Self {
        //         status: Some(status),
        //         // connector_transaction_id,
        //         authentication_type,
        //         payment_token,
        //         modified_at: common_utils::date_time::now(),
        //         payment_method_data,
        //         payment_experience,
        //         surcharge_amount,
        //         tax_amount,
        //         fingerprint_id,
        //         payment_method_billing_address_id,
        //         updated_by,
        //         net_amount: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::AuthenticationTypeUpdate {
        //         authentication_type,
        //         updated_by,
        //     } => Self {
        //         authentication_type: Some(authentication_type),
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         net_amount: None,
        //         status: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ConfirmUpdate {
        //         amount,
        //         currency,
        //         authentication_type,
        //         capture_method,
        //         status,
        //         payment_method,
        //         browser_info,
        //         connector,
        //         payment_token,
        //         payment_method_data,
        //         payment_method_type,
        //         payment_experience,
        //         business_sub_label,
        //         straight_through_algorithm,
        //         error_code,
        //         error_message,
        //         amount_capturable,
        //         updated_by,
        //         merchant_connector_id,
        //         surcharge_amount,
        //         tax_amount,
        //         external_three_ds_authentication_attempted,
        //         authentication_connector,
        //         authentication_id,
        //         payment_method_billing_address_id,
        //         fingerprint_id,
        //         payment_method_id,
        //         client_source,
        //         client_version,
        //         customer_acceptance,
        //     } => Self {
        //         authentication_type,
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         browser_info,
        //         payment_token,
        //         payment_method_data,
        //         payment_experience,
        //         straight_through_algorithm,
        //         error_code,
        //         error_message,
        //         amount_capturable,
        //         updated_by,
        //         merchant_connector_id: merchant_connector_id.map(Some),
        //         surcharge_amount,
        //         tax_amount,
        //         external_three_ds_authentication_attempted,
        //         authentication_connector,
        //         authentication_id,
        //         payment_method_billing_address_id,
        //         fingerprint_id,
        //         payment_method_id,
        //         client_source,
        //         client_version,
        //         customer_acceptance,
        //         net_amount: None,
        //         cancellation_reason: None,
        //         connector_metadata: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         charge_id: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::VoidUpdate {
        //         status,
        //         cancellation_reason,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         cancellation_reason,
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::RejectUpdate {
        //         status,
        //         error_code,
        //         error_message,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         error_code,
        //         error_message,
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::BlocklistUpdate {
        //         status,
        //         error_code,
        //         error_message,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         error_code,
        //         error_message,
        //         updated_by,
        //         merchant_connector_id: Some(None),
        //         net_amount: None,
        //         authentication_type: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ConnectorMandateDetailUpdate {
        //         connector_mandate_detail,
        // updated_by,
        //     } => Self {
        //         payment_method_id: None,
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         amount: None,
        //         net_amount: None,
        //         currency: None,
        //         status: None,
        //         connector_transaction_id: None,
        //         amount_to_capture: None,
        //         connector: None,
        //         authentication_type: None,
        //         payment_method: None,
        //         error_message: None,
        //         cancellation_reason: None,
        //         mandate_id: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_method_type: None,
        //         payment_experience: None,
        //         business_sub_label: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         capture_method: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //         shipping_cost: None,
        //         order_tax_amount: None,
        //         processor_transaction_data: None,
        //         connector_mandate_detail,
        //     },
        //     PaymentAttemptUpdate::ConnectorMandateDetailUpdate {
        //         payment_method_id,
        //         updated_by,
        //     } => Self {
        //         payment_method_id,
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         net_amount: None,
        //         status: None,
        //         authentication_type: None,
        //         error_message: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ResponseUpdate {
        //         status,
        //         connector,
        //         connector_transaction_id,
        //         authentication_type,
        //         payment_method_id,
        //         mandate_id,
        //         connector_metadata,
        //         payment_token,
        //         error_code,
        //         error_message,
        //         error_reason,
        //         connector_response_reference_id,
        //         amount_capturable,
        //         updated_by,
        //         authentication_data,
        //         encoded_data,
        //         unified_code,
        //         unified_message,
        //         payment_method_data,
        //         charge_id,
        //     } => Self {
        //         status: Some(status),
        //         authentication_type,
        //         payment_method_id,
        //         modified_at: common_utils::date_time::now(),
        //         connector_metadata,
        //         error_code,
        //         error_message,
        //         payment_token,
        //         error_reason,
        //         connector_response_reference_id,
        //         amount_capturable,
        //         updated_by,
        //         authentication_data,
        //         encoded_data,
        //         unified_code,
        //         unified_message,
        //         payment_method_data,
        //         charge_id,
        //         net_amount: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         merchant_connector_id: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ErrorUpdate {
        //         connector,
        //         status,
        //         error_code,
        //         error_message,
        //         error_reason,
        //         amount_capturable,
        //         updated_by,
        //         unified_code,
        //         unified_message,
        //         connector_transaction_id,
        //         payment_method_data,
        //         authentication_type,
        //     } => Self {
        //         status: Some(status),
        //         error_message,
        //         error_code,
        //         modified_at: common_utils::date_time::now(),
        //         error_reason,
        //         amount_capturable,
        //         updated_by,
        //         unified_code,
        //         unified_message,
        //         payment_method_data,
        //         authentication_type,
        //         net_amount: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         connector_metadata: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::StatusUpdate { status, updated_by } => Self {
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::UpdateTrackers {
        //         payment_token,
        //         connector,
        //         straight_through_algorithm,
        //         amount_capturable,
        //         surcharge_amount,
        //         tax_amount,
        //         updated_by,
        //         merchant_connector_id,
        //     } => Self {
        //         payment_token,
        //         modified_at: common_utils::date_time::now(),
        //         straight_through_algorithm,
        //         amount_capturable,
        //         surcharge_amount,
        //         tax_amount,
        //         updated_by,
        //         merchant_connector_id: merchant_connector_id.map(Some),
        //         net_amount: None,
        //         status: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::UnresolvedResponseUpdate {
        //         status,
        //         connector,
        //         connector_transaction_id,
        //         payment_method_id,
        //         error_code,
        //         error_message,
        //         error_reason,
        //         connector_response_reference_id,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         payment_method_id,
        //         modified_at: common_utils::date_time::now(),
        //         error_code,
        //         error_message,
        //         error_reason,
        //         connector_response_reference_id,
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::PreprocessingUpdate {
        //         status,
        //         payment_method_id,
        //         connector_metadata,
        //         preprocessing_step_id,
        //         connector_transaction_id,
        //         connector_response_reference_id,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         payment_method_id,
        //         modified_at: common_utils::date_time::now(),
        //         connector_metadata,
        //         preprocessing_step_id,
        //         connector_response_reference_id,
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         error_message: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         error_reason: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::CaptureUpdate {
        //         multiple_capture_count,
        //         updated_by,
        //         amount_to_capture,
        //     } => Self {
        //         multiple_capture_count,
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         net_amount: None,
        //         status: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::AmountToCaptureUpdate {
        //         status,
        //         amount_capturable,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         amount_capturable: Some(amount_capturable),
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ConnectorResponse {
        //         authentication_data,
        //         encoded_data,
        //         connector_transaction_id,
        //         connector,
        //         updated_by,
        //         charge_id,
        //     } => Self {
        //         authentication_data,
        //         encoded_data,
        //         modified_at: common_utils::date_time::now(),
        //         updated_by,
        //         charge_id,
        //         net_amount: None,
        //         status: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::IncrementalAuthorizationAmountUpdate {
        //         amount,
        //         amount_capturable,
        //     } => Self {
        //         modified_at: common_utils::date_time::now(),
        //         amount_capturable: Some(amount_capturable),
        //         net_amount: None,
        //         status: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         updated_by: String::default(),
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::AuthenticationUpdate {
        //         status,
        //         external_three_ds_authentication_attempted,
        //         authentication_connector,
        //         authentication_id,
        //         updated_by,
        //     } => Self {
        //         status: Some(status),
        //         modified_at: common_utils::date_time::now(),
        //         external_three_ds_authentication_attempted,
        //         authentication_connector,
        //         authentication_id,
        //         updated_by,
        //         net_amount: None,
        //         authentication_type: None,
        //         error_message: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         error_code: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         error_reason: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         unified_code: None,
        //         unified_message: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        //     PaymentAttemptUpdate::ManualUpdate {
        //         status,
        //         error_code,
        //         error_message,
        //         error_reason,
        //         updated_by,
        //         unified_code,
        //         unified_message,
        //         connector_transaction_id,
        //     } => Self {
        //         status,
        //         error_code: error_code.map(Some),
        //         modified_at: common_utils::date_time::now(),
        //         error_message: error_message.map(Some),
        //         error_reason: error_reason.map(Some),
        //         updated_by,
        //         unified_code: unified_code.map(Some),
        //         unified_message: unified_message.map(Some),
        //         net_amount: None,
        //         authentication_type: None,
        //         payment_method_id: None,
        //         cancellation_reason: None,
        //         browser_info: None,
        //         payment_token: None,
        //         connector_metadata: None,
        //         payment_method_data: None,
        //         payment_experience: None,
        //         straight_through_algorithm: None,
        //         preprocessing_step_id: None,
        //         connector_response_reference_id: None,
        //         multiple_capture_count: None,
        //         surcharge_amount: None,
        //         tax_amount: None,
        //         amount_capturable: None,
        //         merchant_connector_id: None,
        //         authentication_data: None,
        //         encoded_data: None,
        //         external_three_ds_authentication_attempted: None,
        //         authentication_connector: None,
        //         authentication_id: None,
        //         fingerprint_id: None,
        //         payment_method_billing_address_id: None,
        //         charge_id: None,
        //         client_source: None,
        //         client_version: None,
        //         customer_acceptance: None,
        //         card_network: None,
        //     },
        // }
    }
}

#[cfg(feature = "v1")]
impl From<PaymentAttemptUpdate> for PaymentAttemptUpdateInternal {
    fn from(payment_attempt_update: PaymentAttemptUpdate) -> Self {
        match payment_attempt_update {
            PaymentAttemptUpdate::Update {
                amount,
                currency,
                status,
                // connector_transaction_id,
                authentication_type,
                payment_method,
                payment_token,
                payment_method_data,
                payment_method_type,
                payment_experience,
                business_sub_label,
                amount_to_capture,
                capture_method,
                surcharge_amount,
                tax_amount,
                fingerprint_id,
                updated_by,
                payment_method_billing_address_id,
            } => Self {
                amount: Some(amount),
                currency: Some(currency),
                status: Some(status),
                // connector_transaction_id,
                authentication_type,
                payment_method,
                payment_token,
                modified_at: common_utils::date_time::now(),
                payment_method_data,
                payment_method_type,
                payment_experience,
                business_sub_label,
                amount_to_capture,
                capture_method,
                surcharge_amount,
                tax_amount,
                fingerprint_id,
                payment_method_billing_address_id,
                updated_by,
                net_amount: None,
                connector_transaction_id: None,
                connector: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                error_code: None,
                connector_metadata: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                capture_before: None,
                extended_authorization_applied: None,
                order_tax_amount: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::AuthenticationTypeUpdate {
                authentication_type,
                updated_by,
            } => Self {
                authentication_type: Some(authentication_type),
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::ConfirmUpdate {
                amount,
                currency,
                authentication_type,
                capture_method,
                status,
                payment_method,
                browser_info,
                connector,
                payment_token,
                payment_method_data,
                payment_method_type,
                payment_experience,
                business_sub_label,
                straight_through_algorithm,
                error_code,
                error_message,
                amount_capturable,
                updated_by,
                merchant_connector_id,
                surcharge_amount,
                tax_amount,
                external_three_ds_authentication_attempted,
                authentication_connector,
                authentication_id,
                payment_method_billing_address_id,
                fingerprint_id,
                payment_method_id,
                client_source,
                client_version,
                customer_acceptance,
                shipping_cost,
                order_tax_amount,
                connector_mandate_detail,
                card_discovery,
            } => Self {
                amount: Some(amount),
                currency: Some(currency),
                authentication_type,
                status: Some(status),
                payment_method,
                modified_at: common_utils::date_time::now(),
                browser_info,
                connector: connector.map(Some),
                payment_token,
                payment_method_data,
                payment_method_type,
                payment_experience,
                business_sub_label,
                straight_through_algorithm,
                error_code,
                error_message,
                amount_capturable,
                updated_by,
                merchant_connector_id: merchant_connector_id.map(Some),
                surcharge_amount,
                tax_amount,
                external_three_ds_authentication_attempted,
                authentication_connector,
                authentication_id,
                payment_method_billing_address_id,
                fingerprint_id,
                payment_method_id,
                capture_method,
                client_source,
                client_version,
                customer_acceptance,
                net_amount: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                cancellation_reason: None,
                mandate_id: None,
                connector_metadata: None,
                preprocessing_step_id: None,
                error_reason: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                card_network: None,
                shipping_cost,
                order_tax_amount,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail,
                card_discovery,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::VoidUpdate {
                status,
                cancellation_reason,
                updated_by,
            } => Self {
                status: Some(status),
                cancellation_reason,
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::RejectUpdate {
                status,
                error_code,
                error_message,
                updated_by,
            } => Self {
                status: Some(status),
                modified_at: common_utils::date_time::now(),
                error_code,
                error_message,
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::BlocklistUpdate {
                status,
                error_code,
                error_message,
                updated_by,
            } => Self {
                status: Some(status),
                modified_at: common_utils::date_time::now(),
                error_code,
                connector: Some(None),
                error_message,
                updated_by,
                merchant_connector_id: Some(None),
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                authentication_type: None,
                payment_method: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::ConnectorMandateDetailUpdate {
                connector_mandate_detail,
                updated_by,
            } => Self {
                payment_method_id: None,
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::PaymentMethodDetailsUpdate {
                payment_method_id,
                updated_by,
            } => Self {
                payment_method_id,
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::ResponseUpdate {
                status,
                connector,
                connector_transaction_id,
                authentication_type,
                payment_method_id,
                mandate_id,
                connector_metadata,
                payment_token,
                error_code,
                error_message,
                error_reason,
                connector_response_reference_id,
                amount_capturable,
                updated_by,
                authentication_data,
                encoded_data,
                unified_code,
                unified_message,
                capture_before,
                extended_authorization_applied,
                payment_method_data,
                connector_mandate_detail,
                charges,
                setup_future_usage_applied,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    status: Some(status),
                    connector: connector.map(Some),
                    connector_transaction_id,
                    authentication_type,
                    payment_method_id,
                    modified_at: common_utils::date_time::now(),
                    mandate_id,
                    connector_metadata,
                    error_code,
                    error_message,
                    payment_token,
                    error_reason,
                    connector_response_reference_id,
                    amount_capturable,
                    updated_by,
                    authentication_data,
                    encoded_data,
                    unified_code,
                    unified_message,
                    payment_method_data,
                    processor_transaction_data,
                    connector_mandate_detail,
                    charges,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    amount_to_capture: None,
                    payment_method: None,
                    cancellation_reason: None,
                    browser_info: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    preprocessing_step_id: None,
                    capture_method: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    merchant_connector_id: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    capture_before,
                    extended_authorization_applied,
                    shipping_cost: None,
                    order_tax_amount: None,
                    card_discovery: None,
                    issuer_error_code: None,
                    issuer_error_message: None,
                    setup_future_usage_applied,
                }
            }
            PaymentAttemptUpdate::ErrorUpdate {
                connector,
                status,
                error_code,
                error_message,
                error_reason,
                amount_capturable,
                updated_by,
                unified_code,
                unified_message,
                connector_transaction_id,
                payment_method_data,
                authentication_type,
                issuer_error_code,
                issuer_error_message,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    connector: connector.map(Some),
                    status: Some(status),
                    error_message,
                    error_code,
                    modified_at: common_utils::date_time::now(),
                    error_reason,
                    amount_capturable,
                    updated_by,
                    unified_code,
                    unified_message,
                    connector_transaction_id,
                    payment_method_data,
                    authentication_type,
                    processor_transaction_data,
                    issuer_error_code,
                    issuer_error_message,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    amount_to_capture: None,
                    payment_method: None,
                    payment_method_id: None,
                    cancellation_reason: None,
                    mandate_id: None,
                    browser_info: None,
                    payment_token: None,
                    connector_metadata: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    preprocessing_step_id: None,
                    capture_method: None,
                    connector_response_reference_id: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    merchant_connector_id: None,
                    authentication_data: None,
                    encoded_data: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    capture_before: None,
                    extended_authorization_applied: None,
                    shipping_cost: None,
                    order_tax_amount: None,
                    connector_mandate_detail: None,
                    card_discovery: None,
                    charges: None,
                    setup_future_usage_applied: None,
                }
            }
            PaymentAttemptUpdate::StatusUpdate { status, updated_by } => Self {
                status: Some(status),
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                capture_before: None,
                extended_authorization_applied: None,
                order_tax_amount: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::UpdateTrackers {
                payment_token,
                connector,
                straight_through_algorithm,
                amount_capturable,
                surcharge_amount,
                tax_amount,
                updated_by,
                merchant_connector_id,
            } => Self {
                payment_token,
                modified_at: common_utils::date_time::now(),
                connector: connector.map(Some),
                straight_through_algorithm,
                amount_capturable,
                surcharge_amount,
                tax_amount,
                updated_by,
                merchant_connector_id: merchant_connector_id.map(Some),
                amount: None,
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                capture_before: None,
                extended_authorization_applied: None,
                order_tax_amount: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::UnresolvedResponseUpdate {
                status,
                connector,
                connector_transaction_id,
                payment_method_id,
                error_code,
                error_message,
                error_reason,
                connector_response_reference_id,
                updated_by,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    status: Some(status),
                    connector: connector.map(Some),
                    connector_transaction_id,
                    payment_method_id,
                    modified_at: common_utils::date_time::now(),
                    error_code,
                    error_message,
                    error_reason,
                    connector_response_reference_id,
                    updated_by,
                    processor_transaction_data,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    amount_to_capture: None,
                    authentication_type: None,
                    payment_method: None,
                    cancellation_reason: None,
                    mandate_id: None,
                    browser_info: None,
                    payment_token: None,
                    connector_metadata: None,
                    payment_method_data: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    preprocessing_step_id: None,
                    capture_method: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    amount_capturable: None,
                    merchant_connector_id: None,
                    authentication_data: None,
                    encoded_data: None,
                    unified_code: None,
                    unified_message: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    shipping_cost: None,
                    capture_before: None,
                    extended_authorization_applied: None,
                    order_tax_amount: None,
                    connector_mandate_detail: None,
                    card_discovery: None,
                    charges: None,
                    issuer_error_code: None,
                    issuer_error_message: None,
                    setup_future_usage_applied: None,
                }
            }
            PaymentAttemptUpdate::PreprocessingUpdate {
                status,
                payment_method_id,
                connector_metadata,
                preprocessing_step_id,
                connector_transaction_id,
                connector_response_reference_id,
                updated_by,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    status: Some(status),
                    payment_method_id,
                    modified_at: common_utils::date_time::now(),
                    connector_metadata,
                    preprocessing_step_id,
                    connector_transaction_id,
                    connector_response_reference_id,
                    updated_by,
                    processor_transaction_data,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    amount_to_capture: None,
                    connector: None,
                    authentication_type: None,
                    payment_method: None,
                    error_message: None,
                    cancellation_reason: None,
                    mandate_id: None,
                    browser_info: None,
                    payment_token: None,
                    error_code: None,
                    payment_method_data: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    error_reason: None,
                    capture_method: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    amount_capturable: None,
                    merchant_connector_id: None,
                    authentication_data: None,
                    encoded_data: None,
                    unified_code: None,
                    unified_message: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    capture_before: None,
                    extended_authorization_applied: None,
                    shipping_cost: None,
                    order_tax_amount: None,
                    connector_mandate_detail: None,
                    card_discovery: None,
                    charges: None,
                    issuer_error_code: None,
                    issuer_error_message: None,
                    setup_future_usage_applied: None,
                }
            }
            PaymentAttemptUpdate::CaptureUpdate {
                multiple_capture_count,
                updated_by,
                amount_to_capture,
            } => Self {
                multiple_capture_count,
                modified_at: common_utils::date_time::now(),
                updated_by,
                amount_to_capture,
                amount: None,
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::AmountToCaptureUpdate {
                status,
                amount_capturable,
                updated_by,
            } => Self {
                status: Some(status),
                modified_at: common_utils::date_time::now(),
                amount_capturable: Some(amount_capturable),
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                capture_before: None,
                extended_authorization_applied: None,
                order_tax_amount: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::ConnectorResponse {
                authentication_data,
                encoded_data,
                connector_transaction_id,
                connector,
                updated_by,
                charges,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    authentication_data,
                    encoded_data,
                    connector_transaction_id,
                    connector: connector.map(Some),
                    modified_at: common_utils::date_time::now(),
                    updated_by,
                    processor_transaction_data,
                    charges,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    status: None,
                    amount_to_capture: None,
                    authentication_type: None,
                    payment_method: None,
                    error_message: None,
                    payment_method_id: None,
                    cancellation_reason: None,
                    mandate_id: None,
                    browser_info: None,
                    payment_token: None,
                    error_code: None,
                    connector_metadata: None,
                    payment_method_data: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    preprocessing_step_id: None,
                    error_reason: None,
                    capture_method: None,
                    connector_response_reference_id: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    amount_capturable: None,
                    merchant_connector_id: None,
                    unified_code: None,
                    unified_message: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    shipping_cost: None,
                    capture_before: None,
                    extended_authorization_applied: None,
                    order_tax_amount: None,
                    connector_mandate_detail: None,
                    card_discovery: None,
                    issuer_error_code: None,
                    issuer_error_message: None,
                    setup_future_usage_applied: None,
                }
            }
            PaymentAttemptUpdate::IncrementalAuthorizationAmountUpdate {
                amount,
                amount_capturable,
            } => Self {
                amount: Some(amount),
                modified_at: common_utils::date_time::now(),
                amount_capturable: Some(amount_capturable),
                net_amount: None,
                currency: None,
                status: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                updated_by: String::default(),
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::AuthenticationUpdate {
                status,
                external_three_ds_authentication_attempted,
                authentication_connector,
                authentication_id,
                updated_by,
            } => Self {
                status: Some(status),
                modified_at: common_utils::date_time::now(),
                external_three_ds_authentication_attempted,
                authentication_connector,
                authentication_id,
                updated_by,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                error_message: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                error_code: None,
                connector_metadata: None,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                error_reason: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                unified_code: None,
                unified_message: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
            PaymentAttemptUpdate::ManualUpdate {
                status,
                error_code,
                error_message,
                error_reason,
                updated_by,
                unified_code,
                unified_message,
                connector_transaction_id,
            } => {
                let (connector_transaction_id, processor_transaction_data) =
                    connector_transaction_id
                        .map(ConnectorTransactionId::form_id_and_data)
                        .map(|(txn_id, txn_data)| (Some(txn_id), txn_data))
                        .unwrap_or((None, None));
                Self {
                    status,
                    error_code: error_code.map(Some),
                    modified_at: common_utils::date_time::now(),
                    error_message: error_message.map(Some),
                    error_reason: error_reason.map(Some),
                    updated_by,
                    unified_code: unified_code.map(Some),
                    unified_message: unified_message.map(Some),
                    connector_transaction_id,
                    processor_transaction_data,
                    amount: None,
                    net_amount: None,
                    currency: None,
                    amount_to_capture: None,
                    connector: None,
                    authentication_type: None,
                    payment_method: None,
                    payment_method_id: None,
                    cancellation_reason: None,
                    mandate_id: None,
                    browser_info: None,
                    payment_token: None,
                    connector_metadata: None,
                    payment_method_data: None,
                    payment_method_type: None,
                    payment_experience: None,
                    business_sub_label: None,
                    straight_through_algorithm: None,
                    preprocessing_step_id: None,
                    capture_method: None,
                    connector_response_reference_id: None,
                    multiple_capture_count: None,
                    surcharge_amount: None,
                    tax_amount: None,
                    amount_capturable: None,
                    merchant_connector_id: None,
                    authentication_data: None,
                    encoded_data: None,
                    external_three_ds_authentication_attempted: None,
                    authentication_connector: None,
                    authentication_id: None,
                    fingerprint_id: None,
                    payment_method_billing_address_id: None,
                    client_source: None,
                    client_version: None,
                    customer_acceptance: None,
                    card_network: None,
                    shipping_cost: None,
                    capture_before: None,
                    extended_authorization_applied: None,
                    order_tax_amount: None,
                    connector_mandate_detail: None,
                    card_discovery: None,
                    charges: None,
                    issuer_error_code: None,
                    issuer_error_message: None,
                    setup_future_usage_applied: None,
                }
            }
            PaymentAttemptUpdate::PostSessionTokensUpdate {
                updated_by,
                connector_metadata,
            } => Self {
                status: None,
                error_code: None,
                modified_at: common_utils::date_time::now(),
                error_message: None,
                error_reason: None,
                updated_by,
                unified_code: None,
                unified_message: None,
                amount: None,
                net_amount: None,
                currency: None,
                connector_transaction_id: None,
                amount_to_capture: None,
                connector: None,
                authentication_type: None,
                payment_method: None,
                payment_method_id: None,
                cancellation_reason: None,
                mandate_id: None,
                browser_info: None,
                payment_token: None,
                connector_metadata,
                payment_method_data: None,
                payment_method_type: None,
                payment_experience: None,
                business_sub_label: None,
                straight_through_algorithm: None,
                preprocessing_step_id: None,
                capture_method: None,
                connector_response_reference_id: None,
                multiple_capture_count: None,
                surcharge_amount: None,
                tax_amount: None,
                amount_capturable: None,
                merchant_connector_id: None,
                authentication_data: None,
                encoded_data: None,
                external_three_ds_authentication_attempted: None,
                authentication_connector: None,
                authentication_id: None,
                fingerprint_id: None,
                payment_method_billing_address_id: None,
                client_source: None,
                client_version: None,
                customer_acceptance: None,
                card_network: None,
                shipping_cost: None,
                order_tax_amount: None,
                capture_before: None,
                extended_authorization_applied: None,
                processor_transaction_data: None,
                connector_mandate_detail: None,
                card_discovery: None,
                charges: None,
                issuer_error_code: None,
                issuer_error_message: None,
                setup_future_usage_applied: None,
            },
        }
    }
}

#[derive(Eq, PartialEq, Clone, Debug, Deserialize, Serialize, diesel::AsExpression)]
#[diesel(sql_type = diesel::sql_types::Jsonb)]
pub enum RedirectForm {
    Form {
        endpoint: String,
        method: common_utils::request::Method,
        form_fields: std::collections::HashMap<String, String>,
    },
    Html {
        html_data: String,
    },
    BlueSnap {
        payment_fields_token: String,
    },
    CybersourceAuthSetup {
        access_token: String,
        ddc_url: String,
        reference_id: String,
    },
    CybersourceConsumerAuth {
        access_token: String,
        step_up_url: String,
    },
    DeutschebankThreeDSChallengeFlow {
        acs_url: String,
        creq: String,
    },
    Payme,
    Braintree {
        client_token: String,
        card_token: String,
        bin: String,
        acs_url: String,
    },
    Nmi {
        amount: String,
        currency: common_enums::Currency,
        public_key: masking::Secret<String>,
        customer_vault_id: String,
        order_id: String,
    },
    Mifinity {
        initialization_token: String,
    },
    WorldpayDDCForm {
        endpoint: common_utils::types::Url,
        method: common_utils::request::Method,
        form_fields: std::collections::HashMap<String, String>,
        collection_id: Option<String>,
    },
}

common_utils::impl_to_sql_from_sql_json!(RedirectForm);

#[cfg(feature = "v2")]
#[derive(
    Clone, Debug, serde::Deserialize, serde::Serialize, Eq, PartialEq, diesel::AsExpression,
)]
#[diesel(sql_type = diesel::pg::sql_types::Jsonb)]
pub struct PaymentAttemptFeatureMetadata {
    pub revenue_recovery: Option<PaymentAttemptRecoveryData>,
}

#[cfg(feature = "v2")]
#[derive(
    Clone, Debug, serde::Deserialize, serde::Serialize, Eq, PartialEq, diesel::AsExpression,
)]
#[diesel(sql_type = diesel::pg::sql_types::Jsonb)]
pub struct PaymentAttemptRecoveryData {
    pub attempt_triggered_by: common_enums::TriggeredBy,
}
#[cfg(feature = "v2")]
common_utils::impl_to_sql_from_sql_json!(PaymentAttemptFeatureMetadata);

mod tests {

    #[test]
    fn test_backwards_compatibility() {
        let serialized_payment_attempt = r#"{
    "payment_id": "PMT123456789",
    "merchant_id": "M123456789",
    "attempt_id": "ATMPT123456789",
    "status": "pending",
    "amount": 10000,
    "currency": "USD",
    "save_to_locker": true,
    "connector": "stripe",
    "error_message": null,
    "offer_amount": 9500,
    "surcharge_amount": 500,
    "tax_amount": 800,
    "payment_method_id": "CRD123456789",
    "payment_method": "card",
    "connector_transaction_id": "CNTR123456789",
    "capture_method": "automatic",
    "capture_on": "2022-09-10T10:11:12Z",
    "confirm": false,
    "authentication_type": "no_three_ds",
    "created_at": "2024-02-26T12:00:00Z",
    "modified_at": "2024-02-26T12:00:00Z",
    "last_synced": null,
    "cancellation_reason": null,
    "amount_to_capture": 10000,
    "mandate_id": null,
    "browser_info": {
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36",
        "accept_header": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        "language": "nl-NL",
        "color_depth": 24,
        "screen_height": 723,
        "screen_width": 1536,
        "time_zone": 0,
        "java_enabled": true,
        "java_script_enabled": true,
        "ip_address": "127.0.0.1"
    },
    "error_code": null,
    "payment_token": "TOKEN123456789",
    "connector_metadata": null,
    "payment_experience": "redirect_to_url",
    "payment_method_type": "credit",
    "payment_method_data": {
        "card": {
            "card_number": "****************",
            "card_exp_month": "10",
            "card_cvc": "123",
            "card_exp_year": "2024",
            "card_holder_name": "John Doe"
        }
    },
    "business_sub_label": "Premium",
    "straight_through_algorithm": null,
    "preprocessing_step_id": null,
    "mandate_details": null,
    "error_reason": null,
    "multiple_capture_count": 0,
    "connector_response_reference_id": null,
    "amount_capturable": 10000,
    "updated_by": "redis_kv",
    "merchant_connector_id": "MCN123456789",
    "authentication_data": null,
    "encoded_data": null,
    "unified_code": null,
    "unified_message": null,
    "net_amount": 10200,
    "mandate_data": {
    "customer_acceptance": {
        "acceptance_type": "offline",
        "accepted_at": "1963-05-03T04:07:52.723Z",
        "online": {
            "ip_address": "127.0.0.1",
            "user_agent": "amet irure esse"
        }
    },
        "single_use": {
            "amount": 6540,
            "currency": "USD"
        }
    }
},
    "fingerprint_id": null
}"#;
        let deserialized =
            serde_json::from_str::<super::PaymentAttempt>(serialized_payment_attempt);

        assert!(deserialized.is_ok());
    }
}
