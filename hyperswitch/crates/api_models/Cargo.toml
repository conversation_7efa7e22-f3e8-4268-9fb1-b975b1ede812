[package]
name = "api_models"
description = "Request/response models for the `router` crate"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
readme = "README.md"
license.workspace = true

[features]
errors = ["dep:actix-web", "dep:reqwest"]
dummy_connector = ["euclid/dummy_connector", "common_enums/dummy_connector"]
detailed_errors = []
payouts = ["common_enums/payouts"]
frm = []
olap = []
openapi = ["common_enums/openapi", "olap", "recon", "dummy_connector", "olap"]
recon = []
v1 = ["common_utils/v1"]
v2 = ["common_types/v2", "common_utils/v2", "refunds_v2", "tokenization_v2", "dep:reqwest"]
refunds_v2 = []
dynamic_routing = []
control_center_theme = ["dep:actix-web", "dep:actix-multipart"]
revenue_recovery = []
tokenization_v2 = ["common_utils/tokenization_v2"]

[dependencies]
actix-multipart = { version = "0.6.2", optional = true }
actix-web = { version = "4.11.0", optional = true }
error-stack = "0.4.1"
mime = "0.3.17"
reqwest = { version = "0.11.27", optional = true }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
strum = { version = "0.26", features = ["derive"] }
time = { version = "0.3.41", features = ["serde", "serde-well-known", "std"] }
url = { version = "2.5.4", features = ["serde"] }
utoipa = { version = "4.2.3", features = ["preserve_order", "preserve_path_order"] }
nutype = { version = "0.4.3", features = ["serde"] }

# First party crates
cards = { version = "0.1.0", path = "../cards" }
common_enums = { version = "0.1.0", path = "../common_enums" }
common_types = { version = "0.1.0", path = "../common_types" }
common_utils = { version = "0.1.0", path = "../common_utils" }
euclid = { version = "0.1.0", path = "../euclid" }
masking = { version = "0.1.0", path = "../masking", default-features = false, features = ["alloc", "serde"] }
router_derive = { version = "0.1.0", path = "../router_derive" }

[lints]
workspace = true
