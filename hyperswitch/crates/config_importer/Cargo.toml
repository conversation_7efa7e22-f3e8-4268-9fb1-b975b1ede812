[package]
name = "config_importer"
description = "Utility to convert a TOML configuration file to a list of environment variables"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
readme = "README.md"
license.workspace = true

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
anyhow = "1.0.98"
clap = { version = "4.5.38", default-features = false, features = ["std", "derive", "help", "usage"] }
indexmap = { version = "2.9.0", optional = true }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
toml = { version = "0.8.22", default-features = false, features = ["parse"] }

[features]
default = ["preserve_order"]
preserve_order = ["dep:indexmap", "serde_json/preserve_order", "toml/preserve_order"]

[lints]
workspace = true
