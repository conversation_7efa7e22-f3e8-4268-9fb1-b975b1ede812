---
title: Go-live Checklist
---

Refer to this checklist for a seamless transition as you prepare to go live with your integration.

<Warning>
The connector configurations set up in the sandbox need to be replicated on the Hyperswitch production account.
</Warning>

### Signing of Hyperswitch services agreement

- [ ] Ensure that the Hyperswitch services agreement is signed and shared with the Hyperswitch team. In case you need any help, please drop an <NAME_EMAIL>.

<Warning>
The Hyperswitch team will share your production Hyperswitch credentials once the above process is completed.
</Warning>

### Connector Configurations

- [ ] Configure all the required connectors using production credentials on the Hyperswitch production dashboard and enable the required payment methods.
- [ ] Ensure that the payment methods are enabled on the connector (payment processor) dashboard.
- [ ] Enable raw card processing for each connector. Some connectors offer this as a dashboard toggle feature. Some processors might need you to share a PCI Attestation of Compliance over email to enable this. Drop an <NAME_EMAIL> if you need any support with PCI AOC.

### Secure your api-keys

- [ ] Make sure your secret key (api-key) is not exposed on the front-end (website/mobile app).
- [ ] Ensure that your workflow avoids the duplication or storage of your API keys in multiple locations.

### Set up Webhooks

- [ ] [Configure your webhook endpoint](https://juspay-78.mintlify.app/essentials/webhooks#configuring-webhooks) on our dashboard to receive notifications for different events.
- [ ] Update Hyperswitch's webhook endpoints on your connector's Dashboard. [Refer here](https://juspay-78.mintlify.app/essentials/webhooks#configuring-webhooks) for detailed instructions.
- [ ] Update the connector secret key in our dashboard for us to authenticate webhooks sent by your connector.

### Secure your Payments

- [ ] Make sure you decrypt and verify the signed payload sent along with the payment status in your return URL.
- [ ] Always verify the payment amount and payment status before fulfilling your customer's shopping cart/service request.

### Error Handling

- [ ] Make sure your API integration is set up to handle all the possible error scenarios (refer this [link](https://juspay-78.mintlify.app/essentials/error_codes)).
- [ ] Ensure your Unified Checkout (SDK) integration is set up to handle all the possible error scenarios (refer this [link](https://hyperswitch.io/docs/sdkIntegrations/unifiedCheckoutWeb/errorCodes)).
- [ ] Ensure that your integration can handle the entire payments lifecycle and test various scenarios using actual payment details.

### Customize and sanity check the payment experience

- [ ] Ensure the pay button is properly highlighted to the customer.
- [ ] Ensure a blended look and feel of the payment experience using the [styling APIs](https://hyperswitch.io/docs/sdkIntegrations/unifiedCheckoutWeb/customization) of Unified Checkout.
