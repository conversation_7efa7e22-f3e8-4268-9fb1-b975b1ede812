---
title: Error Codes
---

Hyperswitch uses error codes, types, and messages to communicate errors during API calls. There are two main types of error codes: Error Codes and Unified Error Codes.
1. **Error Codes** refer to the error code sent by the connector.
2. **Unified Error Codes** refer to the generic error code sent by the Hyperswitch server, based on the connector's error code. Hyperswitch groups the different error codes from connectors into more generic Unified Error Codes for structured relay of PSP errors, helping merchants derive patterns and determine the next steps for ongoing transactions.

There are four types of Error Codes and five types of Unified Error Codes.

**1. Error Codes:**
The section below contains all the error codes and their corresponding error messages.
| Error Code | Type                  | Description                                                  |
| ---------- | --------------------- | ------------------------------------------------------------ |
| IR         | Invalid Request Error | Error caused due to invalid fields and values in API request |
| CE         | Connector Error       | Errors originating from connector’s end                      |
| HE         | Hyperswitch Error     | Errors originating from Hyperswitch’s end                    |
| WE         | Webhook Error         | Errors related to Webhooks                |

| Error Codes | HTTP Status codes                     | Error Type            | Error message                                                                                                                                                       | Error Handling                                                                                                                                                                       |
| ----------- | ------------------------------------- | --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| IR_00       | 501                                   | server_not_available  | This API is under development and will be made available soon                                                                                                       | No action required. If you require this feature, please reach out to Hyperswitch support                                                                                             |
| IR_01       | 401                                   | invalid_request_error | API key not provided or invalid API key used. Provide the API key in the Authorization header using api-key (e.g api-key: API_KEY) or create a new API key from the dashboard | Provide the API key in the Authorization header using api-key (e.g api-key: API_KEY) or create a new API key from the dashboard                                                                |
| IR_02       | 404                                   | invalid_request_error | Unrecognized request URL                                                                                                                                            | Please recheck and enter the correct request URL. Refer to our API documentation                                                                                                        |
| IR_03       | 405                                   | invalid_request_error | The HTTP method is not applicable for this API                                                                                                                      | Please recheck the HTTP method used in the request. Refer to our API documentation                                                                                                      |
| IR_04       | 400                                   | invalid_request_error | Missing required param: “field_name”                                                                                                                                | Please pass the missing required parameter. Refer to our API documentation                                                                                                              |
| IR_05       | 422                                   | invalid_request_error | “field_name” contains invalid data. Expected format is “expected_format”                                                                                            | Please pass the data in the expected format. Refer to our API documentation                                                                                                                 |
| IR_06       | 400                                   | invalid_request_error | “message”                                                                                                                                                           | Refer to our API documentation for required fields and format                                                                                                                           |
| IR_07       | 400                                   | invalid_request_error | Invalid value provided: “field_name”                                                                                                                                | Provide a valid value for the required fields in the expected format. Refer to our API documentation                                                                                          |
| IR_08       | 400                                   | invalid_request_error | Client secret was not provided                                                                                                                                      | Provide the client secret received in payments/create API response                                                                                                                   |
| IR_09       | 400                                   | invalid_request_error | The client_secret provided does not match the client_secret associated with the Payment                                                                             | Provide the same client secret received in payments/create API response for the corresponding payment                                                                                |
| IR_10       | 400                                   | invalid_request_error | Customer has an existing mandate/subscription                                                                                                                       | Cancel the active mandates/subscriptions for the customer before proceeding to delete the customer data                                                                              |
| IR_11       | 400                                   | invalid_request_error | Customer has already been redacted                                                                                                                                  | Customer has already been redacted. No action required                                                                                                                               |
| IR_12       | 400                                   | invalid_request_error | Reached the maximum refund attempts                                                                                                                                 | Maximum refund attempts reached for this payment. Please contact Hyperswitch support for attempting more refunds for the same payment                                                |
| IR_13       | 400                                   | invalid_request_error | Refund amount exceeds the payment amount                                                                                                                            | Please verify and pass a refund amount equal to or less than the payment amount                                                                                                    |
| IR_14       | 400                                   | invalid_request_error | This Payment could not be “current_flow” because it has a “field_name” of “current_value”. The expected state is “states”                                           | Please verify the status of the payment and make sure that you are performing an action that is allowed for the current status of the payment                                        |
| IR_15       | 400                                   | invalid_request_error | Invalid Ephemeral Key for the customer                                                                                                                              | Please pass the right Ephemeral key for the customer                                                                                                                                 |
| IR_16       | 400                                   | invalid_request_error | “message”                                                                                                                                                           | Typically used when information involving multiple fields or previously provided information doesn’t satisfy a condition. Refer to our API documentation for required fields and format |
| IR_17       | 401                                   | invalid_request_error | Access forbidden, an invalid JWT token was used                                                                                                                     | Provide a valid JWT token to access the APIs                                                                                                                                  |
| IR_18       | 401                                   | invalid_request_error | "message"                                                                                                                                                           | The user is not authorised to update the customer, Contact Org. Admin for the appropriate access.                                                                                                                 |
| IR_19       | 400                                      | invalid_request_error | "message"                                                                                                                                                           | Please check and retry with correct details. Refer to our API documentation                                                                                                                                                                                           |
| IR_20       | 400                                      | invalid_request_error | "flow" not supported by the "connector"                                                                                                                             | Requested flow is not supported for this Connector.                                                                                                                              |
| IR_21       | 400                                      | invalid_request_error | Missing required params                                                                                                                                             | Please add the required params in the request. Refer to our API documentation                                                                                                                             |
| IR_22       | 403                                      | invalid_request_error | Access forbidden. Not authorized to access this "resource"                                                                                                          | Contact Org. Admin for the appropriate access.                                                                                                                              |
| IR_23       | 400                                      | invalid_request_error | "message"                                                                                                                                                           | Use a supported file provider. Refer to our API documentation                                                                                                                               |
| IR_24       | 422                                      | processing_error      | Invalid "wallet_name" wallet token                                                                                                                                  | Share the correct wallet token.                                                                                                                               |
| IR_25       | 400                                      | invalid_request_error | Cannot delete the default payment method                                                                                                                            | Check if the Payment method is activated. Refer to Control centre to check this.                                                                                                                             |
| IR_26       | 400                                      | invalid_request_error | Invalid Cookie                                                                                                                                                      | Recheck the site setting for the cookies.                                                                                                                              |
| IR_27       | 404                                      | invalid_request_error | Extended card info does not exist                                                                                                                                   | Recheck the card info shared in the request.                                                                                                                              |
| IR_28       | 400                                      | invalid_request_error | "message"                                                                                                                                                           | Use a valid currency. Refer to our API documentation                                                                                                                              |
| IR_29       | 422                                     | invalid_request_error | "message"                                                                                                                                                           | The data format is invalid for this request. Refer to our API documentation                                                                                                                              |
| IR_30       | 400                                      | invalid_request_error | Merchant connector account is configured with invalid config                                                                                                        | Correct the config for merchant connector account                                                                                                                              |
| IR_31       | 400                                    | invalid_request_error | Card with the provided iin does not exist                                                                                                                            | Check the IIN (Issuer Identification Number) and ensure it is correct.                                                                                                          |
| IR_32       | 400                                    | invalid_request_error | The provided card IIN length is invalid, please provide an iin with 6 or 8 digits                                                                                    | Provide a valid IIN with either 6 or 8 digits.                                                                                                                                  |
| IR_33       | 400                                    | invalid_request_error | File not found / valid in the request                                                                                                                               | Ensure the required file is included in the request and is valid. Refer to our API documentation                                                                                                               |
| IR_34       | 400                                    | invalid_request_error | Dispute id not found in the request                                                                                                                                 | Ensure that a valid dispute ID is included in the request.                                                                                                                                      |
| IR_35       | 400                                    | invalid_request_error | File purpose not found in the request or is invalid                                                                                                                 | Specify a valid file purpose in the request.                                                                                                                                    |
| IR_36       | 400                                    | invalid_request_error | File content type not found / valid                                                                                                                                 | Ensure the file content type is specified and is valid.                                                                                                                         |
| IR_37       | 404                                    | invalid_request_error | "message"                                                                                                                                                           | Check the request for the resource being accessed and ensure it exists.                                                                                                         |
| IR_38       | 400                                    | invalid_request_error | "message"                                                                                                                                                          | Check for any duplicate entries in the request and correct them.                                                                                                                |
| IR_39       | 400                                    | invalid_request_error | required payment method is not configured or configured incorrectly for all configured connectors                                                                   | Verify that the required payment method is correctly configured for all connectors in use.                                                                                       |
| CE_00       | Status codes shared by the connectors | connector_error       | “message”                                                                                                                                                           | The error code and message passed from the connectors. Refer to the respective connector’s documentation for more information on the error                                           |
| CE_01       | 400                                   | processing_error      | Payment failed during authorization with the connector. Retry payment                                                                                               | Retry the payment again as payment failed at the connector during authorization                                                                                                      |
| CE_02       | 400                                   | processing_error      | Payment failed during authentication with the connector. Retry payment                                                                                              | Retry the payment again as payment failed at the connector during authentication                                                                                                     |
| CE_03       | 400                                   | processing_error      | Capture attempt failed while processing with the connector                                                                                                          | Capture failed for the payment at the connector. Please retry the payment                                                                                                            |
| CE_04       | 400                                   | processing_error      | The card data is invalid                                                                                                                                            | Invalid card data passed. Please pass valid card data                                                                                                                                |
| CE_05       | 400                                   | processing_error      | The card has expired                                                                                                                                                | Card expired. Please pass valid card data                                                                                                                                            |
| CE_06       | 400                                   | processing_error      | Refund failed while processing with the connector. Retry refund                                                                                                     | Refund failed to process at the connector. Please retry refund                                                                                                                       |
| CE_07       | 400                                   | processing_error      | Verification failed while processing with the connector. Retry operation                                                                                            | Retry the operation again as verification failed at the connector                                                                                                                    |
| CE_08       | 400                                      | processing_error      | Dispute operation failed while processing with connector. Retry operation                                                                                           | Retry the operation again as dispute failed at the connector                                                                                                                                   |
| CE_09       | 400                                      | invalid_request_error      | Payout validation failed                                                                                                                                       | Retry the operation again with correct Payout details.                                                                                                                                    |
| HE_00       | 422,500                                   | server_not_available      | Resource not available right now, Please try again later.                                                                                                         | Please Wait for a few moments and try again. If the error still persists, please reach out to Hyperswitch support                                                                              |                                                                                                                         
| HE_01       | 400,422                                   | duplicate_request       | Requested operation(Customer, Payments, Merchants, Refunds etc.) for these identifier already exists.                                                              | Please verify the Details(Customer, Payments, Merchants, Refunds, as applicable on the basis of request) and enter valid details.                                                                                         |
| HE_02       | 404                                   | object_not_found      | Requested object(Customer, Payments, Merchants, Refunds etc.) does not exist in our records                                                                         | Please verify the Details(Customer, Payments, Merchants, Refunds, as applicable on the basis of request) and enter valid details.                                                                               |                                                                                                                         
| HE_03       | 500                                   | validation_error      | Validation Failed for the requested operation with the given details.                                                                                               | Please verify the details again and enter valid details                                                                                                                      |
| HE_04       | 404                                   | object_not_found          | Requested object(Customer, Payments, Merchants, Refunds etc.) does not exist in our records                                                                     | Please verify the Details(Customer, Payments, Merchants, Refunds, as applicable on the basis of request) and enter valid details.                                                                                                                 |
| HE_05       | 500                                   | processing_error          | Missing or invalid tenant details.                                                                                                                               | Please verify the tenant Details and try again.                                                                                                                 |
| WE_01       | 400                                   | invalid_request_error    | Failed to authenticate the webhook                                                                                                                         | Please verify the authentication credentials and try again.                                                                         |
| WE_02       | 400                                   | invalid_request_error              | Bad request received in webhook                                                                                                                            | Check the request parameters and format, then try again.                                                                            |
| WE_03       | 500                                   | router_error       | There was some issue processing the webhook                                                                                                                | Please try again later. If the issue persists, contact Hyperswitch support.                                                                     |
| WE_04       | 404                                   | object_not_found        | Webhook resource not found                                                                                                                                 | Ensure the webhook URL is correct and the resource exists.                                                                          |
| WE_05       | 400                                   | invalid_request_error     | Unable to process the webhook body                                                                                                                         | Ensure the webhook body is correctly formatted and try again.                                                                       |
| WE_06       | 400                                   | invalid_request_error   | Merchant Secret set by merchant for webhook source verification is invalid                                                                                 | Verify the Merchant Secret, then try again.                                                              |


**2. Unified Error codes:**
The section below contains all the unified error codes and their corresponding error messages.

| Unified Error Code | Unified Error Categorisation          | Unified Error message               |                                                                                                                                                                    
| ------------------ | ------------------------------------- | ----------------------------------- | 
| UE_1000            | Customer Error                        | Issue with payment method details.  |  
| UE_2000            | Connector Declines                    | Issue with Configurations.          |  
| UE_3000            | Connector Error                       | Technical issue with PSP.           | 
| UE_4000            | Integration Error                     | Issue in the integration.           | 
| UE_9000            | Others                                | Something went wrong.               |