// 英文翻译文件
let translations = {
  open Dict
  let dict = make()

  // 通用
  dict->set("loading", "Loading...")
  dict->set("save", "Save")
  dict->set("cancel", "Cancel")
  dict->set("confirm", "Confirm")
  dict->set("edit", "Edit")
  dict->set("delete", "Delete")
  dict->set("close", "Close")
  dict->set("back", "Back")
  dict->set("next", "Next")
  dict->set("submit", "Submit")
  dict->set("search", "Search")
  dict->set("filter", "Filter")
  dict->set("clear", "Clear")
  dict->set("refresh", "Refresh")
  dict->set("export", "Export")
  dict->set("import", "Import")
  dict->set("download", "Download")
  dict->set("upload", "Upload")
  dict->set("copy", "Copy")
  dict->set("copied", "Copied")
  dict->set("view", "View")
  dict->set("manage", "Manage")
  dict->set("settings", "Settings")
  dict->set("profile", "Profile")
  dict->set("language", "Language")
  dict->set("theme", "Theme")
  dict->set("logout", "Logout")
  dict->set("login", "Login")
  dict->set("signup", "Sign Up")
  dict->set("email", "Email")
  dict->set("password", "Password")
  dict->set("name", "Name")
  dict->set("username", "Username")
  dict->set("phone", "Phone")
  dict->set("address", "Address")
  dict->set("country", "Country")
  dict->set("city", "City")
  dict->set("state", "State")
  dict->set("zipcode", "Zip Code")
  dict->set("date", "Date")
  dict->set("time", "Time")
  dict->set("status", "Status")
  dict->set("active", "Active")
  dict->set("inactive", "Inactive")
  dict->set("enabled", "Enabled")
  dict->set("disabled", "Disabled")
  dict->set("yes", "Yes")
  dict->set("no", "No")
  dict->set("success", "Success")
  dict->set("error", "Error")
  dict->set("warning", "Warning")
  dict->set("info", "Info")

  // 导航和菜单
  dict->set("home", "Home")
  dict->set("dashboard", "Dashboard")
  dict->set("analytics", "Analytics")
  dict->set("transactions", "Transactions")
  dict->set("payments", "Payments")
  dict->set("connectors", "Connectors")
  dict->set("routing", "Routing")
  dict->set("customers", "Customers")
  dict->set("disputes", "Disputes")
  dict->set("refunds", "Refunds")
  dict->set("payouts", "Payouts")
  dict->set("reports", "Reports")
  dict->set("developers", "Developers")
  dict->set("api_keys", "API Keys")
  dict->set("webhooks", "Webhooks")
  dict->set("team_management", "Team Management")
  dict->set("account_settings", "Account Settings")
  dict->set("business_profile", "Business Profile")
  dict->set("payment_methods", "Payment Methods")
  dict->set("three_ds", "3DS")
  dict->set("surcharge", "Surcharge")
  dict->set("tax_processor", "Tax Processor")

  // Profile 页面
  dict->set("profile_settings", "Profile Settings")
  dict->set("manage_profile_settings", "Manage your profile settings here")
  dict->set("user_info", "User Info")
  dict->set("basic_details", "Basic Details")
  dict->set("change_password", "Change Password")
  dict->set("old_password", "Old Password")
  dict->set("new_password", "New Password")
  dict->set("confirm_password", "Confirm Password")
  dict->set("reset_password", "Reset Password")
  dict->set("two_factor_auth", "Two Factor Authentication")
  dict->set("security_settings", "Security Settings")
  dict->set("language_settings", "Language Settings")
  dict->set("select_language", "Select Language")
  dict->set("current_language", "Current Language")
  dict->set("change_language", "Change Language")
  dict->set("language_changed_successfully", "Language changed successfully")
  dict->set("language_change_failed", "Failed to change language")

  // 问候语
  dict->set("good_morning", "Good morning")
  dict->set("good_afternoon", "Good afternoon")
  dict->set("good_evening", "Good evening")
  dict->set(
    "welcome_message",
    "Welcome to the home of your Payments Control Centre. It aims at providing your team with a 360-degree view of payments.",
  )
  dict->set("great_to_see_you", "it's great to see you!")

  // 错误和成功消息
  dict->set("something_went_wrong", "Something went wrong")
  dict->set("operation_successful", "Operation completed successfully")
  dict->set("operation_failed", "Operation failed")
  dict->set("invalid_input", "Invalid input")
  dict->set("required_field", "This field is required")
  dict->set("invalid_email", "Invalid email address")
  dict->set("password_too_short", "Password is too short")
  dict->set("passwords_dont_match", "Passwords don't match")
  dict->set("unauthorized", "Unauthorized access")
  dict->set("forbidden", "Access forbidden")
  dict->set("not_found", "Not found")
  dict->set("server_error", "Server error")
  dict->set("network_error", "Network error")
  dict->set("timeout_error", "Request timeout")

  // 表格和列表
  dict->set("no_data_found", "No data found")
  dict->set("loading_data", "Loading data...")
  dict->set("rows_per_page", "Rows per page")
  dict->set("page", "Page")
  dict->set("\"of\"", "of")
  dict->set("total", "Total")
  dict->set("showing", "Showing")
  dict->set("entries", "entries")
  dict->set("first", "First")
  dict->set("last", "Last")
  dict->set("previous", "Previous")
  dict->set("sort_by", "Sort by")
  dict->set("ascending", "Ascending")
  dict->set("descending", "Descending")

  // 表单验证
  dict->set("field_required", "This field is required")
  dict->set("invalid_format", "Invalid format")
  dict->set("min_length", "Minimum length is {{min}} characters")
  dict->set("max_length", "Maximum length is {{max}} characters")
  dict->set("invalid_email_format", "Please enter a valid email address")
  dict->set("invalid_phone_format", "Please enter a valid phone number")
  dict->set("invalid_url_format", "Please enter a valid URL")

  // 确认对话框
  dict->set("are_you_sure", "Are you sure?")
  dict->set("confirm_delete", "Are you sure you want to delete this item?")
  dict->set("confirm_logout", "Are you sure you want to logout?")
  dict->set("unsaved_changes", "You have unsaved changes. Are you sure you want to leave?")
  dict->set("confirm_action", "Please confirm this action")

  // 时间和日期
  dict->set("today", "Today")
  dict->set("yesterday", "Yesterday")
  dict->set("tomorrow", "Tomorrow")
  dict->set("this_week", "This Week")
  dict->set("last_week", "Last Week")
  dict->set("this_month", "This Month")
  dict->set("last_month", "Last Month")
  dict->set("this_year", "This Year")
  dict->set("last_year", "Last Year")
  dict->set("custom_range", "Custom Range")
  dict->set("start_date", "Start Date")
  dict->set("end_date", "End Date")

  // 交易状态
  dict->set("all", "All")
  dict->set("succeeded", "Succeeded")
  dict->set("failed", "Failed")
  dict->set("dropoffs", "Dropoffs")
  dict->set("cancelled", "Cancelled")
  dict->set("pending", "Pending")
  dict->set("processing", "Processing")
  dict->set("completed", "Completed")
  dict->set("partial", "Partial")

  // 筛选器
  dict->set("add_filters", "Add Filters")
  dict->set("clear_all", "Clear All")
  dict->set("apply_filters", "Apply Filters")
  dict->set("add_custom_filters", "Add Custom Filters")
  dict->set("add_custom_filter", "Add Custom Filter")
  dict->set("custom_filter", "Custom Filter")
  dict->set("filter_by", "Filter by")
  dict->set("no_filters_applied", "No filters applied")

  // 表格操作
  dict->set("customize_columns", "Customize Columns")
  dict->set("table_columns", "Table Columns")
  dict->set("select_columns", "Select columns to display")
  dict->set("reset_to_default", "Reset to Default")
  dict->set("select_all", "Select All")
  dict->set("deselect_all", "DESELECT ALL")
  dict->set("selected", "Selected")
  dict->set("columns", "Columns")
  dict->set("search_in", "Search in")
  dict->set("options", "options")

  // 报告和下载
  dict->set("generate_reports", "Generate Reports")
  dict->set("download_csv", "Download CSV")
  dict->set("download_report", "Download Report")
  dict->set("email_sent", "Email Sent")
  dict->set("downloading", "Downloading...")
  dict->set("download_complete", "Download complete")
  dict->set("download_failed", "Download failed")

  // 商户和组织
  dict->set("add_new_merchant", "Add a new merchant")
  dict->set("merchant_name", "Merchant Name")
  dict->set("add_merchant", "Add Merchant")
  dict->set("switching_merchant", "Switching merchant...")
  dict->set("search_merchant", "Search Merchant Account or ID")
  dict->set("merchant_id", "Merchant ID")
  dict->set("organization", "Organization")
  dict->set("switch_merchant", "Switch Merchant")

  // 错误和提示消息
  dict->set("oops_something_wrong", "Oops, Something Went Wrong! Try again Later.")
  dict->set("please_use_single_quotes", "Please use ' instead of \".")
  dict->set("script_tags_not_allowed", "Script Tags are not allowed")
  dict->set("input_cannot_contain_script", "Input cannot contain <script>, </script> tags")
  dict->set("response_not_json", "Response was not a JSON object")
  dict->set("error_loading_data", "Error loading data")
  dict->set("retry", "Retry")
  dict->set("try_again", "Try again")
  dict->set("no_results", "No results")
  dict->set("no_results_found", "No results found")
  dict->set("no_results_for", "No Results for")
  dict->set("suggested_filters", "Suggested Filters")
  dict->set("view_results", "View")

  // 搜索相关
  dict->set("search_placeholder", "Search...")
  dict->set("search_results", "Search Results")
  dict->set("search_no_results", "No search results found")
  dict->set("clear_search", "Clear Search")
  dict->set("search_by", "Search by")

  // 日期选择器
  dict->set("select_date", "Select Date")
  dict->set("from", "From")
  dict->set("to", "To")
  dict->set("now", "Now")
  dict->set("date_range", "Date Range")
  dict->set("invalid_date_range", "Invalid date range")
  dict->set("start_date_after_end", "Start date cannot be after end date")

  // Payouts
  dict->set("payouts", "Payouts")
  dict->set("view_manage_payouts", "View and manage all payouts")
  dict->set("search_payout_id", "Search for payout ID")
  dict->set("payout_id", "Payout ID")
  dict->set("merchant_id", "Merchant ID")
  dict->set("amount", "Amount")
  dict->set("currency", "Currency")
  dict->set("connector", "Connector")
  dict->set("payout_type", "Payout Type")
  dict->set("billing", "Billing")
  dict->set("customer_id", "Customer ID")
  dict->set("auto_fulfill", "Auto Fulfill")
  dict->set("email", "Email")
  dict->set("name", "Name")
  dict->set("phone", "Phone")
  dict->set("phone_country_code", "Phone Country Code")
  dict->set("client_secret", "Client Secret")
  dict->set("return_url", "Return URL")
  dict->set("business_country", "Business Country")
  dict->set("business_label", "Business Label")
  dict->set("description", "Description")
  dict->set("entity_type", "Entity Type")
  dict->set("recurring", "Recurring")
  dict->set("status", "Status")
  dict->set("error_message", "Error Message")
  dict->set("error_code", "Error Code")
  dict->set("profile_id", "Profile ID")
  dict->set("created", "Created")
  dict->set("connector_transaction_id", "Connector Transaction ID")
  dict->set("send_priority", "Send Priority")
  dict->set("attempt_id", "Attempt ID")
  dict->set("payment_method", "Payment Method")
  dict->set("payout_method_type", "Payout Method Type")
  dict->set("cancellation_reason", "Cancellation Reason")
  dict->set("unified_code", "Unified Code")
  dict->set("unified_message", "Unified Message")
  dict->set("attempts", "Attempts")
  dict->set("payout_attempts", "Payout Attempts")
  dict->set("failed_to_fetch", "Failed to fetch")

  // Customers
  dict->set("customers", "Customers")
  dict->set("view_manage_customers", "View and manage all customers")
  dict->set("customer_details", "Customer Details")
  dict->set("customer_information", "Customer Information")
  dict->set("payment_methods", "Payment Methods")
  dict->set("saved_payment_methods", "Saved Payment Methods")
  dict->set("customer_name", "Customer Name")
  dict->set("customer_email", "Customer Email")
  dict->set("customer_phone", "Customer Phone")
  dict->set("customer_address", "Customer Address")
  dict->set("search_customer", "Search Customer")

  // Connectors
  dict->set("connectors", "Connectors")
  dict->set("payment_processors", "Payment Processors")
  dict->set("payout_processors", "Payout Processors")
  dict->set("fraud_risk_management", "Fraud & Risk Management")
  dict->set("connector_name", "Connector Name")
  dict->set("connector_type", "Connector Type")
  dict->set("connector_status", "Connector Status")
  dict->set("connector_configuration", "Connector Configuration")
  dict->set("test_mode", "Test Mode")
  dict->set("live_mode", "Live Mode")
  dict->set("configure_connector", "Configure Connector")
  dict->set("add_connector", "Add Connector")
  dict->set("edit_connector", "Edit Connector")
  dict->set("delete_connector", "Delete Connector")
  dict->set("connector_credentials", "Connector Credentials")
  dict->set("webhook_url", "Webhook URL")
  dict->set("api_key", "API Key")
  dict->set("secret_key", "Secret Key")
  dict->set("merchant_account_id", "Merchant Account ID")
  dict->set("test_credentials", "Test Credentials")
  dict->set("live_credentials", "Live Credentials")

  // Analytics
  dict->set("analytics", "Analytics")
  dict->set("refunds_analytics", "Refunds Analytics")
  dict->set("payment_analytics", "Payment Analytics")
  dict->set("dispute_analytics", "Dispute Analytics")
  dict->set("authentication_analytics", "Authentication Analytics")
  dict->set("refunds", "Refunds")
  dict->set("refund_id", "Refund ID")
  dict->set("refund_amount", "Refund Amount")
  dict->set("refund_status", "Refund Status")
  dict->set("refund_reason", "Refund Reason")
  dict->set("refund_type", "Refund Type")
  dict->set("refunded_amount", "Refunded Amount")
  dict->set("total_refunds", "Total Refunds")
  dict->set("successful_refunds", "Successful Refunds")
  dict->set("failed_refunds", "Failed Refunds")
  dict->set("pending_refunds", "Pending Refunds")
  dict->set("refund_rate", "Refund Rate")
  dict->set("refund_success_rate", "Refund Success Rate")

  // Workflows
  dict->set("workflows", "Workflows")
  dict->set("workflow_name", "Workflow Name")
  dict->set("workflow_status", "Workflow Status")
  dict->set("workflow_type", "Workflow Type")
  dict->set("workflow_description", "Workflow Description")
  dict->set("create_workflow", "Create Workflow")
  dict->set("edit_workflow", "Edit Workflow")
  dict->set("delete_workflow", "Delete Workflow")
  dict->set("workflow_configuration", "Workflow Configuration")
  dict->set("workflow_steps", "Workflow Steps")
  dict->set("add_step", "Add Step")
  dict->set("step_name", "Step Name")
  dict->set("step_type", "Step Type")
  dict->set("step_configuration", "Step Configuration")
  dict->set("conditions", "Conditions")
  dict->set("actions", "Actions")
  dict->set("triggers", "Triggers")
  dict->set("surcharge", "Surcharge")
  dict->set("three_ds_decision_manager", "3DS Decision Manager")

  // Connector Detail Pages
  dict->set("payment_processors", "Payment Processors")
  dict->set(
    "connect_test_processor",
    "Connect a test processor and get started with testing your payments",
  )
  dict->set("no_test_credentials", "No Test Credentials?")
  dict->set("connect_dummy_processor", "Connect a Dummy Processor")
  dict->set(
    "start_simulating_payments",
    "Start simulating payments and refunds with a dummy processor setup.",
  )
  dict->set("connect_now", "Connect Now")
  dict->set("connected_processors", "Connected Processors")
  dict->set("search_placeholder", "Search Processor or Merchant Connector Id or Connector Label")
  dict->set("integration_experience", "Tell us about your integration experience")
  dict->set("three_ds_authenticators", "3DS Authenticators")
  dict->set("fraud_risk_management", "Fraud & Risk Management")
  dict->set("payment_method_auth", "Payment Method Auth Processors")
  dict->set("processor", "Processor")
  dict->set("test_mode", "Test Mode")
  dict->set("live_mode", "Live Mode")
  dict->set("integration_status", "Integration Status")
  dict->set("disabled", "Disabled")
  dict->set("enabled", "Enabled")
  dict->set("merchant_connector_id", "Merchant Connector ID")
  dict->set("connector_label", "Connector Label")
  dict->set("payment_methods", "Payment Methods")
  dict->set("configure", "Configure")
  dict->set("view_details", "View Details")
  dict->set("edit", "Edit")
  dict->set("delete", "Delete")

  // Analytics Pages
  dict->set("payment_analytics", "Payment Analytics")
  dict->set("total_volume", "Total Volume")
  dict->set("total_count", "Total Count")
  dict->set("success_rate", "Success Rate")
  dict->set("average_ticket_size", "Average Ticket Size")
  dict->set("payment_processed_amount", "Payment Processed Amount")
  dict->set("payment_success_rate", "Payment Success Rate")
  dict->set("refund_processed_amount", "Refund Processed Amount")
  dict->set("smart_retries", "Smart Retries")
  dict->set("total_smart_retries", "Total Smart Retries")
  dict->set("smart_retries_success_rate", "Smart Retries Success Rate")
  dict->set("connector_success_rate", "Connector Success Rate")
  dict->set("payment_methods_distribution", "Payment Methods Distribution")
  dict->set("top_errors", "Top Errors")
  dict->set("failure_reasons", "Failure Reasons")

  // Reconciliation Pages
  dict->set("reconciliation", "Reconciliation")
  dict->set("recon_reports", "Recon Reports")
  dict->set("upload_file", "Upload File")
  dict->set("file_upload", "File Upload")
  dict->set("download_sample", "Download Sample")
  dict->set("file_format", "File Format")
  dict->set("processing_status", "Processing Status")
  dict->set("uploaded_at", "Uploaded At")
  dict->set("processed_at", "Processed At")
  dict->set("total_records", "Total Records")
  dict->set("matched_records", "Matched Records")
  dict->set("unmatched_records", "Unmatched Records")
  dict->set("disputed_records", "Disputed Records")

  // Developer Pages
  dict->set("developer", "Developer")
  dict->set("payment_settings", "Payment Settings")
  dict->set("api_keys", "API Keys")
  dict->set("webhooks", "Webhooks")
  dict->set("api_key_name", "API Key Name")
  dict->set("key_id", "Key ID")
  dict->set("created_on", "Created On")
  dict->set("last_used", "Last Used")
  dict->set("expires_on", "Expires On")
  dict->set("create_api_key", "Create API Key")
  dict->set("regenerate", "Regenerate")
  dict->set("revoke", "Revoke")
  dict->set("copy_key", "Copy Key")
  dict->set("webhook_url", "Webhook URL")
  dict->set("webhook_events", "Webhook Events")
  dict->set("webhook_status", "Webhook Status")
  dict->set("test_webhook", "Test Webhook")

  // Settings Pages
  dict->set("settings", "Settings")
  dict->set("payment_methods_config", "Payment Methods Configuration")
  dict->set("user_management", "User Management")
  dict->set("business_profile", "Business Profile")
  dict->set("account_settings", "Account Settings")
  dict->set("configure_payment_methods", "Configure Payment Methods")
  dict->set("enable_payment_method", "Enable Payment Method")
  dict->set("disable_payment_method", "Disable Payment Method")
  dict->set("minimum_amount", "Minimum Amount")
  dict->set("maximum_amount", "Maximum Amount")
  dict->set("supported_countries", "Supported Countries")
  dict->set("supported_currencies", "Supported Currencies")

  // User Management
  dict->set("users", "Users")
  dict->set("user_name", "User Name")
  dict->set("user_email", "User Email")
  dict->set("user_role", "User Role")
  dict->set("user_status", "User Status")
  dict->set("invite_user", "Invite User")
  dict->set("edit_user", "Edit User")
  dict->set("delete_user", "Delete User")
  dict->set("admin", "Admin")
  dict->set("operator", "Operator")
  dict->set("viewer", "Viewer")
  dict->set("active", "Active")
  dict->set("inactive", "Inactive")
  dict->set("pending", "Pending")

  // Filter Dropdown Menu
  dict->set("add_filter", "Add Filter")
  dict->set("remove_filter", "Remove Filter")
  dict->set("filter_by", "Filter By")
  dict->set("equals", "Equals")
  dict->set("not_equals", "Not Equals")
  dict->set("contains", "Contains")
  dict->set("not_contains", "Not Contains")
  dict->set("starts_with", "Starts With")
  dict->set("ends_with", "Ends With")
  dict->set("greater_than", "Greater Than")
  dict->set("less_than", "Less Than")
  dict->set("greater_equal", "Greater or Equal")
  dict->set("less_equal", "Less or Equal")
  dict->set("between", "Between")
  dict->set("is_null", "Is Null")
  dict->set("is_not_null", "Is Not Null")
  dict->set("select_operator", "Select Operator")
  dict->set("enter_value", "Enter Value")
  dict->set("select_value", "Select Value")
  dict->set("done", "Done")
  dict->set("connect", "Connect")
  dict->set(
    "connect_configure_processors",
    "Connect and configure processors to screen transactions and mitigate fraud",
  )
  dict->set("connect_and_proceed", "Connect and Proceed")
  dict->set("payments_overview", "Payments Overview")
  dict->set("amount_metrics", "Amount Metrics")
  dict->set("payments_trends", "Payments Trends")
  dict->set("payment_method_type", "Payment Method Type")
  dict->set("payment_method_plus_type", "Payment Method + Payment Method Type")
  dict->set("saved_successfully", "Saved successfully!")
  dict->set("heads_up", "Heads up!")
  dict->set(
    "override_surcharge_config",
    "This will override the existing surcharge configuration. Please confirm to proceed",
  )
  dict->set("confirm", "Confirm")
  dict->set("configure_surcharge_rules", "Configure advanced rules to apply surcharges")
  dict->set("configure_surcharge", "Configure Surcharge")
  dict->set(
    "create_surcharge_rules",
    "Create advanced rules using various payment parameters like amount, currency,payment method etc to enforce a surcharge on your payments",
  )
  dict->set("create_new", "Create New")
  dict->set("something_went_wrong", "Something went wrong")
  dict->set("configuration_saved_successfully", "Configuration saved successfully!")
  dict->set("minimum_1_rule_needed", "Minimum 1 rule needed")
  dict->set("invalid", "Invalid")
  dict->set(
    "override_3ds_config",
    "This will override the existing 3DS configuration. Please confirm to proceed",
  )
  dict->set(
    "secure_payments_3ds",
    "Make your payments more secure by enforcing 3DS authentication through custom rules defined on payment parameters",
  )
  dict->set("rule_based_configuration", "Rule Based Configuration")
  dict->set("configure_3ds_rule", "Configure 3DS Rule")
  dict->set(
    "create_3ds_rules",
    "Create advanced rules using various payment parameters like amount, currency,payment method etc to enforce 3DS authentication for specific payments to reduce fraudulent transactions",
  )
  dict->set("reconciliation", "Reconciliation")
  dict->set("settlement_reconciliation_automation", "Settlement reconciliation automation")
  dict->set("built_for_financial_accuracy", "Built for 10x financial & transactional accuracy")
  dict->set("try_demo", "Try Demo")
  dict->set("refunds_analytics", "Refunds Analytics")
  dict->set("failed_to_fetch", "Failed to Fetch!")
  dict->set("payment_settings", "Payment settings")
  dict->set(
    "setup_monitor_webhooks",
    "Set up and monitor transaction webhooks for real-time notifications",
  )
  dict->set("payment_behaviour", "Payment Behaviour")
  dict->set("three_ds", "3DS")
  dict->set("custom_headers", "Custom Headers")
  dict->set("metadata_headers", "Metadata Headers")
  dict->set("keys", "Keys")
  dict->set("manage_api_keys", "Manage API keys and credentials for integrated payment services")
  dict->set("api_keys", "API Keys")
  dict->set("create_new_api_key", "Create New API Key")
  dict->set("update", "Update")
  dict->set("create", "Create")
  dict->set("update_api_key", "Update API Key")
  dict->set("create_api_key", "Create API Key")
  dict->set("api_key_generation_failed", "Api Key Generation Failed")
  dict->set("account_settings", "Account Settings")
  dict->set(
    "manage_payment_account_config",
    "Manage payment account configuration and dashboard settings",
  )
  dict->set("configure_pmts", "Configure PMTs")
  dict->set("countries", "Countries")
  dict->set("currencies", "Currencies")
  dict->set("processing", "Processing...")
  dict->set("submit", "Submit")
  dict->set("invite_users", "Invite users")
  dict->set("users", "Users")
  dict->set("search_by_name_or_email", "Search by name or email..")
  dict->set("manage_user", "Manage user")
  dict->set("search_placeholder", "Search...")
  dict->set("add_filters", "Add Filters")
  dict->set("no_data_available", "No Data Available")
  dict->set("response_not_json", "Response was not a JSON object")
  dict->set("customize_columns", "Customize Columns")
  dict->set("clear_all", "Clear All")

  // Payout Processors
  dict->set("payout_processors", "Payout Processors")
  dict->set(
    "connect_manage_payout_processors",
    "Connect and manage payout processors for disbursements and settlements",
  )
  dict->set(
    "search_processor_merchant_connector",
    "Search Processor or Merchant Connector Id or Connector Label",
  )

  // 3DS Authentication
  dict->set("three_ds_authentication_manager", "3DS Authentication Manager")
  dict->set(
    "connect_manage_3ds_providers",
    "Connect and manage 3DS authentication providers to enhance the conversions",
  )

  // Payment Method Authentication
  dict->set("payment_method_auth_processors", "PM Authentication Processor")
  dict->set(
    "connect_configure_open_banking",
    "Connect and configure open banking providers to verify customer bank accounts",
  )

  // Tax Processor
  dict->set("tax_processor", "Tax Processor")
  dict->set("connect_configure_tax_processor", "Connect and configure Tax Processor")

  // Routing
  dict->set("active_configuration", "Active configuration")
  dict->set("manage_rules", "Manage rules")
  dict->set("no_routing_rule_configured", "No Routing Rule Configured!")
  dict->set("payout_routing_configuration", "Payout routing configuration")
  dict->set(
    "smart_routing_description",
    "Smart routing stack helps you to increase success rates and reduce costs by optimising your payment traffic across the various processors in the most customised yet reliable way. Set it up based on the preferred level of control",
  )
  dict->set("smart_routing_configuration", "Smart routing configuration")

  // Processor Cards
  dict->set("connect_new_processor", "Connect a new processor")
  dict->set("connect_dummy_processor", "Connect a Dummy Processor")
  dict->set("request_processor", "Request a Processor")
  dict->set("search_processor", "Search a processor")
  dict->set(
    "processor_not_found",
    "Uh-oh! Looks like we couldn't find the processor you were searching for.",
  )

  // Payouts
  dict->set("attempt_details", "Attempt Details")
  dict->set("payout_attempts", "Payout Attempts")
  dict->set("attempts", "Attempts")

  // Analytics
  dict->set("successful_refunds_distribution", "Successful Refunds Distribution")
  dict->set("smart_retries", "Smart Retries")
  dict->set(
    "smart_retry_note",
    "Note: Only date range filters are supported currently for Smart Retry metrics",
  )

  // Workflow
  dict->set("configure_advanced_rules_surcharge", "Configure Advanced Rules to apply surcharges")
  dict->set("for_example", "For example:")
  dict->set(
    "surcharge_example",
    "If payment_method = card && amount > 50000, apply 5% or 2500 surcharge.",
  )
  dict->set(
    "currency_unit_note",
    "Ensure to enter the payment amount and surcharge fixed amount in the smallest currency unit (e.g., cents for USD, yen for JPY). For instance, pass 100 to charge $1.00 (USD) and ¥100 (JPY) since ¥ is a zero-decimal currency.",
  )
  dict->set("rule", "Rule")

  // Vault 模块
  dict->set("vault", "Vault")
  dict->set("vault_configuration", "Vault Configuration")
  dict->set("vault_customers", "Vault Customers")
  dict->set("vault_tokens", "Vault Tokens")
  dict->set("vault_home", "Vault Home")
  dict->set("vault_overview", "Vault Overview")
  dict->set("vault_securely_store", "Securely store your users's sensitive data")
  dict->set("vault_learn_pci", "Learn how to vault cards from your Server if you're PCI compliant and learn how to vault cards using Hyperswitch's Checkout if you're non-PCI compliant")
  dict->set("processor_configuration", "Processor configuration")
  dict->set("customers_tokens", "Customers & tokens")
  dict->set("get_started", "Get Started")
  dict->set("setup_connector", "Setup {{connector}}")
  dict->set("authenticate_processor", "Authenticate Processor")
  dict->set("authenticate_processor_subtitle", "Configure your credentials from your processor dashboard. Hyperswitch encrypts and stores these credentials securely.")
  dict->set("payment_methods", "Payment Methods")
  dict->set("payment_methods_subtitle", "Configure your PaymentMethods.")
  dict->set("setup_webhook", "Setup Webhook")
  dict->set("setup_webhook_subtitle", "Configure this endpoint in the processors dashboard under webhook settings for us to receive events from the processor")
  dict->set("done", "Done")
  dict->set("generate_sample_data", "Generate Sample Data")
  dict->set("no_data_available", "No Data Available")
  dict->set("generate_sample_data_description", "You can generate sample data to gain a better understanding of the product.")
  dict->set("customer_details", "Customer Details")
  dict->set("customer_details_description", "Click on a customer entry to view their details and vaulted payment methods.")
  dict->set("customers_summary", "Customers Summary")
  dict->set("vaulted_payment_method", "Vaulted Payment Method")
  dict->set("vaulted_payment_method_description", "Click on an entry to view detailed information about a vaulted payment method.")
  dict->set("payment_method_details", "Payment Method Details")
  dict->set("psp_tokens", "PSP Tokens")
  dict->set("network_tokens", "Network Tokens")
  dict->set("psp_tokenisation", "PSP Tokenisation")
  dict->set("network_tokenisation", "Network Tokenisation")
  dict->set("pci_vault_configuration", "PCI Vault Configuration")
  dict->set("pci_vault_description", "Tokenize and secure your customers' card data in our PCI-compliant vault:")
  dict->set("advanced_vault_configuration", "Advanced Vault configuration")
  dict->set("advanced_vault_description", "Apart from storing cards in our PCI vault, you can also tokenize across PSPs and networks:")
  dict->set("network_tokenization", "Network Tokenization")
  dict->set("network_tokenization_description", "To enable this feature, please contact us on")
  dict->set("slack", "Slack")
  dict->set("request_processor", "Request a Processor")
  dict->set("request_processor_description", "To enable psp tokenisation through other processors , Click here")
  dict->set("profile", "Profile")
  dict->set("uh_oh_processor_not_found", "Uh-oh! Looks like we couldn't find the processor you were searching for.")
  dict->set("request_a_processor", "Request a processor")
  dict->set("connect_dummy_processor", "Connect a Dummy Processor")

  // Intelligent Routing 模块
  dict->set("intelligent_routing", "Intelligent Routing")
  dict->set("demo", "Demo")
  dict->set("running_intelligence_routing", "Running Intelligence Routing")
  dict->set("authentication_rate_uplift", "potential authentication rate uplift.")
  dict->set("transactions_details", "Transactions Details")
  dict->set("without_intelligence", "Without Intelligence")
  dict->set("with_intelligence", "With Intelligence")
  dict->set("change_file", "Change File")
  dict->set("insights", "Insights")
  dict->set("overall_transaction_distribution", "Overall Transaction Distribution")
  dict->set("download_template_file", "Download template file")
  dict->set("simulate_intelligent_routing", "Simulate Intelligent Routing")
  dict->set("explore_insights", "Explore Insights")
  dict->set("explore_simulator", "Explore Simulator")
  dict->set("file_uploaded", "File Uploaded")
  dict->set("upload_file", "Upload File")
  dict->set("analysing_data", "Analysing data")
  dict->set("preparing_sample_data", "Preparing sample data")
  dict->set("please_upload_file", "Please upload a file")
  dict->set("select_timestamp", "Select timestamp")

  // Recon 模块
  dict->set("reconciliation", "Reconciliation")
  dict->set("reconciliation_reports", "Reconciliation Reports")
  dict->set("setup_reconciliation", "Setup Reconciliation")
  dict->set("resolve_issue", "Resolve Issue")
  dict->set("resolved", "Resolved")
  dict->set("settlement_missing_description", "Payment Gateway processed the payment, but no matching record exists in the bank statement (Settlement Missing).")
  dict->set("reason", "Reason")
  dict->set("missing_payment_gateway", "Missing (Payment Gateway processed payment, but no bank settlement found.")
  dict->set("completed", "Completed")
  dict->set("where_fetch_data", "Where do you want to fetch your data from?")
  dict->set("total_orders", "Total Orders")
  dict->set("total_amount", "Total Amount")
  dict->set("exceptions_found", "Exceptions Found")
  dict->set("view_details", "View Details")
  dict->set("exceptions_aging", "Exceptions Aging")
  dict->set("unmatched_transactions", "Unmatched Transactions")
  dict->set("ok", "OK")
  dict->set("more_details", "More Details")
  dict->set("start_exploring", "Start exploring")
  dict->set("try_demo", "Try Demo")

  // Auth 模块
  dict->set("continue_with", "Continue with {{method}}")
  dict->set("confirm", "Confirm")
  dict->set("loading", "Loading...")
  dict->set("accept", "Accept")
  dict->set("login_to_dashboard", "Login to Dashboard")
  dict->set("continue_with_password", "Continue with Password")
  dict->set("login_with", "Login with {{method}}")
  dict->set("go_back_to_login", "Go back to login")
  dict->set("or_sign_in_using_password", "or sign in using password")
  dict->set("or_sign_in_with_magic_link", "or sign in with magic link")
  dict->set("select_field", "Select Field")
  dict->set("select_merchant", "Select Merchant")
  dict->set("add_merchant", "Add Merchant")
  dict->set("sign_in_using_password", "sign in using password")
  dict->set("sign_in_with_email", "sign in with an email")
  dict->set("recovery_codes_description", "These codes are the last resort for accessing your account in case you lose your password and second factors. If you cannot find these codes, you will lose access to your account.")
  dict->set("copy", "Copy")
  dict->set("download", "Download")
  dict->set("skip_now", "Skip now")
  dict->set("verify_recovery_code", "Verify recovery code")

  // 登录注册相关
  dict->set("welcome_back", "Welcome back to Pay Project!")
  dict->set("welcome_to", "Welcome to Pay Project")
  dict->set("new_to_hyperswitch", "New to Hyperswitch?")
  dict->set("already_using_hyperswitch", "Already using Hyperswitch?")
  dict->set("sign_up", "Sign up")
  dict->set("sign_in", "Sign in")
  dict->set("forgot_password", "Forgot Password?")
  dict->set("forgot_password_description", "Enter your email address associated with your account, and we'll send you a link to reset your password.")
  dict->set("check_inbox", "Please check your inbox")
  dict->set("reset_password", "Reset Password")
  dict->set("resend_verify_email", "Resend Verify Email")
  dict->set("magic_link_sent", "A magic link has been sent to")
  dict->set("reset_link_sent", "A reset password link has been sent to")
  dict->set("verify_link_sent", "A verify email link has been sent to")
  dict->set("powered_by", "powered by Pay_project inc.")

  // 表单字段
  dict->set("email_label", "Email")
  dict->set("email_placeholder", "Enter your Email")
  dict->set("password_label", "Password")
  dict->set("password_placeholder", "Enter your Password")
  dict->set("old_password_label", "Old Password")
  dict->set("old_password_placeholder", "Enter your Old Password")
  dict->set("new_password_label", "New Password")
  dict->set("new_password_placeholder", "Enter your New Password")
  dict->set("confirm_password_label", "Confirm Password")
  dict->set("confirm_password_placeholder", "Re-enter your Password")
  dict->set("create_password_label", "Password")
  dict->set("create_password_placeholder", "Enter your Password")

  // 通用组件
  dict->set("search", "Search")
  dict->set("select_action", "Select Action")
  dict->set("go_to_home", "Go to Home")
  // SDK页面
  dict->set("setup_checkout", "Setup Checkout")
  dict->set("checkout_details", "Checkout Details")
  dict->set("theme_customization", "Theme Customization")
  dict->set("preview", "Preview")
  dict->set("customer_id", "Customer ID")
  dict->set("guest_mode", "Guest Mode")
  dict->set("enter_customer_id", "Enter Customer ID")
  dict->set("show_saved_card", "Show Saved Card")
  dict->set("currency", "Currency")
  dict->set("select_currency", "Select Currency")
  dict->set("enter_amount", "Enter amount")
  dict->set("edit_checkout_details", "Edit Checkout Details")
  dict->set("show_preview", "Show preview")
  dict->set("for_testing_stripe_dummy", "For Testing Stripe & Dummy Connectors")
  dict->set("card_number", "Card Number :")
  dict->set("expiry", "Expiry:")
  dict->set("any_future_date", "Any future date")
  dict->set("cvc", "CVC:")
  dict->set("any_3_digits", "Any 3 Digits")
  dict->set("test_creds_other_connectors", "Test creds for other connectors here")
  dict->set("integration_type", "Integration Type")
  dict->set("select_integration_type", "Select Integration Type")
  dict->set("theme", "Theme")
  dict->set("select_theme", "Select Theme")
  dict->set("locale", "Locale")
  dict->set("select_locale", "Select Locale")
  dict->set("layout", "Layout")
  dict->set("select_layout", "Select Layout")
  dict->set("labels", "Labels")
  dict->set("select_label", "Select Label")
  dict->set("color_picker_input", "Color Picker Input")
  dict->set("capture_method", "Capture Method")
  dict->set("select_capture_method", "Select Capture Method")
  dict->set("setup_future_usage", "Setup Future Usage")
  dict->set("select_setup_future_usage", "Select Setup Future Usage")
  dict->set("authentication_type", "Authentication Type")
  dict->set("select_authentication_type", "Select Authentication Type")
  dict->set("request_external_3ds", "Request External 3DS Authentication")
  dict->set("select_value", "Select Value")
  dict->set("select_option", "Select Option")
  dict->set("enter_your_email", "Enter your Email")
  dict->set("first_name", "First Name")
  dict->set("enter_your_first_name", "Enter your First Name")
  dict->set("enter_your_name", "Enter your Name")
  dict->set("last_name", "Last Name")
  dict->set("enter_your_last_name", "Enter your Last Name")
  dict->set("address_line_1", "Address Line 1")
  dict->set("enter_your_address_line_1", "Enter your Address Line 1")
  dict->set("address_line_2", "Address Line 2")
  dict->set("enter_your_address_line_2", "Enter your Address Line 2")
  dict->set("city", "City")
  dict->set("enter_your_city", "Enter your City")
  dict->set("state", "State")
  dict->set("enter_your_state", "Enter your State")
  dict->set("country", "Country")
  dict->set("enter_your_country", "Enter your Country")
  dict->set("zipcode", "Zipcode")
  dict->set("enter_your_zipcode", "Enter your zipcode")
  dict->set("phone_number", "Phone Number")
  dict->set("select_country_code", "Select Country Code")
  dict->set("something_went_wrong_try_again", "Something went wrong. Please try again")
  dict->set("learn_more_about_customization", "Learn More About Customization")

  dict
}
