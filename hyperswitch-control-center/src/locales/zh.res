// 中文翻译文件
let translations = {
  open Dict
  let dict = make()

  // 通用
  dict->set("loading", "加载中...")
  dict->set("save", "保存")
  dict->set("cancel", "取消")
  dict->set("confirm", "确认")
  dict->set("edit", "编辑")
  dict->set("delete", "删除")
  dict->set("close", "关闭")
  dict->set("back", "返回")
  dict->set("next", "下一步")
  dict->set("submit", "提交")
  dict->set("search", "搜索")
  dict->set("filter", "筛选")
  dict->set("clear", "清除")
  dict->set("refresh", "刷新")
  dict->set("export", "导出")
  dict->set("import", "导入")
  dict->set("download", "下载")
  dict->set("upload", "上传")
  dict->set("copy", "复制")
  dict->set("copied", "已复制")
  dict->set("view", "查看")
  dict->set("manage", "管理")
  dict->set("settings", "设置")
  dict->set("profile", "个人资料")
  dict->set("language", "语言")
  dict->set("theme", "主题")
  dict->set("logout", "退出登录")
  dict->set("login", "登录")
  dict->set("signup", "注册")
  dict->set("email", "邮箱")
  dict->set("password", "密码")
  dict->set("name", "姓名")
  dict->set("username", "用户名")
  dict->set("phone", "电话")
  dict->set("address", "地址")
  dict->set("country", "国家")
  dict->set("city", "城市")
  dict->set("state", "省份")
  dict->set("zipcode", "邮政编码")
  dict->set("date", "日期")
  dict->set("time", "时间")
  dict->set("status", "状态")
  dict->set("active", "活跃")
  dict->set("inactive", "非活跃")
  dict->set("enabled", "已启用")
  dict->set("disabled", "已禁用")
  dict->set("yes", "是")
  dict->set("no", "否")
  dict->set("success", "成功")
  dict->set("error", "错误")
  dict->set("warning", "警告")
  dict->set("info", "信息")

  // 导航和菜单
  dict->set("home", "首页")
  dict->set("dashboard", "仪表板")
  dict->set("analytics", "分析")
  dict->set("transactions", "交易")
  dict->set("payments", "支付")
  dict->set("connectors", "连接器")
  dict->set("routing", "路由")
  dict->set("customers", "客户")
  dict->set("disputes", "争议")
  dict->set("refunds", "退款")
  dict->set("payouts", "付款")
  dict->set("reports", "报告")
  dict->set("developers", "开发者")
  dict->set("api_keys", "API 密钥")
  dict->set("webhooks", "Webhooks")
  dict->set("team_management", "团队管理")
  dict->set("account_settings", "账户设置")
  dict->set("business_profile", "商业档案")
  dict->set("payment_methods", "支付方式")
  dict->set("three_ds", "3DS")
  dict->set("surcharge", "附加费")
  dict->set("tax_processor", "税务处理器")

  // Profile 页面
  dict->set("profile_settings", "个人资料设置")
  dict->set("manage_profile_settings", "在这里管理您的个人资料设置")
  dict->set("user_info", "用户信息")
  dict->set("basic_details", "基本信息")
  dict->set("change_password", "修改密码")
  dict->set("old_password", "旧密码")
  dict->set("new_password", "新密码")
  dict->set("confirm_password", "确认密码")
  dict->set("reset_password", "重置密码")
  dict->set("two_factor_auth", "双因素认证")
  dict->set("security_settings", "安全设置")
  dict->set("language_settings", "语言设置")
  dict->set("select_language", "选择语言")
  dict->set("current_language", "当前语言")
  dict->set("change_language", "更改语言")
  dict->set("language_changed_successfully", "语言更改成功")
  dict->set("language_change_failed", "语言更改失败")

  // 问候语
  dict->set("good_morning", "早上好")
  dict->set("good_afternoon", "下午好")
  dict->set("good_evening", "晚上好")
  dict->set(
    "welcome_message",
    "欢迎来到您的支付控制中心。它旨在为您的团队提供支付的360度全景视图。",
  )
  dict->set("great_to_see_you", "很高兴见到您！")

  // 错误和成功消息
  dict->set("something_went_wrong", "出现了一些问题")
  dict->set("operation_successful", "操作成功完成")
  dict->set("operation_failed", "操作失败")
  dict->set("invalid_input", "输入无效")
  dict->set("required_field", "此字段为必填项")
  dict->set("invalid_email", "邮箱地址无效")
  dict->set("password_too_short", "密码太短")
  dict->set("passwords_dont_match", "密码不匹配")
  dict->set("unauthorized", "未授权访问")
  dict->set("forbidden", "访问被禁止")
  dict->set("not_found", "未找到")
  dict->set("server_error", "服务器错误")
  dict->set("network_error", "网络错误")
  dict->set("timeout_error", "请求超时")

  // 表格和列表
  dict->set("no_data_found", "未找到数据")
  dict->set("loading_data", "正在加载数据...")
  dict->set("rows_per_page", "每页行数")
  dict->set("page", "页")
  dict->set("of", "共")
  dict->set("total", "总计")
  dict->set("showing", "显示")
  dict->set("entries", "条目")
  dict->set("first", "首页")
  dict->set("last", "末页")
  dict->set("previous", "上一页")
  dict->set("sort_by", "排序方式")
  dict->set("ascending", "升序")
  dict->set("descending", "降序")

  // 表单验证
  dict->set("field_required", "此字段为必填项")
  dict->set("invalid_format", "格式无效")
  dict->set("min_length", "最小长度为 {{min}} 个字符")
  dict->set("max_length", "最大长度为 {{max}} 个字符")
  dict->set("invalid_email_format", "请输入有效的邮箱地址")
  dict->set("invalid_phone_format", "请输入有效的电话号码")
  dict->set("invalid_url_format", "请输入有效的URL")

  // 确认对话框
  dict->set("are_you_sure", "您确定吗？")
  dict->set("confirm_delete", "您确定要删除此项目吗？")
  dict->set("confirm_logout", "您确定要退出登录吗？")
  dict->set("unsaved_changes", "您有未保存的更改。确定要离开吗？")
  dict->set("confirm_action", "请确认此操作")

  // 时间和日期
  dict->set("today", "今天")
  dict->set("yesterday", "昨天")
  dict->set("tomorrow", "明天")
  dict->set("this_week", "本周")
  dict->set("last_week", "上周")
  dict->set("this_month", "本月")
  dict->set("last_month", "上月")
  dict->set("this_year", "今年")
  dict->set("last_year", "去年")
  dict->set("custom_range", "自定义范围")
  dict->set("start_date", "开始日期")
  dict->set("end_date", "结束日期")

  // 交易状态
  dict->set("all", "全部")
  dict->set("succeeded", "成功")
  dict->set("failed", "失败")
  dict->set("dropoffs", "中断")
  dict->set("cancelled", "已取消")
  dict->set("pending", "待处理")
  dict->set("processing", "处理中")
  dict->set("completed", "已完成")
  dict->set("partial", "部分")

  // 筛选器
  dict->set("add_filters", "添加筛选器")
  dict->set("clear_all", "清除全部")
  dict->set("apply_filters", "应用筛选器")
  dict->set("add_custom_filters", "添加自定义筛选器")
  dict->set("add_custom_filter", "添加自定义筛选器")
  dict->set("custom_filter", "自定义筛选器")
  dict->set("filter_by", "筛选条件")
  dict->set("no_filters_applied", "未应用筛选器")

  // 表格操作
  dict->set("customize_columns", "自定义列")
  dict->set("table_columns", "表格列")
  dict->set("select_columns", "选择要显示的列")
  dict->set("reset_to_default", "重置为默认")
  dict->set("select_all", "全选")
  dict->set("deselect_all", "取消全选")
  dict->set("selected", "已选择")
  dict->set("columns", "列")
  dict->set("search_in", "在")
  dict->set("options", "个选项中搜索")

  // 报告和下载
  dict->set("generate_reports", "生成报告")
  dict->set("download_csv", "下载 CSV")
  dict->set("download_report", "下载报告")
  dict->set("email_sent", "邮件已发送")
  dict->set("downloading", "下载中...")
  dict->set("download_complete", "下载完成")
  dict->set("download_failed", "下载失败")

  // 商户和组织
  dict->set("add_new_merchant", "添加新商户")
  dict->set("merchant_name", "商户名称")
  dict->set("add_merchant", "添加商户")
  dict->set("switching_merchant", "正在切换商户...")
  dict->set("search_merchant", "搜索商户账户或ID")
  dict->set("merchant_id", "商户ID")
  dict->set("organization", "组织")
  dict->set("switch_merchant", "切换商户")

  // 错误和提示消息
  dict->set("oops_something_wrong", "哎呀，出了点问题！请稍后再试。")
  dict->set("please_use_single_quotes", "请使用 ' 而不是 \"。")
  dict->set("script_tags_not_allowed", "不允许使用脚本标签")
  dict->set("input_cannot_contain_script", "输入不能包含 <script>, </script> 标签")
  dict->set("response_not_json", "响应不是JSON对象")
  dict->set("error_loading_data", "加载数据时出错")
  dict->set("retry", "重试")
  dict->set("try_again", "重试")
  dict->set("no_results", "无结果")
  dict->set("no_results_found", "未找到结果")
  dict->set("no_results_for", "未找到结果：")
  dict->set("suggested_filters", "建议的筛选器")
  dict->set("view_results", "查看结果")

  // 搜索相关
  dict->set("search_placeholder", "搜索...")
  dict->set("search_results", "搜索结果")
  dict->set("search_no_results", "未找到搜索结果")
  dict->set("clear_search", "清除搜索")
  dict->set("search_by", "搜索条件")

  // 日期选择器
  dict->set("select_date", "选择日期")
  dict->set("from", "从")
  dict->set("to", "到")
  dict->set("now", "现在")
  dict->set("date_range", "日期范围")
  dict->set("invalid_date_range", "无效的日期范围")
  dict->set("start_date_after_end", "开始日期不能晚于结束日期")

  // Payouts 支付
  dict->set("payouts", "付款")
  dict->set("view_manage_payouts", "查看和管理所有付款")
  dict->set("search_payout_id", "搜索支付ID")
  dict->set("payout_id", "支付ID")
  dict->set("merchant_id", "商户ID")
  dict->set("amount", "金额")
  dict->set("currency", "货币")
  dict->set("connector", "连接器")
  dict->set("payout_type", "付款类型")
  dict->set("billing", "账单")
  dict->set("customer_id", "客户ID")
  dict->set("auto_fulfill", "自动履行")
  dict->set("email", "邮箱")
  dict->set("name", "姓名")
  dict->set("phone", "电话")
  dict->set("phone_country_code", "电话国家代码")
  dict->set("client_secret", "客户端密钥")
  dict->set("return_url", "返回URL")
  dict->set("business_country", "业务国家")
  dict->set("business_label", "业务标签")
  dict->set("description", "描述")
  dict->set("entity_type", "实体类型")
  dict->set("recurring", "循环")
  dict->set("status", "状态")
  dict->set("error_message", "错误消息")
  dict->set("error_code", "错误代码")
  dict->set("profile_id", "配置文件ID")
  dict->set("created", "创建时间")
  dict->set("connector_transaction_id", "连接器交易ID")
  dict->set("send_priority", "发送优先级")
  dict->set("attempt_id", "尝试ID")
  dict->set("payment_method", "支付方式")
  dict->set("payout_method_type", "付款方式类型")
  dict->set("cancellation_reason", "取消原因")
  dict->set("unified_code", "统一代码")
  dict->set("unified_message", "统一消息")
  dict->set("attempts", "尝试")
  dict->set("payout_attempts", "付款尝试")
  dict->set("failed_to_fetch", "获取失败")

  // 客户管理
  dict->set("customers", "客户")
  dict->set("view_manage_customers", "查看和管理所有客户")
  dict->set("customer_details", "客户详情")
  dict->set("customer_information", "客户信息")
  dict->set("payment_methods", "支付方式")
  dict->set("saved_payment_methods", "已保存的支付方式")
  dict->set("customer_name", "客户姓名")
  dict->set("customer_email", "客户邮箱")
  dict->set("customer_phone", "客户电话")
  dict->set("customer_address", "客户地址")
  dict->set("search_customer", "搜索客户")

  // 连接器管理
  dict->set("connectors", "连接器")
  dict->set("payment_processors", "支付处理器")
  dict->set("payout_processors", "付款处理器")
  dict->set("fraud_risk_management", "欺诈风险管理")
  dict->set("connector_name", "连接器名称")
  dict->set("connector_type", "连接器类型")
  dict->set("connector_status", "连接器状态")
  dict->set("connector_configuration", "连接器配置")
  dict->set("test_mode", "测试模式")
  dict->set("live_mode", "生产模式")
  dict->set("configure_connector", "配置连接器")
  dict->set("add_connector", "添加连接器")
  dict->set("edit_connector", "编辑连接器")
  dict->set("delete_connector", "删除连接器")
  dict->set("connector_credentials", "连接器凭据")
  dict->set("webhook_url", "Webhook URL")
  dict->set("api_key", "API密钥")
  dict->set("secret_key", "密钥")
  dict->set("merchant_account_id", "商户账户ID")
  dict->set("test_credentials", "测试凭据")
  dict->set("live_credentials", "生产凭据")

  // 分析页面
  dict->set("analytics", "分析")
  dict->set("refunds_analytics", "退款分析")
  dict->set("payment_analytics", "支付分析")
  dict->set("dispute_analytics", "争议分析")
  dict->set("authentication_analytics", "认证分析")
  dict->set("refunds", "退款")
  dict->set("refund_id", "退款ID")
  dict->set("refund_amount", "退款金额")
  dict->set("refund_status", "退款状态")
  dict->set("refund_reason", "退款原因")
  dict->set("refund_type", "退款类型")
  dict->set("refunded_amount", "已退款金额")
  dict->set("total_refunds", "总退款数")
  dict->set("successful_refunds", "成功退款")
  dict->set("failed_refunds", "失败退款")
  dict->set("pending_refunds", "待处理退款")
  dict->set("refund_rate", "退款率")
  dict->set("refund_success_rate", "退款成功率")

  // 工作流
  dict->set("workflows", "工作流")
  dict->set("workflow_name", "工作流名称")
  dict->set("workflow_status", "工作流状态")
  dict->set("workflow_type", "工作流类型")
  dict->set("workflow_description", "工作流描述")
  dict->set("create_workflow", "创建工作流")
  dict->set("edit_workflow", "编辑工作流")
  dict->set("delete_workflow", "删除工作流")
  dict->set("workflow_configuration", "工作流配置")
  dict->set("workflow_steps", "工作流步骤")
  dict->set("add_step", "添加步骤")
  dict->set("step_name", "步骤名称")
  dict->set("step_type", "步骤类型")
  dict->set("step_configuration", "步骤配置")
  dict->set("conditions", "条件")
  dict->set("actions", "操作")
  dict->set("triggers", "触发器")
  dict->set("surcharge", "附加费")
  dict->set("three_ds_decision_manager", "3DS决策管理器")

  // 连接器详细页面
  dict->set("payment_processors", "支付处理器")
  dict->set("connect_test_processor", "连接测试处理器并开始测试您的支付")
  dict->set("no_test_credentials", "没有测试凭据？")
  dict->set("connect_dummy_processor", "连接虚拟处理器")
  dict->set(
    "start_simulating_payments",
    "使用虚拟处理器设置开始模拟支付和退款。",
  )
  dict->set("connect_now", "立即连接")
  dict->set("connected_processors", "已连接的处理器")
  dict->set("search_placeholder", "搜索处理器或商户连接器ID或连接器标签")
  dict->set("integration_experience", "告诉我们您的集成体验")
  dict->set("three_ds_authenticators", "3DS认证器")
  dict->set("fraud_risk_management", "欺诈与风险管理")
  dict->set("payment_method_auth", "支付方式认证处理器")
  dict->set("processor", "处理器")
  dict->set("test_mode", "测试模式")
  dict->set("live_mode", "生产模式")
  dict->set("integration_status", "集成状态")
  dict->set("disabled", "已禁用")
  dict->set("no_connectors_configured", "尚未配置连接器。尝试连接一个连接器。")
  dict->set("take_me_to_connectors", "带我去连接器")
  dict->set(
    "connect_configure_processors",
    "连接和配置处理器以筛选交易并减轻欺诈",
  )
  dict->set("enabled", "已启用")
  dict->set("merchant_connector_id", "商户连接器ID")
  dict->set("connector_label", "连接器标签")
  dict->set("payment_methods", "支付方式")
  dict->set("configure", "配置")
  dict->set("view_details", "查看详情")
  dict->set("edit", "编辑")
  dict->set("delete", "删除")

  // FRM 相关
  dict->set("show_advanced_settings", "显示高级设置")
  dict->set("try_again", "重试")
  dict->set("connect_and_finish", "连接并完成")
  dict->set("please_select_business_profile", "请选择您的业务配置文件")
  dict->set("back", "返回")
  dict->set("frm_player_created_successfully", "FRM播放器创建成功！")
  dict->set("details_updated", "详情已更新！")
  dict->set("connector_label_already_exists", "连接器标签已存在！")
  dict->set("successfully_saved_changes", "成功保存更改")
  dict->set("failed_to_disable_connector", "禁用连接器失败！")

  // Recon 相关
  dict->set("setup_reconciliation", "设置对账")
  dict->set(
    "recon_error_refresh_message",
    "如果您遇到任何错误，请刷新页面以解决问题。",
  )
  dict->set("refresh_recon_tab", "刷新对账标签页")
  dict->set("all", "全部")
  dict->set("exceptions", "异常")
  dict->set("report_downloaded_successfully", "报告下载成功")
  dict->set("failed_to_download_report", "下载报告失败")
  dict->set("reconciliation_reports", "对账报告")
  dict->set("download_reports", "下载报告")
  dict->set("activate_reconciliation", "激活对账")
  dict->set("streamline_reconciliation_operations", "简化您的对账和结算操作")
  dict->set("upgrade_streamline_reconciliation", "立即升级以简化您的对账和结算操作")
  dict->set(
    "recon_redirect_message",
    "您将在片刻后重定向到对账仪表板。（请在浏览器中启用弹出窗口以自动重定向。）",
  )
  dict->set("go_to_recon_tab", "前往对账标签页")
  dict->set(
    "recon_request_thank_you",
    "感谢您对我们对账模块的关注。我们正在审核您的访问请求。我们将很快就下一步与您联系。",
  )
  dict->set("failed_to_fetch_token", "获取令牌失败！")
  dict->set("drop_us_email", "给我们发邮件！")
  dict->set(
    "response_in_48_hours",
    "提交后，您应该在48小时内收到回复，通常更快。",
  )
  dict->set("send_email", "发送邮件")
  dict->set("or_contact_us_on", "或通过以下方式联系我们")

  // Payment Settings 相关
  dict->set("enter_key", "输入键")
  dict->set("enter_value", "输入值")
  dict->set("edit_current_configuration", "编辑当前配置")
  dict->set("edit_configuration_warning", "编辑当前配置将覆盖当前活动配置。")
  dict->set("proceed", "继续")
  dict->set("cancel", "取消")
  dict->set("auto_retries", "自动重试")
  dict->set("click_to_pay", "点击支付")

  // Configure PMTs 相关
  dict->set("configure_pmts_at_checkout", "在结账时配置PMTs")
  dict->set(
    "control_payment_methods_visibility",
    "控制您的支付方式在结账时的可见性",
  )

  // Users 相关
  dict->set("invite_new_users", "邀请新用户")
  dict->set("team_management", "团队管理")
  dict->set("inviting_users", "邀请用户中")
  dict->set("roles", "角色")
  dict->set("create_custom_roles", "创建自定义角色")
  dict->set(
    "manage_user_description",
    "执行各种用户相关操作，如修改角色、删除用户或发送新邀请。",
  )

  // 分析页面
  dict->set("payment_analytics", "支付分析")
  dict->set("total_volume", "总交易量")
  dict->set("total_count", "总交易数")
  dict->set("success_rate", "成功率")
  dict->set("average_ticket_size", "平均票据大小")
  dict->set("payment_processed_amount", "支付处理金额")
  dict->set("payment_success_rate", "支付成功率")
  dict->set("refund_processed_amount", "退款处理金额")
  dict->set("smart_retries", "智能重试")
  dict->set("total_smart_retries", "总智能重试数")
  dict->set("smart_retries_success_rate", "智能重试成功率")
  dict->set("connector_success_rate", "连接器成功率")
  dict->set("payment_methods_distribution", "支付方式分布")
  dict->set("top_errors", "主要错误")
  dict->set("failure_reasons", "失败原因")

  // 对账页面
  dict->set("reconciliation", "对账")
  dict->set("recon_reports", "对账报告")
  dict->set("upload_file", "上传文件")
  dict->set("file_upload", "文件上传")
  dict->set("download_sample", "下载样本")
  dict->set("file_format", "文件格式")
  dict->set("processing_status", "处理状态")
  dict->set("uploaded_at", "上传时间")
  dict->set("processed_at", "处理时间")
  dict->set("total_records", "总记录数")
  dict->set("matched_records", "匹配记录")
  dict->set("unmatched_records", "未匹配记录")
  dict->set("disputed_records", "争议记录")

  // 开发者页面
  dict->set("developer", "开发者")
  dict->set("payment_settings", "支付设置")
  dict->set("api_keys", "API密钥")
  dict->set("webhooks", "Webhooks")
  dict->set("api_key_name", "API密钥名称")
  dict->set("key_id", "密钥ID")
  dict->set("created_on", "创建时间")
  dict->set("last_used", "最后使用")
  dict->set("expires_on", "过期时间")
  dict->set("create_api_key", "创建API密钥")
  dict->set("regenerate", "重新生成")
  dict->set("revoke", "撤销")
  dict->set("copy_key", "复制密钥")
  dict->set("webhook_url", "Webhook URL")
  dict->set("webhook_events", "Webhook事件")
  dict->set("webhook_status", "Webhook状态")
  dict->set("test_webhook", "测试Webhook")

  // 设置页面
  dict->set("settings", "设置")
  dict->set("payment_methods_config", "支付方式配置")
  dict->set("user_management", "用户管理")
  dict->set("business_profile", "业务配置文件")
  dict->set("account_settings", "账户设置")
  dict->set("configure_payment_methods", "配置支付方式")
  dict->set("enable_payment_method", "启用支付方式")
  dict->set("disable_payment_method", "禁用支付方式")
  dict->set("minimum_amount", "最小金额")
  dict->set("maximum_amount", "最大金额")
  dict->set("supported_countries", "支持的国家")
  dict->set("supported_currencies", "支持的货币")

  // 用户管理
  dict->set("users", "用户")
  dict->set("user_name", "用户名")
  dict->set("user_email", "用户邮箱")
  dict->set("user_role", "用户角色")
  dict->set("user_status", "用户状态")
  dict->set("invite_user", "邀请用户")
  dict->set("edit_user", "编辑用户")
  dict->set("delete_user", "删除用户")
  dict->set("admin", "管理员")
  dict->set("operator", "操作员")
  dict->set("viewer", "查看者")
  dict->set("active", "活跃")
  dict->set("inactive", "非活跃")
  dict->set("pending", "待处理")

  // 筛选器下拉菜单
  dict->set("add_filter", "添加筛选器")
  dict->set("remove_filter", "移除筛选器")
  dict->set("filter_by", "筛选条件")
  dict->set("equals", "等于")
  dict->set("not_equals", "不等于")
  dict->set("contains", "包含")
  dict->set("not_contains", "不包含")
  dict->set("starts_with", "开始于")
  dict->set("ends_with", "结束于")
  dict->set("greater_than", "大于")
  dict->set("less_than", "小于")
  dict->set("greater_equal", "大于等于")
  dict->set("less_equal", "小于等于")
  dict->set("between", "介于")
  dict->set("is_null", "为空")
  dict->set("is_not_null", "不为空")
  dict->set("select_operator", "选择操作符")
  dict->set("enter_value", "输入值")
  dict->set("select_value", "选择值")
  dict->set("done", "完成")
  dict->set("connect", "连接")
  dict->set(
    "connect_configure_processors",
    "连接和配置处理器以筛选交易并减轻欺诈",
  )
  dict->set("connect_and_proceed", "连接并继续")
  dict->set("payments_overview", "支付概览")
  dict->set("amount_metrics", "金额指标")
  dict->set("payments_trends", "支付趋势")
  dict->set("payment_method_type", "支付方式类型")
  dict->set("payment_method_plus_type", "支付方式 + 支付方式类型")
  dict->set("saved_successfully", "保存成功！")
  dict->set("heads_up", "注意！")
  dict->set("override_surcharge_config", "这将覆盖现有的附加费配置。请确认继续")
  dict->set("confirm", "确认")
  dict->set("configure_surcharge_rules", "配置高级规则以应用附加费")
  dict->set("configure_surcharge", "配置附加费")
  dict->set(
    "create_surcharge_rules",
    "使用各种支付参数（如金额、货币、支付方式等）创建高级规则，以对您的支付强制收取附加费",
  )
  dict->set("create_new", "创建新的")
  dict->set("something_went_wrong", "出了点问题")
  dict->set("configuration_saved_successfully", "配置保存成功！")
  dict->set("minimum_1_rule_needed", "至少需要1条规则")
  dict->set("invalid", "无效")
  dict->set("override_3ds_config", "这将覆盖现有的3DS配置。请确认继续")
  dict->set(
    "secure_payments_3ds",
    "通过在支付参数上定义的自定义规则强制执行3DS认证，使您的支付更加安全",
  )
  dict->set("rule_based_configuration", "基于规则的配置")
  dict->set("configure_3ds_rule", "配置3DS规则")
  dict->set(
    "create_3ds_rules",
    "使用各种支付参数（如金额、货币、支付方式等）创建高级规则，以对特定支付强制执行3DS认证，减少欺诈交易",
  )
  dict->set("reconciliation", "对账")
  dict->set("settlement_reconciliation_automation", "结算对账自动化")
  dict->set("built_for_financial_accuracy", "为10倍金融和交易准确性而构建")
  dict->set("try_demo", "试用演示")
  dict->set("refunds_analytics", "退款分析")
  dict->set("failed_to_fetch", "获取失败！")
  dict->set("payment_settings", "支付设置")
  dict->set("setup_monitor_webhooks", "设置和监控交易Webhook以获得实时通知")
  dict->set("payment_behaviour", "支付行为")
  dict->set("three_ds", "3DS")
  dict->set("custom_headers", "自定义标头")
  dict->set("metadata_headers", "元数据标头")
  dict->set(
    "click_to_pay_description",
    "点击支付是一种安全、无缝的数字支付解决方案，让客户无需输入详细信息即可使用保存的卡片快速结账",
  )
  dict->set("click_to_pay_connector_id", "点击支付 - 连接器ID")
  dict->set("select_click_to_pay_connector", "选择点击支付 - 连接器ID")
  dict->set("profile_name", "配置文件名称")
  dict->set("profile_id", "配置文件ID")
  dict->set("merchant_id", "商户ID")
  dict->set("payment_response_hash_key", "支付响应哈希密钥")
  dict->set("collect_billing_details_from_wallets", "从钱包收集账单详细信息")
  dict->set(
    "enable_billing_collection_description",
    "当客户连接钱包时启用账单信息的自动收集",
  )
  dict->set("collect_shipping_details_from_wallets", "从钱包收集配送详细信息")
  dict->set(
    "enable_shipping_collection_description",
    "当客户连接钱包时启用配送信息的自动收集",
  )
  dict->set("only_if_required_by_connector", "仅在连接器要求时")
  dict->set("custom_metadata_headers", "自定义元数据标头")
  dict->set(
    "editing_configuration_override_warning",
    "编辑当前配置将覆盖当前活动配置。",
  )
  dict->set("proceed", "继续")
  dict->set("details_updated", "详细信息已更新")
  dict->set("failed_to_update", "更新失败")
  dict->set("keys", "密钥")
  dict->set("manage_api_keys", "管理集成支付服务的API密钥和凭据")
  dict->set("api_keys", "API密钥")
  dict->set("create_new_api_key", "创建新的API密钥")
  dict->set("update", "更新")
  dict->set("create", "创建")
  dict->set("update_api_key", "更新API密钥")
  dict->set("create_api_key", "创建API密钥")
  dict->set("api_key_generation_failed", "API密钥生成失败")
  dict->set("account_settings", "账户设置")
  dict->set("manage_payment_account_config", "管理支付账户配置和仪表板设置")
  dict->set("configure_pmts", "配置PMTs")
  dict->set("countries", "国家")
  dict->set("currencies", "货币")
  dict->set("processing", "处理中...")
  dict->set("submit", "提交")
  dict->set("invite_users", "邀请用户")
  dict->set("users", "用户")
  dict->set("search_by_name_or_email", "按姓名或邮箱搜索...")
  dict->set("manage_user", "管理用户")
  dict->set("search_placeholder", "搜索...")
  dict->set("add_filters", "添加筛选器")
  dict->set("no_data_available", "暂无数据")
  dict->set("response_not_json", "响应不是JSON对象")
  dict->set("customize_columns", "自定义列")
  dict->set("clear_all", "清除全部")

  // 支付处理器
  dict->set("payout_processors", "支付处理器")
  dict->set(
    "connect_manage_payout_processors",
    "连接和管理用于支付和结算的支付处理器",
  )
  dict->set(
    "search_processor_merchant_connector",
    "搜索处理器或商户连接器ID或连接器标签",
  )

  // 3DS认证
  dict->set("three_ds_authentication_manager", "3DS认证管理器")
  dict->set("connect_manage_3ds_providers", "连接和管理3DS认证提供商以提高转化率")

  // 支付方式认证
  dict->set("payment_method_auth_processors", "支付方式认证处理器")
  dict->set(
    "connect_configure_open_banking",
    "连接和配置开放银行提供商以验证客户银行账户",
  )

  // 税务处理器
  dict->set("tax_processor", "税务处理器")
  dict->set("connect_configure_tax_processor", "连接和配置税务处理器")

  // 路由
  dict->set("active_configuration", "活动配置")
  dict->set("manage_rules", "管理规则")
  dict->set("no_routing_rule_configured", "未配置路由规则！")
  dict->set("payout_routing_configuration", "支付路由配置")
  dict->set(
    "smart_routing_description",
    "智能路由堆栈通过以最定制化且可靠的方式优化您在各种处理器之间的支付流量，帮助您提高成功率并降低成本。根据您偏好的控制级别进行设置",
  )
  dict->set("smart_routing_configuration", "智能路由配置")

  // 处理器卡片
  dict->set("connect_new_processor", "连接新处理器")
  dict->set("connect_dummy_processor", "连接虚拟处理器")
  dict->set("request_processor", "请求处理器")
  dict->set("search_processor", "搜索处理器")
  dict->set("processor_not_found", "哎呀！看起来我们找不到您要搜索的处理器。")

  // 支付
  dict->set("attempt_details", "尝试详情")
  dict->set("payout_attempts", "支付尝试")
  dict->set("attempts", "尝试")

  // 分析
  dict->set("successful_refunds_distribution", "成功退款分布")
  dict->set("smart_retries", "智能重试")
  dict->set("smart_retry_note", "注意：智能重试指标目前仅支持日期范围筛选")

  // 工作流
  dict->set("configure_advanced_rules_surcharge", "配置高级规则以应用附加费")
  dict->set("for_example", "例如：")
  dict->set(
    "surcharge_example",
    "如果 payment_method = card && amount > 50000，则应用 5% 或 2500 附加费。",
  )
  dict->set(
    "currency_unit_note",
    "确保以最小货币单位输入支付金额和附加费固定金额（例如，美元为分，日元为日元）。例如，传递 100 来收取 $1.00（美元）和 ¥100（日元），因为 ¥ 是零小数货币。",
  )
  dict->set("rule", "规则")

  // Vault 模块
  dict->set("vault", "保险库")
  dict->set("vault_configuration", "保险库配置")
  dict->set("vault_customers", "保险库客户")
  dict->set("vault_tokens", "保险库令牌")
  dict->set("vault_home", "保险库首页")
  dict->set("vault_overview", "保险库概览")
  dict->set("vault_securely_store", "安全存储您用户的敏感数据")
  dict->set("vault_learn_pci", "如果您符合PCI标准，了解如何从服务器保管卡；如果您不符合PCI标准，了解如何使用Hyperswitch的结账功能保管卡")
  dict->set("processor_configuration", "处理器配置")
  dict->set("customers_tokens", "客户和令牌")
  dict->set("get_started", "开始使用")
  dict->set("setup_connector", "设置 {{connector}}")
  dict->set("authenticate_processor", "认证处理器")
  dict->set("authenticate_processor_subtitle", "从您的处理器仪表板配置您的凭据。Hyperswitch加密并安全存储这些凭据。")
  dict->set("payment_methods", "支付方式")
  dict->set("payment_methods_subtitle", "配置您的支付方式。")
  dict->set("setup_webhook", "设置Webhook")
  dict->set("setup_webhook_subtitle", "在处理器仪表板的webhook设置中配置此端点，以便我们接收来自处理器的事件")
  dict->set("done", "完成")
  dict->set("generate_sample_data", "生成示例数据")
  dict->set("no_data_available", "暂无数据")
  dict->set("generate_sample_data_description", "您可以生成示例数据以更好地了解产品。")
  dict->set("customer_details", "客户详情")
  dict->set("customer_details_description", "点击客户条目查看其详情和保管的支付方式。")
  dict->set("customers_summary", "客户摘要")
  dict->set("vaulted_payment_method", "保管的支付方式")
  dict->set("vaulted_payment_method_description", "点击条目查看保管支付方式的详细信息。")
  dict->set("payment_method_details", "支付方式详情")
  dict->set("psp_tokens", "PSP令牌")
  dict->set("network_tokens", "网络令牌")
  dict->set("psp_tokenisation", "PSP令牌化")
  dict->set("network_tokenisation", "网络令牌化")
  dict->set("pci_vault_configuration", "PCI保险库配置")
  dict->set("pci_vault_description", "在我们的PCI合规保险库中令牌化并保护您客户的卡数据：")
  dict->set("advanced_vault_configuration", "高级保险库配置")
  dict->set("advanced_vault_description", "除了在我们的PCI保险库中存储卡外，您还可以在PSP和网络之间进行令牌化：")
  dict->set("network_tokenization", "网络令牌化")
  dict->set("network_tokenization_description", "要启用此功能，请联系我们")
  dict->set("slack", "Slack")
  dict->set("request_processor", "请求处理器")
  dict->set("request_processor_description", "要通过其他处理器启用psp令牌化，请点击此处")
  dict->set("profile", "档案")
  dict->set("uh_oh_processor_not_found", "哎呀！看起来我们找不到您搜索的处理器。")
  dict->set("request_a_processor", "请求处理器")
  dict->set("connect_dummy_processor", "连接虚拟处理器")

  // Intelligent Routing 模块
  dict->set("intelligent_routing", "智能路由")
  dict->set("demo", "演示")
  dict->set("running_intelligence_routing", "运行智能路由")
  dict->set("authentication_rate_uplift", "潜在认证率提升。")
  dict->set("transactions_details", "交易详情")
  dict->set("without_intelligence", "无智能")
  dict->set("with_intelligence", "有智能")
  dict->set("change_file", "更改文件")
  dict->set("insights", "洞察")
  dict->set("overall_transaction_distribution", "整体交易分布")
  dict->set("download_template_file", "下载模板文件")
  dict->set("simulate_intelligent_routing", "模拟智能路由")
  dict->set("explore_insights", "探索洞察")
  dict->set("explore_simulator", "探索模拟器")
  dict->set("file_uploaded", "文件已上传")
  dict->set("upload_file", "上传文件")
  dict->set("analysing_data", "分析数据中")
  dict->set("preparing_sample_data", "准备示例数据")
  dict->set("please_upload_file", "请上传文件")
  dict->set("select_timestamp", "选择时间戳")

  // Recon 模块
  dict->set("reconciliation", "对账")
  dict->set("reconciliation_reports", "对账报告")
  dict->set("setup_reconciliation", "设置对账")
  dict->set("resolve_issue", "解决问题")
  dict->set("resolved", "已解决")
  dict->set("settlement_missing_description", "支付网关处理了支付，但银行对账单中没有匹配的记录（结算缺失）。")
  dict->set("reason", "原因")
  dict->set("missing_payment_gateway", "缺失（支付网关处理了支付，但未找到银行结算。）")
  dict->set("completed", "已完成")
  dict->set("where_fetch_data", "您想从哪里获取数据？")
  dict->set("total_orders", "总订单")
  dict->set("total_amount", "总金额")
  dict->set("exceptions_found", "发现异常")
  dict->set("view_details", "查看详情")
  dict->set("exceptions_aging", "异常老化")
  dict->set("unmatched_transactions", "未匹配交易")
  dict->set("ok", "确定")
  dict->set("more_details", "更多详情")
  dict->set("start_exploring", "开始探索")
  dict->set("try_demo", "试用演示")

  // Auth 模块
  dict->set("continue_with", "继续使用 {{method}}")
  dict->set("confirm", "确认")
  dict->set("loading", "加载中...")
  dict->set("accept", "接受")
  dict->set("login_to_dashboard", "登录到仪表板")
  dict->set("continue_with_password", "使用密码继续")
  dict->set("login_with", "使用 {{method}} 登录")
  dict->set("go_back_to_login", "返回登录")
  dict->set("or_sign_in_using_password", "或使用密码登录")
  dict->set("or_sign_in_with_magic_link", "或使用魔法链接登录")
  dict->set("select_field", "选择字段")
  dict->set("select_merchant", "选择商户")
  dict->set("add_merchant", "添加商户")
  dict->set("sign_in_using_password", "使用密码登录")
  dict->set("sign_in_with_email", "使用邮箱登录")
  dict->set("recovery_codes_description", "这些代码是在您丢失密码和第二因素时访问账户的最后手段。如果您找不到这些代码，您将失去对账户的访问权限。")
  dict->set("copy", "复制")
  dict->set("download", "下载")
  dict->set("skip_now", "现在跳过")
  dict->set("verify_recovery_code", "验证恢复代码")

  // 登录注册相关
  dict->set("welcome_back", "欢迎回到 Pay Project！")
  dict->set("welcome_to", "欢迎来到 Pay Project")
  dict->set("new_to_hyperswitch", "新用户？")
  dict->set("already_using_hyperswitch", "已经在使用 Hyperswitch？")
  dict->set("sign_up", "注册")
  dict->set("sign_in", "登录")
  dict->set("forgot_password", "忘记密码？")
  dict->set("forgot_password_description", "输入与您账户关联的邮箱地址，我们将向您发送重置密码的链接。")
  dict->set("check_inbox", "请检查您的收件箱")
  dict->set("reset_password", "重置密码")
  dict->set("resend_verify_email", "重新发送验证邮件")
  dict->set("magic_link_sent", "魔法链接已发送到")
  dict->set("reset_link_sent", "重置密码链接已发送到")
  dict->set("verify_link_sent", "验证邮件链接已发送到")
  dict->set("powered_by", "由 Pay_project inc. 提供支持")

  // 表单字段
  dict->set("email_label", "邮箱")
  dict->set("email_placeholder", "输入您的邮箱")
  dict->set("password_label", "密码")
  dict->set("password_placeholder", "输入您的密码")
  dict->set("old_password_label", "旧密码")
  dict->set("old_password_placeholder", "输入您的旧密码")
  dict->set("new_password_label", "新密码")
  dict->set("new_password_placeholder", "输入您的新密码")
  dict->set("confirm_password_label", "确认密码")
  dict->set("confirm_password_placeholder", "重新输入您的密码")
  dict->set("create_password_label", "密码")
  dict->set("create_password_placeholder", "输入您的密码")

  // 通用组件
  dict->set("search", "搜索")
  dict->set("select_action", "选择操作")
  dict->set("go_to_home", "返回首页")

  // NewAnalytics 模块翻译
  // 分析页面实体标题
  dict->set("overview_section", "概览部分")
  dict->set("payments_lifecycle", "支付生命周期")
  dict->set("payments_processed", "已处理支付")
  dict->set("payments_success_rate", "支付成功率")
  dict->set("successful_payments_distribution", "成功支付分布")
  dict->set("failed_payments_distribution", "失败支付分布")
  dict->set("refunds_processed", "已处理退款")
  dict->set("refunds_success_rate", "退款成功率")
  dict->set("successful_refunds_distribution_by_connector", "按连接器的成功退款分布")
  dict->set("failed_refunds_distribution", "失败退款分布")
  dict->set("failed_refund_error_reasons", "退款失败错误原因")
  dict->set("refund_reasons", "退款原因")
  dict->set("authentication_analytics", "认证分析")
  dict->set("authentication_success_rate", "认证成功率")
  dict->set("authentication_summary", "认证摘要")
  dict->set("three_ds_exemption_analytics", "3DS豁免分析")

  // 支付生命周期状态
  dict->set("payments_initiated", "发起的支付")
  dict->set("success", "成功")
  dict->set("failed", "失败")
  dict->set("pending", "待处理")
  dict->set("cancelled", "已取消")
  dict->set("drop_offs", "中断")
  dict->set("refunds_issued", "已发起退款")
  dict->set("partial_refunded", "部分退款")
  dict->set("dispute_raised", "争议提起")
  dict->set("succeeded_on_first_attempt", "首次尝试成功")
  dict->set("succeeded_on_subsequent_attempts", "后续尝试成功")
  dict->set("smart_retried_failure", "智能重试失败")

  // 认证分析指标
  dict->set("payments_requiring_3ds_authentication", "需要3DS 2.0认证的支付")
  dict->set("authentication_initiated", "认证已发起")
  dict->set("authentication_attempted", "认证已尝试")
  dict->set("authentication_successful", "认证成功")
  dict->set("challenge_flow", "挑战流程")
  dict->set("frictionless_flow", "无摩擦流程")
  dict->set("challenge_flow_count", "挑战流程数量")
  dict->set("frictionless_flow_count", "无摩擦流程数量")
  dict->set("authentication_exemption_requested", "请求认证豁免")
  dict->set("authentication_exemption_approved", "批准认证豁免")

  // 表格列标题
  dict->set("amount", "金额")
  dict->set("count", "数量")
  dict->set("date", "日期")
  dict->set("connector", "连接器")
  dict->set("error_message", "错误消息")
  dict->set("success_rate", "成功率")
  dict->set("time_bucket", "时间段")

  // 描述文本
  dict->set("breakdown_of_threeds_journey", "ThreeDS 2.0流程分解")
  dict->set("authentication_funnel", "认证漏斗")

  // 下拉选项
  dict->set("by_amount", "按金额")
  dict->set("by_count", "按数量")

  // 失败原因
  dict->set("failure_reasons", "失败原因")

  // Transaction模块翻译
  dict->set("payment_operations", "支付操作")
  dict->set("search_payment_id", "搜索支付ID")
  dict->set("no_results_found", "未找到结果")
  dict->set("orders", "订单")
  dict->set("view_manage_disputes", "查看和管理所有争议")
  dict->set("search_dispute_id", "搜索争议ID")
  dict->set("search_payment_refund_id", "搜索支付ID或退款ID")

  // 添加缺失的翻译键
  dict->set("exemption_flowchart", "豁免流程图")

  // Settings模块翻译
  dict->set("language_settings", "语言设置")
  dict->set("current_language", "当前语言")
  dict->set("select_language", "选择您的首选语言")
  dict->set("profile_settings", "个人资料设置")
  dict->set("manage_profile_settings", "在此管理您的个人资料设置")
  dict->set("user_info", "用户信息")
  dict->set("name", "姓名")
  dict->set("email", "邮箱")
  dict->set("current", "当前：")
  dict->set("manage_payment_account_config", "管理您的支付账户配置")
  dict->set("configure_pmts", "配置支付方式")
  dict->set("processing", "处理中")
  dict->set("submit", "提交")
  dict->set("invite_users", "邀请用户")
  dict->set("users", "用户")

  // HSwitchSettings页面翻译
  dict->set("delete_sample_data", "删除示例数据")
  dict->set("delete_all_generated_sample_data", "删除所有生成的示例数据。")
  dict->set("delete_all", "全部删除")
  dict->set("add_details", "添加详情")
  dict->set("sample_data_deleted_successfully", "示例数据删除成功")
  dict->set("are_you_sure", "您确定吗？")
  dict->set("delete_sample_data_warning", "此操作无法撤销。这将永久删除所有示例支付和退款数据。要确认，请点击下面的全部删除按钮。")
  dict->set("cancel", "取消")

  // Auth模块翻译
  // CommonAuth.res
  dict->set("powered_by", "由 Pay_project inc. 提供支持")
  dict->set("welcome_back", "欢迎回到 Pay Project！")
  dict->set("welcome_to", "欢迎来到 Pay Project")
  dict->set("check_inbox", "请检查您的收件箱")
  dict->set("reset_password", "重置密码")
  dict->set("forgot_password", "忘记密码？")
  dict->set("resend_verify_email", "重新发送验证邮件")
  dict->set("new_to_hyperswitch", "新用户？")
  dict->set("already_using_hyperswitch", "已经在使用 Hyperswitch？")
  dict->set("sign_up", "注册")
  dict->set("sign_in", "登录")
  dict->set("forgot_password_description", "输入与您账户关联的邮箱地址，我们将向您发送重置密码的链接。")
  dict->set("magic_link_sent", "魔法链接已发送到")
  dict->set("reset_link_sent", "重置密码链接已发送到")
  dict->set("verify_link_sent", "验证邮件链接已发送到")

  // CommonAuthHooks.res
  dict->set("or_sign_in_using_password", "或使用密码登录")
  dict->set("or_sign_in_with_magic_link", "或使用魔法链接登录")
  dict->set("magic_link_description", "我们将向您发送一封魔法链接邮件，提供无密码体验，您随时可以选择稍后设置密码。")

  // CommonAuthError.res
  dict->set("invalid_link_expired", "链接无效或会话已过期")
  dict->set("link_expired_description", "看起来您尝试访问的链接已过期或不再有效。请重试。")
  dict->set("go_back_to_login", "返回登录")

  // AuthModuleHooks.res
  dict->set("sign_in_using_password", "使用密码登录")
  dict->set("sign_in_with_email", "使用邮箱登录")

  // CommonInputFields.res
  dict->set("email_label", "邮箱")
  dict->set("email_placeholder", "输入您的邮箱")
  dict->set("password_label", "密码")
  dict->set("password_placeholder", "输入您的密码")
  dict->set("old_password_label", "旧密码")
  dict->set("old_password_placeholder", "输入您的旧密码")
  dict->set("new_password_label", "新密码")
  dict->set("new_password_placeholder", "输入您的新密码")
  dict->set("confirm_password_label", "确认密码")
  dict->set("confirm_password_placeholder", "重新输入您的密码")
  dict->set("create_password_label", "密码")
  dict->set("create_password_placeholder", "输入您的密码")

  // CommonInviteScreen.res
  dict->set("welcome_to_pay_project", "嘿，欢迎来到 Pay Project！")
  dict->set("accept_pending_invitations", "请接受您的待处理邀请")
  dict->set("invited_to_dashboard", "您已被邀请加入 Hyperswitch 仪表板，邀请人：")
  dict->set("accepted", "已接受")
  dict->set("accept", "接受")
  dict->set("login_to_dashboard", "登录到仪表板")
  dict->set("login_different_account", "使用不同账户登录？")
  dict->set("click_to_logout", "点击此处退出登录。")

  // CommonAuthUtils.res
  dict->set("test_mode", "测试模式")
  dict->set("live_mode", "生产模式")

  // Home模块翻译
  // HomeUtils.res
  dict->set("integrate_processor", "集成处理器")
  dict->set("integrate_processor_desc", "通过连接20多个网关、支付方式和网络，为您提供先机。")
  dict->set("connect_processors", "连接处理器")
  dict->set("demo_checkout", "演示我们的结账体验")
  dict->set("demo_checkout_desc", "通过发起交易来测试您的支付连接器，并可视化用户结账体验")
  dict->set("test_payment_checkout", "进行测试支付 - 尝试我们的统一结账")
  dict->set("test_payment_desc", "通过发起交易来测试您的支付连接器，并可视化用户结账体验")
  dict->set("try_it_out", "试一试")
  dict->set("developer_resources", "开发者资源")
  dict->set("developer_resources_desc", "开发者需要的一些便利工具可以在这里找到。")
  dict->set("credentials_keys", "凭据和密钥")
  dict->set("credentials_keys_desc", "您开始集成的秘密凭据")
  dict->set("go_to_api_keys", "转到 API 密钥")
  dict->set("developer_docs", "开发者文档")
  dict->set("developer_docs_desc", "您需要了解的关于启动和运行 SDK 的一切都在这里。")
  dict->set("visit", "访问")
  dict->set("sign_up_access_features", "注册以访问所有功能！")
  dict->set("sign_up_description", "要释放潜力并体验全方位的功能，只需今天注册。加入我们的探索者社区，获得增强的可能性世界。")
  dict->set("sign_up_now", "立即注册")
  dict->set("merchant_id", "商户 ID")
  dict->set("publishable_key", "可发布密钥")
  dict->set("copied_to_clipboard", "已复制到剪贴板！")

  // Home.res
  dict->set("great_to_see_you", "很高兴见到您！")
  dict->set("welcome_message", "欢迎来到您的支付控制中心主页。它旨在为您的团队提供支付的360度全景视图。")
  dict->set("good_morning", "早上好")
  dict->set("good_afternoon", "下午好")
  dict->set("good_evening", "晚上好")
  dict->set("apply_production_access", "申请生产模式访问权限")
  dict->set("production_access_contact_admin", "如需开通生产模式，请联系系统管理员进行申请。管理员将协助您完成业务验证并开通相关权限。")
  dict->set("contact_info", "联系信息")
  dict->set("email_contact", "邮箱: <EMAIL>")
  dict->set("telegram_contact", "Telegram: @yeeu")
  dict->set("i_understand", "我知道了")
  dict->set("production_access_requested", "已申请生产模式")
  dict->set("get_production_access", "申请生产模式")
  dict->set("live_mode", "生产模式")
  dict->set("test_mode", "当前为测试模式")
  dict->set("credentials", "凭据")
  dict->set("payment_methods", "支付方式")
  dict->set("summary", "摘要")
  dict->set("preview", "预览")
  dict->set("automatic_flow", "自动流程")
  dict->set("profile", "配置文件")
  dict->set("processor", "处理器")
  dict->set("integration_status", "集成状态")
  dict->set("disabled", "已禁用")
  dict->set("merchant_connector_id", "商户连接器ID")
  dict->set("connector_label", "连接器标签")
  dict->set("update_payment_methods", "更新支付方式")

  // GlobalSearchBarHelper.res
  dict->set("no_results_for", "未找到结果：")
  dict->set("no_results", "无结果")
  dict->set("search", "搜索")
  dict->set("suggested_filters", "建议的筛选器")
  dict->set("view_results", "查看结果")

  // DefaultHome.res
  dict->set("hi_there", "您好 👋🏻")
  dict->set("explore_composable_services", "探索可组合服务")
  dict->set("orchestrator", "编排器")
  dict->set("orchestrator_desc", "统一多样化的抽象，连接支付处理器、支付处理器、欺诈管理解决方案、税务自动化解决方案、身份解决方案和报告系统")
  dict->set("vault", "保险库")
  dict->set("vault_desc", "独立的、符合PCI标准的保险库，安全地对客户的卡数据进行令牌化和存储——无需使用我们的支付解决方案。也支持在PSP和网络中进行卡令牌化。")

  // Analytics相关
  dict->set("performance_monitor", "性能监控")
  dict->set("authentication_analytics", "认证分析")

  // 通用组件翻译
  // NoDataFound.res
  dict->set("no_data_found", "未找到数据")
  dict->set("no_results_found", "未找到结果")
  dict->set("try_again", "重试")
  dict->set("expand_search_90_days", "将搜索扩展到前90天")
  dict->set("or_try_following", "或尝试以下方法：")
  dict->set("try_different_search", "尝试不同的搜索参数")
  dict->set("adjust_remove_filters", "调整或删除筛选器并再次搜索")
  dict->set("go_to_home", "返回首页")
  dict->set("error_404", "错误 404！")

  // ResendBtn.res
  dict->set("didnt_receive_mail", "没有收到邮件？")
  dict->set("send_again", "重新发送。")
  dict->set("seconds", "秒")

  // Paginator.res
  dict->set("of", "共")
  dict->set("results", "条结果")
  dict->set("showing", "显示")
  dict->set("to", "至")
  dict->set("page", "页")
  dict->set("per_page", "每页")
  dict->set("items", "项")

  // Loader.res
  dict->set("loading", "加载中...")

  // Toast相关
  dict->set("success", "成功")
  dict->set("error", "错误")
  dict->set("warning", "警告")
  dict->set("info", "信息")

  // 错误消息和状态文本
  // SearchInput.res
  dict->set("script_tags_not_allowed", "不允许使用脚本标签")
  dict->set("input_cannot_contain_script", "输入不能包含 <script>, </script> 标签")
  dict->set("ok", "确定")

  // FormRenderer.res
  dict->set("submit", "提交")
  dict->set("update", "更新")
  dict->set("cancel", "取消")
  dict->set("save", "保存")
  dict->set("error_occurred_on_submit", "提交时发生错误")

  // APIUtils.res - 错误消息
  dict->set("session_expired", "会话已过期")
  dict->set("access_forbidden", "访问被禁止")
  dict->set("no_access_control_text", "您没有访问此模块所需的权限。请联系您的管理员。")
  dict->set("close", "关闭")
  dict->set("error_occurred", "发生错误")
  dict->set("something_went_wrong", "出现了问题")
  dict->set("failed_to_fetch", "获取失败")

  // SDK 页面
  dict->set("sdk", "SDK")
  dict->set("integration_guide", "集成指南")
  dict->set("documentation", "文档")
  dict->set("code_examples", "代码示例")
  dict->set("test_payment", "测试支付")
  dict->set("demo_payment", "演示支付")
  dict->set("payment_form", "支付表单")
  dict->set("checkout_experience", "结账体验")

  // DefaultHome 页面
  dict->set("hi_there", "您好 👋🏻")
  dict->set("explore_composable_services", "探索可组合服务")

  // 更多通用翻译
  dict->set("seconds", "秒")
  dict->set("minutes", "分钟")
  dict->set("hours", "小时")
  dict->set("days", "天")
  dict->set("weeks", "周")
  dict->set("months", "月")
  dict->set("years", "年")

  // 更多操作按钮
  dict->set("create", "创建")
  dict->set("update", "更新")
  dict->set("remove", "移除")
  dict->set("duplicate", "复制")
  dict->set("archive", "归档")
  dict->set("restore", "恢复")
  dict->set("publish", "发布")
  dict->set("unpublish", "取消发布")

  // 更多状态
  dict->set("draft", "草稿")
  dict->set("published", "已发布")
  dict->set("archived", "已归档")
  dict->set("scheduled", "已计划")
  dict->set("expired", "已过期")

  // 双因素认证相关
  dict->set("two_factor_authentication", "双因素认证")
  dict->set("change_app_device", "更换应用/设备")
  dict->set("reset_totp_description", "如果您更换或丢失了设备，请重置TOTP以重新获得访问权限。")
  dict->set("regenerate_recovery_codes", "重新生成恢复代码")
  dict->set("regenerate_recovery_codes_description", "重新生成您的访问代码，以确保您的账户持续访问和安全。")

  // 支付方式配置页面
  dict->set("no_data_available", "暂无数据")
  dict->set("select_profile", "选择配置文件")
  dict->set("select_connector", "选择连接器")
  dict->set("select_payment_method", "选择支付方式")
  dict->set("select_payment_method_type", "选择支付方式类型")

  // 数据视图选择器
  dict->set("view_data_for", "查看数据：")
  dict->set("organization", "组织")
  dict->set("merchant", "商户")
  dict->set("profile", "配置文件")
  dict->set("all_default", "全部（默认）")

  dict
}
