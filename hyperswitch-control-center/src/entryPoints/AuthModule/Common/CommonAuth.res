open I18nUtils

module TermsAndCondition = {
  @react.component
  let make = () => {
    let (t, _, _, _) = useTranslation()

    <AddDataAttributes attributes=[("data-testid", "tc-text")]>
      <div id="tc-text" className="text-center text-sm text-gray-300">
        {t("powered_by")->React.string}
      </div>
    </AddDataAttributes>
  }
}

module PageFooterSection = {
  @react.component
  let make = () => {
    let (t, _, _, _) = useTranslation()

    <div
      className="justify-center text-base flex flex-col md:flex-row md:gap-3 items-center py-5 md:py-7">
      <AddDataAttributes attributes=[("data-testid", "footer")]>
        <div id="footer" className="flex items-center gap-2">
          {t("powered_by")->React.string}
        </div>
      </AddDataAttributes>
    </div>
  }
}

module Header = {
  @react.component
  let make = (~authType, ~setAuthType, ~email) => {
    open CommonAuthTypes
    let {globalUIConfig: {font: {textColor}}} = React.useContext(ThemeProvider.themeContext)
    let {isSignUpAllowed} = AuthModuleHooks.useAuthMethods()
    let form = ReactFinalForm.useForm()
    let {email: isMagicLinkEnabled} = HyperswitchAtom.featureFlagAtom->Recoil.useRecoilValueFromAtom
    let authId = HyperSwitchEntryUtils.getSessionData(~key="auth_id")
    let (t, _, _, _) = useTranslation()

    let headerStyle = switch authType {
    | MagicLinkEmailSent
    | ForgetPasswordEmailSent
    | ForgetPassword
    | ResendVerifyEmailSent => "flex flex-col justify-center items-center"
    | _ => "flex flex-col"
    }

    let cardHeaderText = switch authType {
    | LoginWithPassword | LoginWithEmail => t("welcome_back")
    | SignUP => t("welcome_to")
    | MagicLinkEmailSent
    | ForgetPasswordEmailSent
    | ResendVerifyEmailSent => t("check_inbox")
    | ResetPassword => t("reset_password")
    | ForgetPassword => t("forgot_password")
    | ResendVerifyEmail => t("resend_verify_email")
    | _ => ""
    }

    let getNoteUI = info => {
      <div className="flex-col items-center justify-center">
        <div> {info->React.string} </div>
        <div className="w-full flex justify-center text-center font-bold">
          {email->React.string}
        </div>
      </div>
    }

    let getHeaderLink = (~prefix, ~authType, ~path, ~sufix) => {
      <div className="flex text-sm items-center gap-2">
        <div className="text-grey-650"> {prefix->React.string} </div>
        <AddDataAttributes attributes=[("data-testid", "card-subtitle")]>
          <div
            onClick={_ => {
              form.resetFieldState("email")
              form.reset(JSON.Encode.object(Dict.make())->Nullable.make)
              setAuthType(_ => authType)
              GlobalVars.appendDashboardPath(~url=path)->RescriptReactRouter.push
            }}
            id="card-subtitle"
            className={`font-semibold ${textColor.primaryNormal} cursor-pointer`}>
            {sufix->React.string}
          </div>
        </AddDataAttributes>
      </div>
    }

    let showInfoIcon = switch authType {
    | MagicLinkEmailSent
    | ForgetPassword
    | ForgetPasswordEmailSent
    | ResendVerifyEmailSent
    | ResendVerifyEmail => true
    | _ => false
    }
    let (signUpAllowed, _) = isSignUpAllowed()
    <div className={`${headerStyle} gap-2 h-fit mb-7 w-96`}>
      <RenderIf condition={showInfoIcon}>
        <div className="flex justify-center my-5">
          {switch authType {
          | MagicLinkEmailSent | ForgetPasswordEmailSent | ResendVerifyEmailSent =>
            <img alt="mail" className="w-48" src={`/assets/mail.svg`} />
          | ForgetPassword =>
            <img alt="password" className="w-24" src={`/assets/key-password.svg`} />
          | _ => React.null
          }}
        </div>
      </RenderIf>
      <AddDataAttributes attributes=[("data-testid", "card-header")]>
        <h1 id="card-header" className="font-semibold text-xl md:text-2xl">
          {cardHeaderText->React.string}
        </h1>
      </AddDataAttributes>
      {switch authType {
      | LoginWithPassword | LoginWithEmail =>
        <RenderIf condition={signUpAllowed}>
          {getHeaderLink(
<<<<<<< HEAD
            ~prefix=t("new_to_hyperswitch"),
=======
            ~prefix="New to Pay_project?",
>>>>>>> 701b99226398f31d5d8939c2c631ad28fa0afc97
            ~authType=SignUP,
            ~path="/register",
            ~sufix=t("sign_up"),
          )}
        </RenderIf>

      | SignUP =>
        getHeaderLink(
<<<<<<< HEAD
          ~prefix=t("already_using_hyperswitch"),
=======
          ~prefix="Already using Pay_project?",
>>>>>>> 701b99226398f31d5d8939c2c631ad28fa0afc97
          ~authType=isMagicLinkEnabled ? LoginWithEmail : LoginWithPassword,
          ~path=`/login?auth_id=${authId}`,
          ~sufix=t("sign_in"),
        )
      | ForgetPassword =>
        <div className="text-md text-center text-grey-650 w-full max-w-md">
          {t("forgot_password_description")->React.string}
        </div>
      | MagicLinkEmailSent => t("magic_link_sent")->getNoteUI
      | ForgetPasswordEmailSent => t("reset_link_sent")->getNoteUI
      | ResendVerifyEmailSent => t("verify_link_sent")->getNoteUI
      | _ => React.null
      }}
    </div>
  }
}
