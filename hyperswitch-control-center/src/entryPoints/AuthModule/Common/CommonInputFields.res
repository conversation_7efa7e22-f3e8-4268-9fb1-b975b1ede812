open I18nUtils

// 创建字段生成函数
let makeEmailField = (t: string => string) => FormRenderer.makeFieldInfo(
  ~label=t("email_label"),
  ~name="email",
  ~placeholder=t("email_placeholder"),
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) =>
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder=t("email_placeholder"),
    ),
)

// 为了向后兼容，保留原有的字段定义，但使用默认语言
let getCurrentLanguage = () => {
  LocalStorage.getItem("hyperswitch_language")->LogicUtils.getValFromNullableValue("en")
}

let translate = (key: string, lang: string) => {
  switch (key, lang) {
  | ("email_label", "zh") => "邮箱"
  | ("email_placeholder", "zh") => "输入您的邮箱"
  | ("password_label", "zh") => "密码"
  | ("password_placeholder", "zh") => "输入您的密码"
  | ("old_password_label", "zh") => "旧密码"
  | ("old_password_placeholder", "zh") => "输入您的旧密码"
  | ("new_password_label", "zh") => "新密码"
  | ("new_password_placeholder", "zh") => "输入您的新密码"
  | ("confirm_password_label", "zh") => "确认密码"
  | ("confirm_password_placeholder", "zh") => "重新输入您的密码"
  | ("create_password_label", "zh") => "密码"
  | ("create_password_placeholder", "zh") => "输入您的密码"
  | ("email_label", _) => "Email"
  | ("email_placeholder", _) => "Enter your Email"
  | ("password_label", _) => "Password"
  | ("password_placeholder", _) => "Enter your Password"
  | ("old_password_label", _) => "Old Password"
  | ("old_password_placeholder", _) => "Enter your Old Password"
  | ("new_password_label", _) => "New Password"
  | ("new_password_placeholder", _) => "Enter your New Password"
  | ("confirm_password_label", _) => "Confirm Password"
  | ("confirm_password_placeholder", _) => "Re-enter your Password"
  | ("create_password_label", _) => "Password"
  | ("create_password_placeholder", _) => "Enter your Password"
  | _ => key
  }
}

let emailField = FormRenderer.makeFieldInfo(
  ~label=translate("email_label", getCurrentLanguage()),
  ~name="email",
  ~placeholder=translate("email_placeholder", getCurrentLanguage()),
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) =>
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder=translate("email_placeholder", getCurrentLanguage()),
    ),
)

let oldPasswordField = FormRenderer.makeFieldInfo(
  ~label=translate("old_password_label", getCurrentLanguage()),
  ~name="old_password",
  ~placeholder=translate("old_password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.passwordMatchField(
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)

let newPasswordField = FormRenderer.makeFieldInfo(
  ~label=translate("new_password_label", getCurrentLanguage()),
  ~name="new_password",
  ~placeholder=translate("new_password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.passwordMatchField(
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)

let confirmNewPasswordField = FormRenderer.makeFieldInfo(
  ~label=translate("confirm_password_label", getCurrentLanguage()),
  ~name="confirm_password",
  ~placeholder=translate("confirm_password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.textInput(
    ~type_="password",
    ~autoComplete="off",
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)

let createPasswordField = FormRenderer.makeFieldInfo(
  ~label=translate("create_password_label", getCurrentLanguage()),
  ~name="create_password",
  ~placeholder=translate("create_password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.passwordMatchField(
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)

let confirmPasswordField = FormRenderer.makeFieldInfo(
  ~label=translate("confirm_password_label", getCurrentLanguage()),
  ~name="comfirm_password",
  ~placeholder=translate("confirm_password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.textInput(
    ~type_="password",
    ~autoComplete="off",
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)

let passwordField = FormRenderer.makeFieldInfo(
  ~label=translate("password_label", getCurrentLanguage()),
  ~name="password",
  ~placeholder=translate("password_placeholder", getCurrentLanguage()),
  ~type_="password",
  ~customInput=InputFields.textInput(
    ~type_="password",
    ~autoComplete="off",
    ~leftIcon={
      <Icon name="password-lock" size=13 />
    },
  ),
  ~isRequired=false,
)
