open I18nUtils

@react.component
let make = (~merchantData, ~acceptInviteOnClick, ~onClickLoginToDashboard) => {
  open HSwitchUtils
  open LogicUtils

  let textHeadingClass = getTextClass((H2, Optional))
  let textSubHeadingClass = getTextClass((P1, Regular))
  let handleLogout = APIUtils.useHandleLogout()
  let (t, _, _, _) = useTranslation()

  let isAtleastOneAccept = React.useMemo(() => {
    merchantData
    ->Array.find(ele => ele->getDictFromJsonObject->getBool("is_active", false))
    ->Option.getOr(JSON.Encode.null)
    ->getDictFromJsonObject
    ->getBool("is_active", false)
  }, [merchantData])

  <BackgroundImageWrapper>
    <div className="h-full w-full flex flex-col gap-4 items-center justify-center p-6">
      <div className="bg-white h-35-rem w-200 rounded-2xl">
        <div className="p-6 border-b-2">
          <img alt="logo-with-text" src={`assets/Dark/payprojectLogoIconWithText.svg`} />
        </div>
        <div className="p-6 flex flex-col gap-2">
          <p className={`${textHeadingClass} text-grey-900`}>
            {t("welcome_to_pay_project")->React.string}
          </p>
          <p className=textSubHeadingClass>
            {t("accept_pending_invitations")->React.string}
          </p>
        </div>
        <div className="h-[50%] overflow-auto show-scrollbar">
          {merchantData
          ->Array.mapWithIndex((ele, index) => {
            let merchantId = ele->getDictFromJsonObject->getString("merchant_id", "")
            let merchantName = ele->getDictFromJsonObject->getString("merchant_name", "")
            let isActive = ele->getDictFromJsonObject->getBool("is_active", false)

            <div
              key={index->string_of_int}
              className="border-1 m-6 p-5 flex items-center justify-between rounded-xl">
              <div className="flex items-center gap-5">
                <Icon size=40 name="group-users" />
                <div>
                  {t("invited_to_dashboard")->React.string}
                  <span className="font-bold">
                    {{merchantName->String.length > 0 ? merchantName : merchantId}->React.string}
                  </span>
                </div>
              </div>
              <RenderIf condition={!isActive}>
                <Button
                  text={t("accept")}
                  buttonType={PrimaryOutline}
                  customButtonStyle="!p-2"
                  onClick={_ => acceptInviteOnClick(index)}
                />
              </RenderIf>
              <RenderIf condition={isActive}>
                <div className="flex items-center gap-1 text-green-accepted_green_800">
                  <Icon name="green-tick-without-background" />
                  {t("accepted")->React.string}
                </div>
              </RenderIf>
            </div>
          })
          ->React.array}
        </div>
        <div className="w-full flex items-center justify-center mt-4">
          <Button
            text={t("login_to_dashboard")}
            buttonType={Primary}
            onClick={_ => onClickLoginToDashboard()->ignore}
            buttonState={isAtleastOneAccept ? Normal : Disabled}
          />
        </div>
      </div>
      <div className="text-grey-200 flex gap-2">
        {t("login_different_account")->React.string}
        <p
          className="underline cursor-pointer underline-offset-2 hover:text-blue-700"
          onClick={_ => handleLogout()->ignore}>
          {t("click_to_logout")->React.string}
        </p>
      </div>
    </div>
  </BackgroundImageWrapper>
}
