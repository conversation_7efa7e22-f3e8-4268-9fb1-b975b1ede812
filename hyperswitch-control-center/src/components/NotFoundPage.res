open I18nUtils

@react.component
let make = (~message=?) => {
  let {setDashboardPageState} = React.useContext(GlobalProvider.defaultContext)
  let (t, _, _, _) = useTranslation()
  let defaultMessage = t("error_404")
  let finalMessage = message->Option.getOr(defaultMessage)
  let goToHomeText = t("go_to_home")

  <NoDataFound message=finalMessage renderType={NotFound}>
    <Button
      text=goToHomeText
      buttonType=Primary
      buttonSize=Small
      onClick={_ => {
        setDashboardPageState(_ => #HOME)
        RescriptReactRouter.replace(GlobalVars.appendDashboardPath(~url="/home"))
      }}
      customButtonStyle="mt-4"
    />
  </NoDataFound>
}
