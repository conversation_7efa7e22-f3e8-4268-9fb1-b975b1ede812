open I18nUtils

@react.component
let make = (
  ~loadingText=?,
  ~children=React.null,
  ~slow=false,
  ~customSpinnerIconColor: string="",
  ~loadingTextColor="",
) => {
  let (t, _, _, _) = useTranslation()
  let defaultLoadingText = t("loading")
  let _finalLoadingText = loadingText->Option.getOr(defaultLoadingText)
  let animationType = if slow {
    "animate-spin-slow"
  } else {
    "animate-spin"
  }

  let size = if slow {
    60
  } else {
    20
  }

  let loader =
    <div className={`flex flex-col py-16 text-center items-center ${loadingTextColor}`}>
      <div className={`${animationType} mb-10`}>
        <Icon name="spinner" size customIconColor=customSpinnerIconColor />
      </div>
      {children}
    </div>

  <div className="flex flex-col">
    <div className="w-full flex justify-center py-10">
      <div className="w-20 h-16">
        <React.Suspense fallback={loader}>
          <ErrorBoundary> {loader} </ErrorBoundary>
        </React.Suspense>
      </div>
    </div>
  </div>
}
