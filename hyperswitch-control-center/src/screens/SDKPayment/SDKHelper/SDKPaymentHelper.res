open SDKPaymentUtils

let enterAmountField = (initialValues: SDKPaymentTypes.paymentType) => {
  FormRenderer.makeFieldInfo(~label=I18n.t("enter_amount"), ~name="amount", ~customInput=(
    ~input,
    ~placeholder as _,
  ) =>
    InputFields.numericTextInput(
      ~isDisabled=false,
      ~customStyle="w-full border-nd_gray-200 rounded-lg",
      ~precision=2,
    )(
      ~input={
        ...input,
        value: (initialValues.amount /. 100.00)->Float.toString->JSON.Encode.string,
        onChange: {
          ev => {
            let eventValueToFloat =
              ev->Identity.formReactEventToString->LogicUtils.getFloatFromString(0.00)
            let valInCents =
              (eventValueToFloat *. 100.00)->Float.toString->Identity.stringToFormReactEvent
            input.onChange(valInCents)
          }
        },
      },
      ~placeholder=I18n.t("enter_amount"),
    )
  )
}

let enterPrimaryColorValue = defaultValue =>
  FormRenderer.makeFieldInfo(
    ~label=I18n.t("color_picker_input"),
    ~name="primary_color",
    ~placeholder="",
    ~isRequired=false,
    ~customInput=InputFields.colorPickerInput(~defaultValue),
  )

let enterCustomerId = (~isGuestMode, ~setIsGuestMode) => {
  FormRenderer.makeFieldInfo(
    ~label=I18n.t("customer_id"),
    ~name="customer_id",
    ~customInput=(~input, ~placeholder as _) =>
      InputFields.textInput(
        ~isDisabled=isGuestMode,
        ~customStyle="w-full border-nd_gray-200 rounded-lg",
      )(
        ~input={
          ...input,
          onChange: ev => input.onChange(ev),
        },
        ~placeholder=I18n.t("enter_customer_id"),
      ),
    ~labelRightComponent=<div className="flex items-center gap-2">
      <span className="text-sm text-gray-600"> {I18n.t("guest_mode")->React.string} </span>
      <input
        type_="checkbox"
        className="rounded border-gray-300"
        onChange={_ => setIsGuestMode(prev => !prev)}
      />
    </div>,
  )
}

let selectEnterIntegrationType = FormRenderer.makeFieldInfo(
  ~label=I18n.t("integration_type"),
  ~name="integration_type",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForIntegrationType,
    ~buttonText=I18n.t("select_integration_type"),
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectCurrencyField = FormRenderer.makeFieldInfo(
  ~label=I18n.t("currency"),
  ~name="country_currency",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForCountryCurrency,
    ~buttonText=I18n.t("select_currency"),
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectThemeField = FormRenderer.makeFieldInfo(
  ~label="Theme",
  ~name="theme",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForTheme,
    ~buttonText="Select Theme",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectLocaleField = FormRenderer.makeFieldInfo(
  ~label="Locale",
  ~name="locale",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForLocales,
    ~buttonText="Select Locale",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectLayoutField = FormRenderer.makeFieldInfo(
  ~label="Layout",
  ~name="layout",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForLayouts,
    ~buttonText="Select Layout",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectLabelsField = FormRenderer.makeFieldInfo(
  ~label="Labels",
  ~name="labels",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForLabels,
    ~buttonText="Select Label",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectCaptureMethodField = FormRenderer.makeFieldInfo(
  ~label="Capture Method",
  ~name="capture_method",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForCaptureMethods,
    ~buttonText="Select Capture Method",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectSetupFutureUsageField = FormRenderer.makeFieldInfo(
  ~label="Setup Future Usage",
  ~name="setup_future_usage",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForSetupFutureUsage,
    ~buttonText="Select Setup Future Usage",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectAuthenticationField = FormRenderer.makeFieldInfo(
  ~label="Authentication Type",
  ~name="authentication_type",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForAuthenticationType,
    ~buttonText="Select Authentication Type",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let selectExternal3DSAuthentication = FormRenderer.makeFieldInfo(
  ~label="Request External 3DS Authentication",
  ~name="request_external_three_ds_authentication",
  ~placeholder="",
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.selectInput(
      ~customStyle="max-h-48",
      ~options={dropDownOptionsForRequestThreeDSAuthentication},
      ~buttonText="Select Value",
    )(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="",
    )
  },
)

let selectShowSavedCardField = FormRenderer.makeFieldInfo(
  ~label=I18n.t("show_saved_card"),
  ~name="show_saved_card",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=dropDownOptionsForShowSavedCard,
    ~buttonText=I18n.t("select_option"),
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let external3DSAuthToggle = FormRenderer.makeFieldInfo(
  ~name="request_external_three_ds_authentication",
  ~label="Request External 3DS Authentication",
  ~customInput=InputFields.boolInput(~isDisabled=false, ~boolCustomClass="rounded-lg"),
)

let enterEmailField = FormRenderer.makeFieldInfo(
  ~label="Email",
  ~name="email",
  ~placeholder="Enter your Email",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Email",
    )
  },
)

let enterBillingFirstName = FormRenderer.makeFieldInfo(
  ~label="First Name",
  ~name="billing.address.first_name",
  ~placeholder="Enter your First Name",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Name",
    )
  },
)

let enterBillingLastName = FormRenderer.makeFieldInfo(
  ~label="Last Name",
  ~name="billing.address.last_name",
  ~placeholder="Enter your Last Name",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Last Name",
    )
  },
)

let enterBillingAddressLine1 = FormRenderer.makeFieldInfo(
  ~label="Address Line 1",
  ~name="billing.address.line1",
  ~placeholder="Enter your Address Line 1",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Address Line 1",
    )
  },
)

let enterBillingAddressLine2 = FormRenderer.makeFieldInfo(
  ~label="Address Line 2",
  ~name="billing.address.line2",
  ~placeholder="Enter your Address Line 2",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Address Line 2",
    )
  },
)

let enterBillingCity = FormRenderer.makeFieldInfo(
  ~label="City",
  ~name="billing.address.city",
  ~placeholder="Enter your City",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your City",
    )
  },
)

let enterBillingState = FormRenderer.makeFieldInfo(
  ~label="City",
  ~name="billing.address.state",
  ~placeholder="Enter your State",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your State",
    )
  },
)

let enterBillingCountry = FormRenderer.makeFieldInfo(
  ~label="Country",
  ~name="billing.address.country",
  ~placeholder="Enter your Country",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your country",
    )
  },
)

let enterBillingZipcode = FormRenderer.makeFieldInfo(
  ~label="Zipcode",
  ~name="billing.address.zip",
  ~placeholder="Enter your zipcode",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your zipcode",
    )
  },
)

let selectCountryPhoneCodeFieldForBilling = FormRenderer.makeFieldInfo(
  ~label="Phone Number",
  ~name="billing.phone.country_code",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=Country.country->Array.map((item): SelectBox.dropdownOption => {
      label: `${item.flag} ${item.phoneCode}`,
      value: item.phoneCode,
    }),
    ~buttonText="Select Country Code",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let enterBillingPhoneNumber = {
  FormRenderer.makeFieldInfo(
    ~label="",
    ~name="billing.phone.number",
    ~customInput=InputFields.numericTextInput(
      ~isDisabled=false,
      ~customStyle="w-full border-nd_gray-200 rounded-lg mt-[20px]",
    ),
  )
}

let enterShippingFirstName = FormRenderer.makeFieldInfo(
  ~label="First Name",
  ~name="shipping.address.first_name",
  ~placeholder="Enter your First Name",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Name",
    )
  },
)

let enterShippingLastName = FormRenderer.makeFieldInfo(
  ~label="Last Name",
  ~name="shipping.address.last_name",
  ~placeholder="Enter your Last Name",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Last Name",
    )
  },
)

let enterShippingAddressLine1 = FormRenderer.makeFieldInfo(
  ~label="Address Line 1",
  ~name="shipping.address.line1",
  ~placeholder="Enter your Address Line 1",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Address Line 1",
    )
  },
)

let enterShippingAddressLine2 = FormRenderer.makeFieldInfo(
  ~label="Address Line 2",
  ~name="shipping.address.line2",
  ~placeholder="Enter your Address Line 2",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your Address Line 2",
    )
  },
)

let enterShippingCity = FormRenderer.makeFieldInfo(
  ~label="City",
  ~name="shipping.address.city",
  ~placeholder="Enter your City",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your City",
    )
  },
)

let enterShippingState = FormRenderer.makeFieldInfo(
  ~label="City",
  ~name="shipping.address.state",
  ~placeholder="Enter your State",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your State",
    )
  },
)

let enterShippingCountry = FormRenderer.makeFieldInfo(
  ~label="Country",
  ~name="shipping.address.country",
  ~placeholder="Enter your Country",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your country",
    )
  },
)

let enterShippingZipcode = FormRenderer.makeFieldInfo(
  ~label="Zipcode",
  ~name="shipping.address.zip",
  ~placeholder="Enter your zipcode",
  ~isRequired=false,
  ~customInput=(~input, ~placeholder as _) => {
    InputFields.textInput(~autoComplete="off")(
      ~input={
        ...input,
        onChange: event =>
          ReactEvent.Form.target(event)["value"]
          ->String.trim
          ->Identity.stringToFormReactEvent
          ->input.onChange,
      },
      ~placeholder="Enter your zipcode",
    )
  },
)

let selectCountryPhoneCodeFieldForShipping = FormRenderer.makeFieldInfo(
  ~label="Phone Number",
  ~name="shipping.phone.country_code",
  ~placeholder="",
  ~customInput=InputFields.selectInput(
    ~options=Country.country->Array.map((item): SelectBox.dropdownOption => {
      label: `${item.flag} ${item.phoneCode}`,
      value: item.phoneCode,
    }),
    ~buttonText="Select Country Code",
    ~deselectDisable=true,
    ~fullLength=true,
    ~textStyle="!font-normal",
  ),
)

let enterShippingPhoneNumber = {
  FormRenderer.makeFieldInfo(
    ~label="",
    ~name="shipping.phone.number",
    ~customInput=InputFields.numericTextInput(
      ~isDisabled=false,
      ~customStyle="w-full border-nd_gray-200 rounded-lg mt-[20px]",
    ),
  )
}
