open HSwitchSettingTypes
open I18nUtils

let getDeleteSampleData = (t: string => string) => {
  heading: t("delete_sample_data"),
  subHeading: t("delete_all_generated_sample_data"),
  buttonText: t("delete_all"),
  isApiCall: true,
  cardName: #DELETE_SAMPLE_DATA,
}

module TileComponent = {
  @react.component
  let make = (
    ~heading,
    ~subHeading,
    ~redirect=?,
    ~isComingSoon=false,
    ~buttonText,
    ~redirectUrl,
    ~isApiCall=true,
    ~cardName,
  ) => {
    open APIUtils
    open GlobalVars
    let getURL = useGetURL()
    let showPopUp = PopUpState.useShowPopUp()
    let showToast = ToastState.useShowToast()
    let updateDetails = useUpdateMethod()
    let {userHasAccess} = GroupACLHooks.useUserGroupACLHook()
    let (t, _, _, _) = useTranslation()

    let deleteSampleData = async () => {
      try {
        let generateSampleDataUrl = getURL(~entityName=V1(GENERATE_SAMPLE_DATA), ~methodType=Delete)
        let _ = await updateDetails(generateSampleDataUrl, Dict.make()->JSON.Encode.object, Delete)
        showToast(~message=t("sample_data_deleted_successfully"), ~toastType=ToastSuccess)
      } catch {
      | _ => ()
      }
    }
    let openPopUpModal = _ =>
      showPopUp({
        popUpType: (Warning, WithIcon),
        heading: t("are_you_sure"),
        description: {
          t("delete_sample_data_warning")->React.string
        },
        handleConfirm: {
          text: t("delete_all"),
          onClick: {
            _ => {
              deleteSampleData()->ignore
            }
          },
        },
        handleCancel: {
          text: t("cancel"),
          onClick: {
            _ => ()
          },
        },
      })

    let onClickHandler = _ => {
      if isApiCall {
        switch cardName {
        | #DELETE_SAMPLE_DATA => openPopUpModal()
        | _ => ()
        }
      } else {
        switch redirectUrl {
        | Some(url) => RescriptReactRouter.push(appendDashboardPath(~url=`/${url}`))
        | None =>
          switch redirect {
          | Some(redirect) =>
            RescriptReactRouter.push(appendDashboardPath(~url=`/settings?type=${redirect}`))
          | None => RescriptReactRouter.push(appendDashboardPath(~url="/settings"))
          }
        }
      }
    }
    let accessBasedOnCardName = switch cardName {
    | #DELETE_SAMPLE_DATA => userHasAccess(~groupAccess=OperationsManage)
    | _ => Access
    }

    <div
      className="flex flex-col bg-white pt-6 pl-6 pr-8 pb-8 justify-between gap-10 border border-jp-gray-border_gray rounded ">
      <div>
        <div className="flex justify-between">
          <p className="text-fs-16 font-semibold m-2"> {heading->React.string} </p>
          {isComingSoon ? <Icon className="w-36" name="comingSoon" size=25 /> : React.null}
        </div>
        <p className="text-fs-14 font-medium m-2 text-black opacity-50">
          {subHeading->React.string}
        </p>
      </div>
      <ACLButton
        authorization=accessBasedOnCardName
        text=buttonText
        buttonType=Secondary
        customButtonStyle="w-2/3"
        buttonSize={Small}
        onClick={onClickHandler}
        buttonState={isComingSoon ? Disabled : Normal}
      />
    </div>
  }
}
module PersonalSettings = {
  @react.component
  let make = () => {
    let featureFlagDetails = HyperswitchAtom.featureFlagAtom->Recoil.useRecoilValueFromAtom
    let (t, _, _, _) = useTranslation()
    let personalSettings = if featureFlagDetails.sampleData {
      [getDeleteSampleData(t)]
    } else {
      []
    }

    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-8">
      {personalSettings
      ->Array.mapWithIndex((sections, index) =>
        <TileComponent
          key={Int.toString(index)}
          heading={sections.heading}
          subHeading={sections.subHeading}
          redirect={sections.redirect->Option.getOr("")}
          isComingSoon={sections.isComingSoon->Option.getOr(false)}
          buttonText={sections.buttonText->Option.getOr(t("add_details"))}
          redirectUrl={sections.redirectUrl}
          isApiCall={sections.isApiCall->Option.getOr(false)}
          cardName={sections.cardName}
        />
      )
      ->React.array}
    </div>
  }
}

@react.component
let make = () => {
  let (t, _, _, _) = I18nUtils.useTranslation()

  <>
    <PageUtils.PageHeading
      title={t("account_settings")} subTitle={t("manage_payment_account_config")}
    />
    <PersonalSettings />
  </>
}
