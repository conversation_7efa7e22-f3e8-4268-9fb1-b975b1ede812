open LogicUtils

type rolesTableTypes = {
  role_name: string,
  role_scope: string,
  groups: array<JSON.t>,
  entity_name: string,
}

type rolesColTypes =
  | RoleName
  | RoleScope
  | EntityType
  | RoleGroupAccess

let defaultColumnsForRoles = [RoleName, EntityType, RoleGroupAccess]

let itemToObjMapperForRoles = dict => {
  {
    role_name: getString(dict, "role_name", ""),
    role_scope: getString(dict, "role_scope", ""),
    groups: getArrayFromDict(dict, "groups", []),
    entity_name: getString(dict, "entity_type", ""),
  }
}

let getHeadingForRoles = (colType: rolesColTypes) => {
  let (t, _, _, _) = I18nUtils.useTranslation()
  switch colType {
  | RoleName => Table.makeHeaderInfo(~key="role_name", ~title=t("role_name"), ~showSort=true)
  | RoleScope => Table.makeHeaderInfo(~key="role_scope", ~title=t("role_scope"))
  | EntityType => Table.makeHeaderInfo(~key="entity_type", ~title=t("entity_type"))
  | RoleGroupAccess => Table.makeHeaderInfo(~key="groups", ~title=t("module_permissions"))
  }
}

let getCellForRoles = (data: rolesTableTypes, colType: rolesColTypes): Table.cell => {
  switch colType {
  | RoleName => Text(data.role_name->LogicUtils.snakeToTitle)
  | RoleScope => Text(data.role_scope->LogicUtils.capitalizeString)
  | EntityType => Text(data.entity_name->LogicUtils.capitalizeString)
  | RoleGroupAccess =>
    let (t, _, _, _) = I18nUtils.useTranslation()
    Table.CustomCell(
      <div>
        {data.groups
        ->LogicUtils.getStrArrayFromJsonArray
        ->Array.map(item => {
          let translatedItem = switch item {
          | "users_view" => t("users_view")
          | "users_manage" => t("users_manage")
          | "account_view" => t("account_view")
          | "account_manage" => t("account_manage")
          | "connectors_view" => t("connectors_view")
          | "connectors_manage" => t("connectors_manage")
          | "workflows_view" => t("workflows_view")
          | "workflows_manage" => t("workflows_manage")
          | "operations_view" => t("operations_view")
          | "operations_manage" => t("operations_manage")
          | "analytics_view" => t("analytics_view")
          | "merchant_details_view" => t("merchant_details_view")
          | "merchant_details_manage" => t("merchant_details_manage")
          | "organization_manage" => t("organization_manage")
          | _ => item->LogicUtils.snakeToTitle
          }
          translatedItem
        })
        ->Array.joinWith(", ")
        ->React.string}
      </div>,
      "",
    )
  }
}

let getrolesData: JSON.t => array<rolesTableTypes> = json => {
  getArrayDataFromJson(json, itemToObjMapperForRoles)
}

let rolesEntity = EntityType.makeEntity(
  ~uri="",
  ~getObjects=getrolesData,
  ~defaultColumns=defaultColumnsForRoles,
  ~allColumns=defaultColumnsForRoles,
  ~getHeading=getHeadingForRoles,
  ~getCell=getCellForRoles,
  ~dataKey="",
)
