open CardUtils
open PageUtils
open HSwitchUtils
open I18nUtils

let headingStyle = `${getTextClass((P2, Medium))} text-grey-700 uppercase opacity-50 px-2`
let paragraphTextVariant = `${getTextClass((P2, Medium))} text-grey-700 opacity-50`
let subtextStyle = `${getTextClass((P1, Regular))} text-grey-700 opacity-50`
let cardHeaderText = getTextClass((H3, Leading_2))
let hoverStyle = "cursor-pointer group-hover:shadow hover:shadow-homePageBoxShadow group"
let boxCssHover = (~ishoverStyleRequired) =>
  `flex flex-col  bg-white border rounded-md pt-10 pl-10 gap-2 h-12.5-rem ${ishoverStyleRequired
      ? hoverStyle
      : ""}`
let boxCss = "flex flex-col bg-white border rounded-md gap-4 p-7"
let imageTransitionCss = "opacity-50 group-hover:opacity-100 transition ease-in-out duration-300"
let cardHeaderTextStyle = `${cardHeaderText} text-grey-700`

type resourcesTypes = {
  icon: string,
  headerText: string,
  subText: string,
  redirectLink: string,
  id: string,
  access: CommonAuthTypes.authorization,
}

let isDefaultBusinessProfile = details => details->Array.length === 1

module MerchantAuthInfo = {
  @react.component
  let make = () => {
    let showToast = ToastState.useShowToast()
    let (t, _, _, _) = useTranslation()
    let handleCopy = copyValue => {
      Clipboard.writeText(copyValue)
      showToast(~message=t("copied_to_clipboard"), ~toastType=ToastSuccess)
    }
    let merchantDetailsValue = MerchantDetailsHook.useMerchantDetailsValue()

    <div className="flex flex-col gap-2">
      <div className="font-semibold text-dark_black md:text-base text-sm">
        {t("merchant_id")->React.string}
      </div>
      <div className="flex items-center gap-2">
        <div className="font-medium text-dark_black opacity-40" style={overflowWrap: "anywhere"}>
          {merchantDetailsValue.merchant_id->React.string}
        </div>
        <div onClick={_ => handleCopy(merchantDetailsValue.merchant_id)}>
          <Icon name="nd-copy" size=16 />
        </div>
      </div>
      <div>
        <div className="font-semibold text-dark_black md:text-base text-sm">
          {t("publishable_key")->React.string}
        </div>
        <div className="flex items-center gap-2">
          <div className="font-medium text-dark_black opacity-40" style={overflowWrap: "anywhere"}>
            {merchantDetailsValue.publishable_key->React.string}
          </div>
          <div onClick={_ => handleCopy(merchantDetailsValue.publishable_key)}>
            <Icon name="nd-copy" size=16 />
          </div>
        </div>
      </div>
    </div>
  }
}

module CheckoutCard = {
  @react.component
  let make = () => {
    let showPopUp = PopUpState.useShowPopUp()
    let mixpanelEvent = MixpanelHook.useSendEvent()
    let handleLogout = APIUtils.useHandleLogout()
    let {userHasAccess, hasAllGroupsAccess} = GroupACLHooks.useUserGroupACLHook()
    let isPlayground = HSLocalStorage.getIsPlaygroundFromLocalStorage()
    let (t, _, _, _) = useTranslation()

    let connectorList = ConnectorInterface.useConnectorArrayMapper(
      ~interface=ConnectorInterface.connectorInterfaceV1,
    )

    let isConfigureConnector = connectorList->Array.length > 0

    let handleOnClick = _ => {
      if isPlayground {
        showPopUp({
          popUpType: (Warning, WithIcon),
          heading: t("sign_up_access_features"),
          description: {
            t("sign_up_description")->React.string
          },
          handleConfirm: {
            text: t("sign_up_now"),
            onClick: {
              _ => handleLogout()->ignore
            },
          },
        })
      } else {
        mixpanelEvent(~eventName=`try_test_payment`)
        RescriptReactRouter.push(GlobalVars.appendDashboardPath(~url="/sdk"))
      }
    }
    let (title, description) = isConfigureConnector
      ? (
          t("test_payment_checkout"),
          t("test_payment_desc"),
        )
      : (
          t("demo_checkout"),
          t("demo_checkout_desc"),
        )

    <CardLayout width="" customStyle="flex-1 rounded-xl p-6 gap-4  ">
      <div className="flex flex-col gap-4  ">
        <img alt="sdk" src="/assets/SDK.png" />
        <CardHeader heading=title subHeading=description />
        <ACLButton
          text={t("try_it_out")}
          authorization={hasAllGroupsAccess([
            userHasAccess(~groupAccess=OperationsManage),
            userHasAccess(~groupAccess=ConnectorsManage),
          ])}
          buttonType={Primary}
          buttonSize={Medium}
          onClick={handleOnClick}
        />
      </div>
    </CardLayout>
  }
}

module ControlCenter = {
  @react.component
  let make = () => {
    let {isLiveMode} = HyperswitchAtom.featureFlagAtom->Recoil.useRecoilValueFromAtom
    let (t, _, _, _) = useTranslation()
    let liveModeStyles = isLiveMode ? "w-1/2 " : "flex flex-col md:flex-row gap-5 "
    <div className=liveModeStyles>
      <CardLayout width="" customStyle="flex-1 rounded-xl p-6 gap-4">
        <div className="flex flex-col gap-4 max-w-[38rem]">
          <img alt="sdk" src="/assets/IntegrateProcessorsOver.png" />
          <CardHeader
            heading={t("integrate_processor")}
            subHeading={t("integrate_processor_desc")}
          />
          <Button
            text={t("connect_processors")}
            buttonType={Primary}
            buttonSize={Medium}
            onClick={_ => {
              RescriptReactRouter.push(GlobalVars.appendDashboardPath(~url="/connectors"))
            }}
          />
        </div>
      </CardLayout>
      <RenderIf condition={!isLiveMode}>
        <CheckoutCard />
      </RenderIf>
    </div>
  }
}
module DevResources = {
  @react.component
  let make = () => {
    let {checkUserEntity} = React.useContext(UserInfoProvider.defaultContext)
    let mixpanelEvent = MixpanelHook.useSendEvent()
    let (t, _, _, _) = useTranslation()
    <div className="flex flex-col mb-5 gap-6 ">
      <PageHeading
        title={t("developer_resources")}
        subTitle={t("developer_resources_desc")}
        customTitleStyle="!text-fs-20 !font-semibold"
        customSubTitleStyle="!text-fs-16 !text-nd_gray-400 !opacity-100 font-medium !mt-1"
      />
      <div className="flex flex-col md:flex-row  gap-5 ">
        <RenderIf condition={!checkUserEntity([#Profile])}>
          <CardLayout width="" customStyle={"flex-1 rounded-xl p-6"}>
            <div className="flex flex-col gap-7 ">
              <CardHeader
                heading={t("credentials_keys")}
                subHeading={t("credentials_keys_desc")}
                customHeadingStyle="!text-fs-18 !font-semibold"
                customSubHeadingStyle="!text-fs-14 !text-nd_gray-400 !opacity-100 !-mt-0.5"
              />
              <MerchantAuthInfo />
              <Button
                text={t("go_to_api_keys")}
                buttonType={Secondary}
                buttonSize={Medium}
                onClick={_ => {
                  mixpanelEvent(~eventName="redirect_to_api_keys")
                  RescriptReactRouter.push(
                    GlobalVars.appendDashboardPath(~url="/developer-api-keys"),
                  )
                }}
              />
            </div>
          </CardLayout>
        </RenderIf>
        <CardLayout width="" customStyle="flex-1 rounded-xl p-6">
          <div className="flex flex-col gap-4 ">
            <CardHeader
              heading={t("developer_docs")}
              subHeading={t("developer_docs_desc")}
              customHeadingStyle="!text-fs-18 !font-semibold"
              customSubHeadingStyle="!text-fs-14 !text-nd_gray-400 !opacity-100 !-mt-0.5"
            />
            <img alt="devdocs" src="/assets/DevDocs.png" />
            <Button
              text={t("visit")}
              buttonType={Secondary}
              buttonSize={Medium}
              onClick={_ => {
                mixpanelEvent(~eventName=`dev_docs`)
                "http://************:3000/"->Window._open
              }}
            />
          </div>
        </CardLayout>
      </div>
    </div>
  }
}

let getGreeting = () => {
  let (t, _, _, _) = useTranslation()
  let dateTime = Date.now()
  let hours = Js.Date.fromFloat(dateTime)->Js.Date.getHours->Int.fromFloat

  switch hours {
  | h if h < 12 => t("good_morning")
  | h if h < 18 => t("good_afternoon")
  | _ => t("good_evening")
  }
}

let homepageStepperItems = ["Configure control center", "Integrate into your app", "Go Live"]

let responseDataMapper = (res: JSON.t, mapper: (Dict.t<JSON.t>, string) => JSON.t) => {
  open LogicUtils
  let arrayFromJson = res->getArrayFromJson([])
  let resDict = Dict.make()

  arrayFromJson->Array.forEach(value => {
    let value1 = value->getDictFromJsonObject
    let key = value1->Dict.keysToArray->Array.get(0)->Option.getOr("")
    resDict->Dict.set(key, value1->mapper(key))
  })
  resDict
}

module LowRecoveryCodeBanner = {
  @react.component
  let make = (~recoveryCode) => {
    let (t, _, currentLang, _) = useTranslation()

    let bannerText = switch currentLang {
    | "zh" => `您的恢复代码不足。仅剩 ${recoveryCode->Int.toString} 个。`
    | _ => `You are low on recovery-codes. Only ${recoveryCode->Int.toString} left.`
    }

    let buttonText = switch currentLang {
    | "zh" => "重新生成恢复代码"
    | _ => "Regenerate recovery-codes"
    }

    <HSwitchUtils.AlertBanner bannerText bannerType=Warning>
      <Button
        text=buttonText
        buttonType={Secondary}
        onClick={_ =>
          RescriptReactRouter.push(
            GlobalVars.appendDashboardPath(~url=`/account-settings/profile`),
          )}
      />
    </HSwitchUtils.AlertBanner>
  }
}
