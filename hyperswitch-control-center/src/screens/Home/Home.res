open I18nUtils

@react.component
let make = (~setAppScreenState) => {
  open HomeUtils
  open PageUtils
  let (t, _, _, _) = useTranslation()
  let {userInfo: {recoveryCodesLeft}} = React.useContext(UserInfoProvider.defaultContext)
  let recoveryCode = recoveryCodesLeft->Option.getOr(0)

  let greeting = getGreeting()
  let titleText = `${greeting}, ${t("great_to_see_you")}`
  let subTitleText = t("welcome_message")

  <>
    <div className="flex flex-col gap-4">
      <RenderIf condition={recoveryCodesLeft->Option.isSome && recoveryCode < 3}>
        <HomeUtils.LowRecoveryCodeBanner recoveryCode />
      </RenderIf>
      <PendingInvitationsHome setAppScreenState />
    </div>
    <div className="w-full gap-8 flex flex-col">
      <PageHeading
        title=titleText
        subTitle=subTitleText
        customTitleStyle="!text-fs-24 !font-semibold"
        customSubTitleStyle="text-fs-16 text-nd_gray-400 !opacity-100 font-medium !mt-1"
      />
      <ControlCenter />
      <DevResources />
    </div>
  </>
}
