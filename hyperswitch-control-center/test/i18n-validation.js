// 国际化功能验证脚本
// 用于验证翻译文件和国际化功能的完整性

const fs = require('fs');
const path = require('path');

// 验证翻译文件
function validateTranslationFile() {
  console.log('🔍 验证翻译文件...');
  
  const zhFilePath = path.join(__dirname, '../src/locales/zh.res');
  
  if (!fs.existsSync(zhFilePath)) {
    console.error('❌ 中文翻译文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(zhFilePath, 'utf8');
  const lines = content.split('\n');
  
  console.log(`📊 翻译文件统计:`);
  console.log(`   - 总行数: ${lines.length}`);
  console.log(`   - 文件大小: ${(content.length / 1024).toFixed(2)} KB`);
  
  // 检查是否有重复的键
  const keyPattern = /dict->set\("([^"]+)"/g;
  const keys = [];
  let match;
  
  while ((match = keyPattern.exec(content)) !== null) {
    keys.push(match[1]);
  }
  
  const uniqueKeys = new Set(keys);
  const duplicateKeys = keys.filter((key, index) => keys.indexOf(key) !== index);
  
  console.log(`   - 翻译键总数: ${keys.length}`);
  console.log(`   - 唯一键数量: ${uniqueKeys.size}`);
  
  if (duplicateKeys.length > 0) {
    console.warn(`⚠️  发现重复键: ${duplicateKeys.join(', ')}`);
  } else {
    console.log('✅ 无重复键');
  }
  
  return true;
}

// 验证核心组件是否使用了统一的翻译系统
function validateComponentIntegration() {
  console.log('\n🔍 验证组件集成...');
  
  const componentsToCheck = [
    'src/screens/Home/Home.res',
    'src/screens/Home/HomeUtils.res',
    'src/components/NoDataFound.res',
    'src/components/Paginator.res',
    'src/components/Loader.res',
    'src/components/SearchInput.res',
    'src/entryPoints/AuthModule/Common/ResendBtn.res'
  ];
  
  let allIntegrated = true;
  
  componentsToCheck.forEach(componentPath => {
    const fullPath = path.join(__dirname, '..', componentPath);
    
    if (!fs.existsSync(fullPath)) {
      console.warn(`⚠️  文件不存在: ${componentPath}`);
      return;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // 检查是否使用了统一的翻译系统
    const hasI18nImport = content.includes('open I18nUtils');
    const hasUseTranslation = content.includes('useTranslation()');
    const hasOldTranslation = content.includes('getCurrentLanguage') || 
                             content.includes('translate') && !content.includes('useTranslation');
    
    if (hasI18nImport && hasUseTranslation && !hasOldTranslation) {
      console.log(`✅ ${componentPath} - 已集成统一翻译系统`);
    } else if (hasOldTranslation) {
      console.warn(`⚠️  ${componentPath} - 仍使用旧的翻译方式`);
      allIntegrated = false;
    } else {
      console.log(`ℹ️  ${componentPath} - 可能不需要翻译`);
    }
  });
  
  return allIntegrated;
}

// 验证编译状态
function validateCompilation() {
  console.log('\n🔍 验证编译状态...');
  
  // 检查是否有编译输出
  const distPath = path.join(__dirname, '../dist');
  
  if (fs.existsSync(distPath)) {
    console.log('✅ 编译输出目录存在');
    
    // 检查主要文件
    const mainFiles = ['hyperswitch/app.js', 'hyperswitch/app.css'];
    let allFilesExist = true;
    
    mainFiles.forEach(file => {
      const filePath = path.join(distPath, file);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ ${file} - ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      } else {
        console.warn(`⚠️  缺少文件: ${file}`);
        allFilesExist = false;
      }
    });
    
    return allFilesExist;
  } else {
    console.warn('⚠️  编译输出目录不存在，请先运行构建');
    return false;
  }
}

// 生成验证报告
function generateValidationReport() {
  console.log('\n📋 生成验证报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    translationFile: validateTranslationFile(),
    componentIntegration: validateComponentIntegration(),
    compilation: validateCompilation()
  };
  
  const reportPath = path.join(__dirname, '../docs/i18n-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📄 验证报告已保存到: ${reportPath}`);
  
  const allPassed = Object.values(report).every(result => 
    typeof result === 'boolean' ? result : true
  );
  
  if (allPassed) {
    console.log('\n🎉 所有验证项目通过！国际化功能已准备就绪。');
  } else {
    console.log('\n⚠️  部分验证项目需要注意，请查看详细报告。');
  }
  
  return report;
}

// 主函数
function main() {
  console.log('🚀 开始国际化功能验证...\n');
  
  try {
    const report = generateValidationReport();
    process.exit(0);
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  validateTranslationFile,
  validateComponentIntegration,
  validateCompilation,
  generateValidationReport
};
