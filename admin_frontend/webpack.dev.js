const { merge } = require("webpack-merge");
const common = require("./webpack.common.js");

module.exports = merge(common(), {
  mode: "development",
  devtool: "inline-source-map",
  devServer: {
    static: "./dist",
    port: 9001,
    historyApiFallback: true,
    hot: true,
    open: false,
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
  },
  output: {
    filename: "[name].bundle.js",
  },
});
