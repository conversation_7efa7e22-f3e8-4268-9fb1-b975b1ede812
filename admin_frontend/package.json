{"name": "pay-project-admin-dashboard", "version": "1.0.0", "main": "index.js", "author": "Pay Project Team <<EMAIL>>", "license": "MIT", "scripts": {"start": "cross-env APP_VERSION=$npm_package_version webpack serve --config webpack.dev.js", "build:prod": "npm run re:build && cross-env APP_VERSION=$npm_package_version webpack --config webpack.prod.js", "build:test": "npm run re:build && cross-env APP_VERSION=$npm_package_version webpack --config webpack.prod.js", "re:build": "rescript", "re:clean": "rescript clean", "re:start": "rescript -w", "re:format": "rescript format -all", "postinstall": "echo 'Setup complete'", "lint:fix": "echo '<PERSON><PERSON> skipped'"}, "husky": {"hooks": {"pre-commit": "npm run pre-commit"}}, "devDependencies": {"@pmmmwh/react-refresh-webpack-plugin": "^0.5.1", "copy-webpack-plugin": "^9.0.0", "cross-env": "^7.0.3", "css-loader": "^6.0.0", "css-minimizer-webpack-plugin": "^5.0.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.0.0", "postcss": "^8.3.6", "postcss-loader": "^6.0.0", "postcss-preset-env": "^7.0.0", "react-refresh": "^0.10.0", "rescript": "^11.1.1", "terser-webpack-plugin": "^5.1.3", "webpack": "^5.88.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.3.0"}, "dependencies": {"@headlessui/react": "^1.7.0", "@rescript/core": "^0.6.0", "@rescript/react": "^0.12.0", "bs-fetch": "^0.6.2", "dayjs": "^1.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recoil": "^0.7.0", "rescript-webapi": "^0.9.1", "tailwindcss": "^3.3.0"}}