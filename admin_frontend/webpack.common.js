const path = require("path");
const webpack = require("webpack");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const ReactRefreshWebpackPlugin = require("@pmmmwh/react-refresh-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const tailwindcss = require("tailwindcss");
// const MonacoWebpackPlugin = require("monaco-editor-webpack-plugin");

module.exports = () => {
  const isDevelopment = process.env.NODE_ENV !== "production";
  let entryObj = {
    app: `./src/entryPoints/AdminEntry.res.js`,
  };
  return {
    entry: entryObj,
    output: {
      path: path.resolve(__dirname, "dist", "admin"),
      clean: true,
      publicPath: "/",
    },
    module: {
      rules: [
        {
          test: /\.css$/i,
          use: [
            MiniCssExtractPlugin.loader,
            "css-loader",
            {
              loader: "postcss-loader",
              options: {
                postcssOptions: {
                  plugins: [[tailwindcss("./tailwind.config.js")]],
                },
              },
            },
          ],
        },
        {
          test: /\.ttf$/,
          use: ["file-loader"],
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/, // Fonts
          use: [
            {
              loader: "file-loader",
              options: {
                name: "fonts/[name].[ext]",
                outputPath: "assets/fonts/",
              },
            },
          ],
        },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: "public/admin/index.html",
        filename: "index.html",
        inject: "body",
      }),
      new MiniCssExtractPlugin(),
      new CopyPlugin({
        patterns: [
          { from: "public/common", globOptions: { ignore: ["**/index.html"] } },
        ].filter(Boolean),
      }),
      // new MonacoWebpackPlugin(),
      new webpack.DefinePlugin({
        dashboardAppEnv: JSON.stringify(process.env.NODE_ENV || "development"),
        GIT_COMMIT_HASH: JSON.stringify(process.env.GIT_COMMIT_HASH || ""),
        appVersion: JSON.stringify(process.env.APP_VERSION || "1.0.0"),
        "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV || "development"),
      }),
      isDevelopment && new ReactRefreshWebpackPlugin(),
    ].filter(Boolean),
  };
};
