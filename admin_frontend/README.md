# Pay Project 总后台管理系统

基于v1版本API构建的企业级支付平台总后台管理系统，提供组织管理、商户管理、用户管理等核心功能。

## 功能特性

### 🏢 组织管理
- 组织列表查看（支持分页）
- 组织详情展示和编辑
- 组织状态和类型管理
- 搜索和筛选功能

### 🏪 商户管理
- 商户账户列表管理
- 按组织筛选商户
- 商户状态监控
- 业务配置和连接器统计

### 👥 用户管理
- 用户账户管理
- 角色权限控制（SuperAdmin/PlatformAdmin/OrgAdmin）
- 登录状态跟踪
- 多级权限体系

### 📊 数据分析
- 核心业务指标展示
- 实时系统监控
- 平台运营数据统计
- 数据导出功能

### 💳 支付管理
- 支付记录查询和管理
- 多维度筛选和搜索
- 支付统计数据展示
- 支付状态监控和操作

### 🔌 连接器管理
- 支付连接器配置管理
- 连接器状态监控
- 支付方法配置
- 连接器测试和验证

### 💰 支付方法管理
- 支付方法类型配置
- 金额范围和货币设置
- 循环支付和分期付款
- 支付功能开关管理

### ⚙️ 系统设置
- 通用系统配置
- 安全策略管理
- 通知设置配置
- 集成和备份管理

### 🔐 权限控制
- 基于AdminApiAuth的身份验证
- 角色级别的访问控制
- 接口权限细粒度管理

## 技术栈

- **前端框架**: React 18 + ReScript
- **样式**: Tailwind CSS
- **构建工具**: Webpack 5
- **状态管理**: Recoil
- **图表**: ApexCharts / Highcharts
- **UI组件**: Headless UI
- **开发语言**: ReScript (编译到 JavaScript)

## 开发环境设置

### 前置要求

- Node.js >= 16
- npm 或 yarn
- ReScript 编译器

### 安装依赖

```bash
cd admin_frontend
npm install
# 或
yarn install
```

### 开发模式

```bash
# 启动 ReScript 编译器（监听模式）
npm run re:start

# 在另一个终端启动开发服务器
npm start
```

访问 http://localhost:9001 查看应用

### 生产构建

```bash
npm run build:prod
```

## 项目结构

```
admin_frontend/
├── src/
│   ├── components/          # 通用组件
│   ├── screens/            # 页面组件
│   │   ├── OrganizationList.res    # 组织管理页面
│   │   ├── MerchantList.res        # 商户管理页面
│   │   ├── UserList.res            # 用户管理页面
│   │   ├── Analytics.res           # 数据分析页面
│   │   ├── Settings.res            # 系统设置页面
│   │   ├── PaymentManagement.res   # 支付管理页面
│   │   ├── ConnectorManagement.res # 连接器管理页面
│   │   └── PaymentMethodManagement.res # 支付方法管理页面
│   ├── services/           # API服务层
│   │   ├── OrganizationService.res # 组织API服务
│   │   ├── MerchantService.res     # 商户API服务
│   │   ├── PaymentService.res      # 支付API服务
│   │   └── ConnectorService.res    # 连接器API服务
│   ├── types/              # 类型定义
│   │   ├── OrganizationTypes.res   # 组织相关类型
│   │   ├── MerchantTypes.res       # 商户相关类型
│   │   └── PaymentTypes.res        # 支付相关类型
│   ├── utils/              # 工具函数
│   ├── context/            # React Context
│   ├── api/                # API 工具
│   ├── locales/            # 国际化文件
│   └── entryPoints/        # 入口文件
│       └── AdminEntry.res          # 总后台主入口
├── public/                 # 静态资源
├── dist/                   # 构建输出
└── docs/                   # 文档
```

## API 接口

总后台基于v1版本API构建，主要使用以下接口：

### 组织管理
- `GET /organization` - 获取组织列表（支持分页）
- `GET /organization/{id}` - 获取组织详情
- `POST /organization` - 创建组织
- `PUT /organization/{id}` - 更新组织

### 商户管理
- `GET /accounts/list` - 获取商户列表
- `GET /accounts/{id}` - 获取商户详情
- `POST /accounts` - 创建商户
- `POST /accounts/{id}` - 更新商户
- `DELETE /accounts/{id}` - 删除商户

### 支付管理
- `GET /payments/list` - 获取支付列表
- `GET /payments/{id}` - 获取支付详情
- `GET /payments/aggregate` - 获取支付统计
- `GET /payments/filter` - 获取支付过滤器

### 连接器管理
- `GET /account/{merchant_id}/connectors` - 获取连接器列表
- `GET /account/{merchant_id}/connectors/{id}` - 获取连接器详情
- `POST /account/{merchant_id}/connectors` - 创建连接器
- `POST /account/{merchant_id}/connectors/{id}` - 更新连接器
- `DELETE /account/{merchant_id}/connectors/{id}` - 删除连接器

### 权限认证
- 使用 `AdminApiAuth` 进行身份验证
- Bearer Token 认证方式
- 基于角色的权限控制

## 环境配置

创建 `.env` 文件：

```env
APP_ENV=development
API_BASE_URL=http://localhost:8080
MIXPANEL_TOKEN=your_mixpanel_token
```

## 部署

### Docker 部署

```bash
docker build -t pay-project-admin .
docker run -p 9001:9001 pay-project-admin
```

### 传统部署

```bash
npm run build:prod
# 将 dist/ 目录部署到 Web 服务器
```

## 开发指南

### 添加新页面

1. 在 `src/screens/` 创建页面组件
2. 在 `src/entryPoints/AdminSidebarValues.res` 添加导航项
3. 在 `src/entryPoints/AdminApp.res` 添加路由

### 添加新 API

1. 在 `src/api/` 创建 API 模块
2. 定义类型在 `src/types/`
3. 添加 API 调用逻辑

### 国际化

1. 在 `src/locales/` 添加翻译文件
2. 使用 `I18n.t()` 函数进行翻译

## 测试

### API测试
```bash
# 运行完整API测试套件
./test/admin_backend_api_test.sh

# 测试特定功能模块
./test/admin_backend_api_test.sh org      # 组织接口测试
./test/admin_backend_api_test.sh merchant # 商户接口测试
./test/admin_backend_api_test.sh auth     # 权限验证测试
./test/admin_backend_api_test.sh health   # 服务健康检查
```

### 前端测试
```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

### 测试环境配置
```bash
# 设置测试用的API密钥
export ADMIN_API_KEY=your_test_admin_api_key

# 设置测试服务器地址
export TEST_BASE_URL=http://localhost:8080
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 邮箱: <EMAIL>
- Telegram: @yeeu

---

Powered by Pay Project Inc.
