#!/bin/bash

# Pay Project Admin Frontend Development Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if setup has been run
check_setup() {
    if [ ! -f .env ]; then
        print_error "Environment file not found. Please run setup first:"
        echo "  chmod +x scripts/setup.sh && ./scripts/setup.sh"
        exit 1
    fi
    
    if [ ! -d node_modules ]; then
        print_error "Dependencies not installed. Please run setup first:"
        echo "  chmod +x scripts/setup.sh && ./scripts/setup.sh"
        exit 1
    fi
}

# Start ReScript compiler in watch mode
start_rescript() {
    print_status "Starting ReScript compiler in watch mode..."
    yarn run re:start &
    RESCRIPT_PID=$!
    echo $RESCRIPT_PID > .rescript.pid
}

# Start development server
start_dev_server() {
    print_status "Starting development server..."
    yarn start &
    DEV_SERVER_PID=$!
    echo $DEV_SERVER_PID > .devserver.pid
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    if [ -f .rescript.pid ]; then
        RESCRIPT_PID=$(cat .rescript.pid)
        if ps -p $RESCRIPT_PID > /dev/null 2>&1; then
            kill $RESCRIPT_PID
        fi
        rm -f .rescript.pid
    fi
    
    if [ -f .devserver.pid ]; then
        DEV_SERVER_PID=$(cat .devserver.pid)
        if ps -p $DEV_SERVER_PID > /dev/null 2>&1; then
            kill $DEV_SERVER_PID
        fi
        rm -f .devserver.pid
    fi
    
    print_success "Cleanup completed"
}

# Handle script termination
trap cleanup EXIT INT TERM

# Main development function
main() {
    echo "=================================================="
    echo "  Pay Project Admin Frontend Development"
    echo "=================================================="
    echo ""
    
    check_setup
    
    print_status "Starting development environment..."
    
    # Clean previous builds
    print_status "Cleaning previous builds..."
    yarn run re:clean
    
    # Start ReScript compiler
    start_rescript
    
    # Wait a moment for ReScript to compile
    sleep 3
    
    # Start development server
    start_dev_server
    
    echo ""
    print_success "Development environment started! 🚀"
    echo ""
    echo "📱 Admin Dashboard: http://localhost:9001"
    echo "🔧 ReScript compiler is running in watch mode"
    echo "🔄 Hot reload is enabled"
    echo ""
    echo "Press Ctrl+C to stop the development server"
    echo ""
    
    # Wait for processes
    wait
}

# Handle command line arguments
case "${1:-}" in
    "setup")
        print_status "Running setup..."
        chmod +x scripts/setup.sh
        ./scripts/setup.sh
        ;;
    "clean")
        print_status "Cleaning build artifacts..."
        yarn run re:clean
        rm -rf dist
        rm -rf node_modules/.cache
        print_success "Clean completed"
        ;;
    "lint")
        print_status "Running linter..."
        yarn run lint:hooks
        ;;
    "lint:fix")
        print_status "Fixing linting issues..."
        yarn run lint:fix
        ;;
    "build")
        print_status "Building for production..."
        yarn run build:prod
        print_success "Production build completed"
        ;;
    "test")
        print_status "Running tests..."
        yarn run build:test
        print_success "Tests completed"
        ;;
    "help"|"-h"|"--help")
        echo "Pay Project Admin Frontend Development Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  (no command)  Start development server"
        echo "  setup         Run initial setup"
        echo "  clean         Clean build artifacts"
        echo "  lint          Run linter"
        echo "  lint:fix      Fix linting issues"
        echo "  build         Build for production"
        echo "  test          Run tests"
        echo "  help          Show this help message"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Run '$0 help' for available commands"
        exit 1
        ;;
esac
