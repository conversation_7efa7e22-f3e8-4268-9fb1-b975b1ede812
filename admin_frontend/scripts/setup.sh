#!/bin/bash

# Pay Project Admin Frontend Setup Script

set -e

echo "🚀 Setting up Pay Project Admin Frontend..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js version 16 or higher is required. Current version: $(node -v)"
        exit 1
    fi
    
    print_success "Node.js $(node -v) is installed"
}

# Check if yarn is installed
check_yarn() {
    if ! command -v yarn &> /dev/null; then
        print_warning "Yarn is not installed. Installing yarn..."
        npm install -g yarn
    fi
    print_success "Yarn $(yarn -v) is available"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    yarn install
    print_success "Dependencies installed successfully"
}

# Setup environment file
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        print_success "Environment file created. Please update .env with your configuration."
    else
        print_warning "Environment file already exists"
    fi
}

# Build ReScript files
build_rescript() {
    print_status "Building ReScript files..."
    yarn run re:build
    print_success "ReScript files built successfully"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p dist
    mkdir -p logs
    mkdir -p uploads
    print_success "Directories created"
}

# Setup git hooks (if git is available)
setup_git_hooks() {
    if [ -d .git ]; then
        print_status "Setting up git hooks..."
        chmod +x .githooks/commit-msg
        git config core.hooksPath .githooks
        print_success "Git hooks configured"
    else
        print_warning "Not a git repository, skipping git hooks setup"
    fi
}

# Verify setup
verify_setup() {
    print_status "Verifying setup..."
    
    # Check if build works
    if yarn run build:test > /dev/null 2>&1; then
        print_success "Build verification passed"
    else
        print_error "Build verification failed"
        exit 1
    fi
    
    # Check if ReScript compilation works
    if yarn run re:build > /dev/null 2>&1; then
        print_success "ReScript compilation verified"
    else
        print_error "ReScript compilation failed"
        exit 1
    fi
}

# Main setup process
main() {
    echo "=================================================="
    echo "  Pay Project Admin Frontend Setup"
    echo "=================================================="
    echo ""
    
    check_node
    check_yarn
    install_dependencies
    setup_env
    create_directories
    build_rescript
    setup_git_hooks
    verify_setup
    
    echo ""
    echo "=================================================="
    print_success "Setup completed successfully! 🎉"
    echo "=================================================="
    echo ""
    echo "Next steps:"
    echo "1. Update .env file with your configuration"
    echo "2. Start development server: yarn start"
    echo "3. Open http://localhost:9001 in your browser"
    echo ""
    echo "Available commands:"
    echo "  yarn start          - Start development server"
    echo "  yarn build:prod     - Build for production"
    echo "  yarn re:start       - Start ReScript compiler in watch mode"
    echo "  yarn lint:fix       - Fix linting issues"
    echo ""
    echo "For more information, see README.md"
}

# Run main function
main "$@"
