# Docker Compose for Pay Project Admin Frontend

version: '3.8'

services:
  # Admin Frontend
  admin-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pay-project-admin-frontend
    ports:
      - "9001:80"
    environment:
      - NODE_ENV=production
      - API_BASE_URL=http://backend:8080
    depends_on:
      - redis
    networks:
      - pay-project-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: pay-project-admin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-admin123}
    networks:
      - pay-project-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (optional)
  nginx-lb:
    image: nginx:alpine
    container_name: pay-project-admin-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - admin-frontend
    networks:
      - pay-project-network
    restart: unless-stopped

  # PostgreSQL for admin data (optional)
  postgres:
    image: postgres:15-alpine
    container_name: pay-project-admin-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-pay_project_admin}
      POSTGRES_USER: ${POSTGRES_USER:-admin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-admin123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - pay-project-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-admin}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: pay-project-admin-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - pay-project-network
    restart: unless-stopped

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: pay-project-admin-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - pay-project-network
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  pay-project-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
