// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";

function Settings(props) {
  return React.createElement("div", {
              className: "space-y-6"
            }, React.createElement("div", undefined, React.createElement("h1", {
                      className: "text-2xl font-bold text-gray-900"
                    }, "系统设置"), React.createElement("p", {
                      className: "mt-1 text-sm text-gray-600"
                    }, "配置系统参数和安全策略")), React.createElement("div", {
                  className: "bg-white shadow rounded-lg p-6"
                }, React.createElement("p", {
                      className: "text-gray-500"
                    }, "系统设置功能待实现")));
}

var make = Settings;

export {
  make ,
}
/* react Not a pure module */
