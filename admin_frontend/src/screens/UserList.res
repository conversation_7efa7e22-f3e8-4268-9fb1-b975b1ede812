// 用户管理页面

@react.component
let make = () => {
  <div className="space-y-6">
    <div>
      <h1 className="text-2xl font-bold text-gray-900">
        {React.string("用户管理")}
      </h1>
      <p className="mt-1 text-sm text-gray-600">
        {React.string("管理平台用户和权限")}
      </p>
    </div>

    <div className="bg-white shadow rounded-lg p-6">
      <p className="text-gray-500">{React.string("用户管理功能待实现")}</p>
    </div>
  </div>
}