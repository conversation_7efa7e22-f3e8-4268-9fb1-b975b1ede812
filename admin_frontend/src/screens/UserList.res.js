// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";

function UserList(props) {
  return React.createElement("div", {
              className: "space-y-6"
            }, React.createElement("div", undefined, React.createElement("h1", {
                      className: "text-2xl font-bold text-gray-900"
                    }, "用户管理"), React.createElement("p", {
                      className: "mt-1 text-sm text-gray-600"
                    }, "管理平台用户和权限")), React.createElement("div", {
                  className: "bg-white shadow rounded-lg p-6"
                }, React.createElement("p", {
                      className: "text-gray-500"
                    }, "用户管理功能待实现")));
}

var make = UserList;

export {
  make ,
}
/* react Not a pure module */
