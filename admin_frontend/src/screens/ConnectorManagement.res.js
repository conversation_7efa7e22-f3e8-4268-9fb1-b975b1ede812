// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";

function ConnectorManagement(props) {
  return React.createElement("div", {
              className: "space-y-6"
            }, React.createElement("div", undefined, React.createElement("h1", {
                      className: "text-2xl font-bold text-gray-900"
                    }, "连接器管理"), React.createElement("p", {
                      className: "mt-1 text-sm text-gray-600"
                    }, "管理支付连接器配置")), React.createElement("div", {
                  className: "bg-white shadow rounded-lg p-6"
                }, React.createElement("p", {
                      className: "text-gray-500"
                    }, "连接器管理功能待实现")));
}

var make = ConnectorManagement;

export {
  make ,
}
/* react Not a pure module */
