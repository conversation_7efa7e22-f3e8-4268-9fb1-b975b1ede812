open MerchantTypes
open MerchantService

@react.component
let make = () => {
  let (merchants, setMerchants) = React.useState(_ => [])
  let (loading, setLoading) = React.useState(_ => false)
  let (error, setError) = React.useState(_ => None)
  let (totalCount, setTotalCount) = React.useState(_ => 0)
  let (currentPage, setCurrentPage) = React.useState(_ => 1)
  let (pageSize, setPageSize) = React.useState(_ => 20)
  let (searchTerm, setSearchTerm) = React.useState(_ => "")
  let (selectedOrganization, setSelectedOrganization) = React.useState(_ => None)

  // 加载商户列表
  let loadMerchants = React.useCallback(async () => {
    setLoading(_ => true)
    setError(_ => None)
    
    try {
      let offset = (currentPage - 1) * pageSize
      let result = await getMerchantList(
        ~limit=pageSize, 
        ~offset, 
        ~organizationId=?selectedOrganization,
        ()
      )
      
      switch result {
      | Ok(response) => {
          setMerchants(_ => response.data)
          setTotalCount(_ => response.total_count)
        }
      | Error(err) => {
          setError(_ => Some(err))
          Console.error("加载商户列表失败:", err)
        }
      }
    } catch {
    | exn => {
        let errorMsg = exn->Exn.message->Option.getOr("未知错误")
        setError(_ => Some(`加载商户列表失败: ${errorMsg}`))
        Console.error("加载商户列表异常:", errorMsg)
      }
    }
    
    setLoading(_ => false)
  }, [currentPage, pageSize, selectedOrganization])

  // 初始加载
  React.useEffect(() => {
    loadMerchants()->ignore
    None
  }, [loadMerchants])

  // 处理页面变化
  let handlePageChange = (page: int) => {
    setCurrentPage(_ => page)
  }

  // 处理页面大小变化
  let handlePageSizeChange = (size: int) => {
    setPageSize(_ => size)
    setCurrentPage(_ => 1)
  }

  // 处理搜索
  let handleSearch = (term: string) => {
    setSearchTerm(_ => term)
    setCurrentPage(_ => 1)
    // TODO: 实现搜索功能
  }

  // 处理组织筛选
  let handleOrganizationFilter = (orgId: option<string>) => {
    setSelectedOrganization(_ => orgId)
    setCurrentPage(_ => 1)
  }

  // 刷新列表
  let handleRefresh = () => {
    loadMerchants()->ignore
  }

  // 获取商户显示名称
  let getMerchantDisplayName = (merchant: merchantAccount) => {
    switch merchant.merchant_name {
    | Some(name) => name
    | None => merchant.merchant_id
    }
  }

  // 获取商户状态显示
  let getStatusDisplay = (merchant: merchantAccount) => {
    switch merchant.status {
    | Some(status) => {
        let statusText = getMerchantStatusDisplayName(status, "zh")
        let statusColor = getMerchantStatusColor(status)
        <Badge color={statusColor}> {statusText->React.string} </Badge>
      }
    | None => <Badge color="secondary"> {"未知"->React.string} </Badge>
    }
  }

  // 获取商户类型显示
  let getTypeDisplay = (merchant: merchantAccount) => {
    switch merchant.merchantType {
    | Some(merchantType) => getMerchantTypeDisplayName(merchantType, "zh")
    | None => "未知"
    }
  }

  // 获取联系信息显示
  let getContactDisplay = (merchant: merchantAccount) => {
    switch merchant.merchant_details {
    | Some(details) => {
        switch details.contact_email {
        | Some(email) => email
        | None => switch details.contact_phone {
          | Some(phone) => phone
          | None => "-"
          }
        }
      }
    | None => "-"
    }
  }

  // 格式化日期
  let formatDate = (dateStr: string) => {
    // TODO: 实现日期格式化
    dateStr
  }

  // 表格列定义
  let columns = [
    {
      "title": "商户名称",
      "dataIndex": "name",
      "key": "name",
      "render": (_, merchant: merchantAccount) => {
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">
            {getMerchantDisplayName(merchant)->React.string}
          </span>
          <span className="text-sm text-gray-500">
            {merchant.merchant_id->React.string}
          </span>
        </div>
      }
    },
    {
      "title": "组织ID",
      "dataIndex": "organizationId",
      "key": "organizationId",
      "render": (_, merchant: merchantAccount) => {
        switch merchant.organization_id {
        | Some(orgId) => orgId->React.string
        | None => <span className="text-gray-400"> {"-"->React.string} </span>
        }
      }
    },
    {
      "title": "类型",
      "dataIndex": "type",
      "key": "type",
      "render": (_, merchant: merchantAccount) => {
        getTypeDisplay(merchant)->React.string
      }
    },
    {
      "title": "状态",
      "dataIndex": "status",
      "key": "status",
      "render": (_, merchant: merchantAccount) => {
        getStatusDisplay(merchant)
      }
    },
    {
      "title": "联系方式",
      "dataIndex": "contact",
      "key": "contact",
      "render": (_, merchant: merchantAccount) => {
        getContactDisplay(merchant)->React.string
      }
    },
    {
      "title": "业务配置",
      "dataIndex": "profileCount",
      "key": "profileCount",
      "render": (_, merchant: merchantAccount) => {
        switch merchant.profileCount {
        | Some(count) => `${count->Int.toString} 个配置`->React.string
        | None => <span className="text-gray-400"> {"-"->React.string} </span>
        }
      }
    },
    {
      "title": "连接器",
      "dataIndex": "connectorCount",
      "key": "connectorCount",
      "render": (_, merchant: merchantAccount) => {
        switch merchant.connectorCount {
        | Some(count) => `${count->Int.toString} 个连接器`->React.string
        | None => <span className="text-gray-400"> {"-"->React.string} </span>
        }
      }
    },
    {
      "title": "创建时间",
      "dataIndex": "createdAt",
      "key": "createdAt",
      "render": (_, merchant: merchantAccount) => {
        formatDate(merchant.created_at)->React.string
      }
    },
    {
      "title": "操作",
      "key": "actions",
      "render": (_, merchant: merchantAccount) => {
        <div className="flex space-x-2">
          <Button size="small" type_="link">
            {"查看"->React.string}
          </Button>
          <Button size="small" type_="link">
            {"编辑"->React.string}
          </Button>
          <Button size="small" type_="link" danger={true}>
            {"删除"->React.string}
          </Button>
        </div>
      }
    }
  ]

  <div className="p-6">
    {/* 页面标题 */}
    <div className="mb-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-2">
        {"商户管理"->React.string}
      </h1>
      <p className="text-gray-600">
        {"管理平台中的所有商户账户"->React.string}
      </p>
    </div>

    {/* 工具栏 */}
    <div className="mb-6 flex justify-between items-center">
      <div className="flex space-x-4">
        <Input.Search
          placeholder="搜索商户名称或ID"
          value={searchTerm}
          onChange={e => handleSearch(ReactEvent.Form.target(e)["value"])}
          style={ReactDOM.Style.make(~width="300px", ())}
        />
        <Select
          placeholder="选择组织"
          value={selectedOrganization}
          onChange={value => handleOrganizationFilter(value)}
          style={ReactDOM.Style.make(~width="200px", ())}
          allowClear={true}
        >
          {/* TODO: 动态加载组织选项 */}
        </Select>
        <Button onClick={_ => handleRefresh()} icon={<Icon name="refresh" />}>
          {"刷新"->React.string}
        </Button>
      </div>
      <Button type_="primary" icon={<Icon name="plus" />}>
        {"创建商户"->React.string}
      </Button>
    </div>

    {/* 错误提示 */}
    {switch error {
    | Some(errorMsg) => 
      <Alert
        type_="error"
        message="加载失败"
        description={errorMsg}
        showIcon={true}
        closable={true}
        onClose={_ => setError(_ => None)}
        className="mb-4"
      />
    | None => React.null
    }}

    {/* 数据表格 */}
    <Card>
      <Table
        columns={columns}
        dataSource={merchants}
        loading={loading}
        pagination={{
          "current": currentPage,
          "pageSize": pageSize,
          "total": totalCount,
          "showSizeChanger": true,
          "showQuickJumper": true,
          "showTotal": (total, range) => {
            `显示 ${range[0]->Int.toString}-${range[1]->Int.toString} 条，共 ${total->Int.toString} 条`
          },
          "onChange": handlePageChange,
          "onShowSizeChange": (_, size) => handlePageSizeChange(size),
        }}
        rowKey={merchant => merchant.merchant_id}
      />
    </Card>
  </div>
}
