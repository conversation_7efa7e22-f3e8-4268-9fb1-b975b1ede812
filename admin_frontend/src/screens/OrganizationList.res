open OrganizationTypes
open OrganizationService

@react.component
let make = () => {
  let (organizations, setOrganizations) = React.useState(_ => [])
  let (loading, setLoading) = React.useState(_ => false)
  let (error, setError) = React.useState(_ => None)
  let (totalCount, setTotalCount) = React.useState(_ => 0)
  let (currentPage, setCurrentPage) = React.useState(_ => 1)
  let (pageSize, setPageSize) = React.useState(_ => 20)
  let (searchTerm, setSearchTerm) = React.useState(_ => "")

  // 加载组织列表
  let loadOrganizations = React.useCallback(async () => {
    setLoading(_ => true)
    setError(_ => None)
    
    try {
      let offset = (currentPage - 1) * pageSize
      let result = await getOrganizationList(~limit=pageSize, ~offset, ())
      
      switch result {
      | Ok(response) => {
          setOrganizations(_ => response.data)
          setTotalCount(_ => response.total_count)
        }
      | Error(err) => {
          setError(_ => Some(err))
          Console.error("加载组织列表失败:", err)
        }
      }
    } catch {
    | exn => {
        let errorMsg = exn->Exn.message->Option.getOr("未知错误")
        setError(_ => Some(`加载组织列表失败: ${errorMsg}`))
        Console.error("加载组织列表异常:", errorMsg)
      }
    }
    
    setLoading(_ => false)
  }, [currentPage, pageSize])

  // 初始加载
  React.useEffect(() => {
    loadOrganizations()->ignore
    None
  }, [loadOrganizations])

  // 处理页面变化
  let handlePageChange = (page: int) => {
    setCurrentPage(_ => page)
  }

  // 处理页面大小变化
  let handlePageSizeChange = (size: int) => {
    setPageSize(_ => size)
    setCurrentPage(_ => 1) // 重置到第一页
  }

  // 处理搜索
  let handleSearch = (term: string) => {
    setSearchTerm(_ => term)
    setCurrentPage(_ => 1) // 重置到第一页
    // TODO: 实现搜索功能
  }

  // 刷新列表
  let handleRefresh = () => {
    loadOrganizations()->ignore
  }

  // 获取组织显示名称
  let getOrganizationDisplayName = (org: organization) => {
    switch org.organization_name {
    | Some(name) => name
    | None => org.id
    }
  }

  // 获取组织状态显示
  let getStatusDisplay = (org: organization) => {
    switch org.status {
    | Some(status) => {
        let statusText = getOrganizationStatusDisplayName(status, "zh")
        let statusColor = getOrganizationStatusColor(status)
        <Badge color={statusColor}> {statusText->React.string} </Badge>
      }
    | None => <Badge color="secondary"> {"未知"->React.string} </Badge>
    }
  }

  // 获取组织类型显示
  let getTypeDisplay = (org: organization) => {
    switch org.organizationType {
    | Some(orgType) => getOrganizationTypeDisplayName(orgType, "zh")
    | None => "未知"
    }
  }

  // 格式化日期
  let formatDate = (dateStr: string) => {
    // TODO: 实现日期格式化
    dateStr
  }

  // 表格列定义
  let columns = [
    {
      "title": "组织名称",
      "dataIndex": "name",
      "key": "name",
      "render": (_, org: organization) => {
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">
            {getOrganizationDisplayName(org)->React.string}
          </span>
          <span className="text-sm text-gray-500">
            {org.id->React.string}
          </span>
        </div>
      }
    },
    {
      "title": "类型",
      "dataIndex": "type",
      "key": "type",
      "render": (_, org: organization) => {
        getTypeDisplay(org)->React.string
      }
    },
    {
      "title": "状态",
      "dataIndex": "status",
      "key": "status",
      "render": (_, org: organization) => {
        getStatusDisplay(org)
      }
    },
    {
      "title": "联系邮箱",
      "dataIndex": "email",
      "key": "email",
      "render": (_, org: organization) => {
        switch org.contactEmail {
        | Some(email) => email->React.string
        | None => <span className="text-gray-400"> {"-"->React.string} </span>
        }
      }
    },
    {
      "title": "商户数量",
      "dataIndex": "merchantCount",
      "key": "merchantCount",
      "render": (_, org: organization) => {
        switch org.merchantCount {
        | Some(count) => count->Int.toString->React.string
        | None => <span className="text-gray-400"> {"-"->React.string} </span>
        }
      }
    },
    {
      "title": "创建时间",
      "dataIndex": "createdAt",
      "key": "createdAt",
      "render": (_, org: organization) => {
        formatDate(org.created_at)->React.string
      }
    },
    {
      "title": "操作",
      "key": "actions",
      "render": (_, org: organization) => {
        <div className="flex space-x-2">
          <Button size="small" type_="link">
            {"查看"->React.string}
          </Button>
          <Button size="small" type_="link">
            {"编辑"->React.string}
          </Button>
        </div>
      }
    }
  ]

  <div className="p-6">
    {/* 页面标题 */}
    <div className="mb-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-2">
        {"组织管理"->React.string}
      </h1>
      <p className="text-gray-600">
        {"管理平台中的所有组织"->React.string}
      </p>
    </div>

    {/* 工具栏 */}
    <div className="mb-6 flex justify-between items-center">
      <div className="flex space-x-4">
        <Input.Search
          placeholder="搜索组织名称或ID"
          value={searchTerm}
          onChange={e => handleSearch(ReactEvent.Form.target(e)["value"])}
          style={ReactDOM.Style.make(~width="300px", ())}
        />
        <Button onClick={_ => handleRefresh()} icon={<Icon name="refresh" />}>
          {"刷新"->React.string}
        </Button>
      </div>
      <Button type_="primary" icon={<Icon name="plus" />}>
        {"创建组织"->React.string}
      </Button>
    </div>

    {/* 错误提示 */}
    {switch error {
    | Some(errorMsg) => 
      <Alert
        type_="error"
        message="加载失败"
        description={errorMsg}
        showIcon={true}
        closable={true}
        onClose={_ => setError(_ => None)}
        className="mb-4"
      />
    | None => React.null
    }}

    {/* 数据表格 */}
    <Card>
      <Table
        columns={columns}
        dataSource={organizations}
        loading={loading}
        pagination={{
          "current": currentPage,
          "pageSize": pageSize,
          "total": totalCount,
          "showSizeChanger": true,
          "showQuickJumper": true,
          "showTotal": (total, range) => {
            `显示 ${range[0]->Int.toString}-${range[1]->Int.toString} 条，共 ${total->Int.toString} 条`
          },
          "onChange": handlePageChange,
          "onShowSizeChange": (_, size) => handlePageSizeChange(size),
        }}
        rowKey={org => org.id}
      />
    </Card>
  </div>
}
