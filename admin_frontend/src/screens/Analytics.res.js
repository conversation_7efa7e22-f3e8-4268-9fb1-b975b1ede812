// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";

function Analytics(props) {
  return React.createElement("div", {
              className: "space-y-6"
            }, React.createElement("div", undefined, React.createElement("h1", {
                      className: "text-2xl font-bold text-gray-900"
                    }, "数据分析"), React.createElement("p", {
                      className: "mt-1 text-sm text-gray-600"
                    }, "查看平台数据分析和统计信息")), React.createElement("div", {
                  className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
                }, React.createElement("div", {
                      className: "bg-white overflow-hidden shadow rounded-lg"
                    }, React.createElement("div", {
                          className: "p-5"
                        }, React.createElement("div", {
                              className: "flex items-center"
                            }, React.createElement("div", {
                                  className: "flex-shrink-0"
                                }, React.createElement("i", {
                                      className: "fas fa-users text-2xl text-blue-600"
                                    })), React.createElement("div", {
                                  className: "ml-5 w-0 flex-1"
                                }, React.createElement("dl", undefined, React.createElement("dt", {
                                          className: "text-sm font-medium text-gray-500 truncate"
                                        }, "总用户数"), React.createElement("dd", {
                                          className: "text-lg font-medium text-gray-900"
                                        }, "1,234"))))))));
}

var make = Analytics;

export {
  make ,
}
/* react Not a pure module */
