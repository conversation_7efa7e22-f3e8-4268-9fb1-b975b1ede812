// 数据分析页面

@react.component
let make = () => {
  <div className="space-y-6">
    <div>
      <h1 className="text-2xl font-bold text-gray-900">
        {React.string("数据分析")}
      </h1>
      <p className="mt-1 text-sm text-gray-600">
        {React.string("查看平台数据分析和统计信息")}
      </p>
    </div>


    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <i className="fas fa-users text-2xl text-blue-600" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  {React.string("总用户数")}
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {React.string("1,234")}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
}


