// Generated by ReScript, PLEASE EDIT WITH CARE

import * as En from "../locales/en.res.js";
import * as Zh from "../locales/zh.res.js";
import * as React from "react";
import * as LocalStorage from "./LocalStorage.res.js";

function languageToString(lang) {
  if (lang === "En") {
    return "en";
  } else {
    return "zh";
  }
}

function stringToLanguage(str) {
  if (str === "zh") {
    return "Zh";
  } else {
    return "En";
  }
}

function getCurrentLanguage() {
  return stringToLanguage(LocalStorage.getLanguage());
}

function setCurrentLanguage(lang) {
  LocalStorage.setLanguage(languageToString(lang));
}

function t(key, paramsOpt, param) {
  var params = paramsOpt !== undefined ? paramsOpt : [];
  var currentLang = getCurrentLanguage();
  if (currentLang === "En") {
    return En.t(key, params, undefined);
  } else {
    return Zh.t(key, params, undefined);
  }
}

function tWithLang(key, lang, paramsOpt, param) {
  var params = paramsOpt !== undefined ? paramsOpt : [];
  if (lang === "En") {
    return En.t(key, params, undefined);
  } else {
    return Zh.t(key, params, undefined);
  }
}

function getLanguageDisplayName(lang) {
  if (lang === "En") {
    return "English";
  } else {
    return "中文";
  }
}

function getSupportedLanguages() {
  return [
          [
            "En",
            "English"
          ],
          [
            "Zh",
            "中文"
          ]
        ];
}

function I18n$LanguageSelector(props) {
  var __className = props.className;
  var className = __className !== undefined ? __className : "";
  var match = React.useState(function () {
        return getCurrentLanguage();
      });
  var setCurrentLang = match[1];
  var handleLanguageChange = React.useCallback((function ($$event) {
          var target = $$event.target;
          var value = target.value;
          var newLang = stringToLanguage(value);
          setCurrentLang(function (param) {
                return newLang;
              });
          setCurrentLanguage(newLang);
        }), []);
  return React.createElement("select", {
              className: "block rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 " + className,
              value: languageToString(match[0]),
              onChange: handleLanguageChange
            }, [
                [
                  "En",
                  "English"
                ],
                [
                  "Zh",
                  "中文"
                ]
              ].map(function (param) {
                  var lang = param[0];
                  return React.createElement("option", {
                              key: languageToString(lang),
                              value: languageToString(lang)
                            }, param[1]);
                }));
}

var LanguageSelector = {
  make: I18n$LanguageSelector
};

function I18n$LanguageToggle(props) {
  var __className = props.className;
  var className = __className !== undefined ? __className : "";
  var match = React.useState(function () {
        return getCurrentLanguage();
      });
  var setCurrentLang = match[1];
  var currentLang = match[0];
  var toggleLanguage = React.useCallback((function () {
          var newLang;
          newLang = currentLang === "En" ? "Zh" : "En";
          setCurrentLang(function (param) {
                return newLang;
              });
          setCurrentLanguage(newLang);
          window.location.reload();
        }), [currentLang]);
  return React.createElement("button", {
              className: "inline-flex items-center px-3 py-2 border border-secondary-300 rounded-md text-sm font-medium text-text-secondary bg-surface hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 " + className,
              onClick: (function (param) {
                  toggleLanguage();
                })
            }, React.createElement("i", {
                  className: "fas fa-globe mr-2"
                }), getLanguageDisplayName(currentLang));
}

var LanguageToggle = {
  make: I18n$LanguageToggle
};

var i18nContext = React.createContext({
      currentLanguage: "En",
      setLanguage: (function (param) {
          
        }),
      t: (function (key, params) {
          return key;
        })
    });

var make = i18nContext.Provider;

var Provider = {
  make: make
};

function I18n$I18nProvider(props) {
  var match = React.useState(function () {
        return getCurrentLanguage();
      });
  var setCurrentLanguage = match[1];
  var currentLanguage = match[0];
  var setLanguage = React.useCallback((function (lang) {
          setCurrentLanguage(function (param) {
                return lang;
              });
          LocalStorage.setLanguage(languageToString(lang));
        }), []);
  var tFunc = React.useCallback((function (key, paramsOpt) {
          var params = paramsOpt !== undefined ? paramsOpt : [];
          return tWithLang(key, currentLanguage, params, undefined);
        }), [currentLanguage]);
  var contextValue = {
    currentLanguage: currentLanguage,
    setLanguage: setLanguage,
    t: tFunc
  };
  return React.createElement(make, {
              value: contextValue,
              children: props.children
            });
}

var I18nProvider = {
  make: I18n$I18nProvider
};

function useI18n() {
  return React.useContext(i18nContext);
}

function formatRelativeTime(date, _lang) {
  return date.toISOString().slice(0, 10);
}

function formatNumber(number, _lang) {
  return number.toString();
}

function formatCurrency(amount, currency, _lang) {
  return currency + " " + amount.toString();
}

function formatDate(date, _lang) {
  return date.toISOString().slice(0, 10);
}

export {
  languageToString ,
  stringToLanguage ,
  getCurrentLanguage ,
  setCurrentLanguage ,
  t ,
  tWithLang ,
  getLanguageDisplayName ,
  getSupportedLanguages ,
  LanguageSelector ,
  LanguageToggle ,
  i18nContext ,
  Provider ,
  I18nProvider ,
  useI18n ,
  formatRelativeTime ,
  formatNumber ,
  formatCurrency ,
  formatDate ,
}
/* i18nContext Not a pure module */
