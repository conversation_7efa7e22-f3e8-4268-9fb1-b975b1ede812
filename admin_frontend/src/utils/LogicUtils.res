// 逻辑工具函数

// 字符串工具
let isNonEmptyString = str => str->String.length > 0

let getNonEmptyString = str => str->isNonEmptyString ? Some(str) : None

let getValFromNullableValue = (value, defaultValue) => {
  switch value->Nullable.toOption {
  | Some(val) => val
  | None => defaultValue
  }
}

let snakeToTitle = str => {
  str
  ->String.split("_")
  ->Array.map(word => {
    let firstChar = word->String.charAt(0)->String.toUpperCase
    let restChars = word->String.sliceToEnd(~start=1)
    `${firstChar}${restChars}`
  })
  ->Array.joinWith(" ")
}

let camelToSnake = str => {
  str->String.replaceRegExp(%re("/([A-Z])/g"), "_$1")->String.toLowerCase
}

let kebabToCamel = str => {
  str->String.split("-")->Array.reduceWithIndex("", (acc, word, index) => {
    if index === 0 {
      acc ++ word
    } else {
      let firstChar = word->String.charAt(0)->String.toUpperCase
      let restChars = word->String.sliceToEnd(~start=1)
      acc ++ firstChar ++ restChars
    }
  })
}

// JSON 工具
let getDictFromJsonObject = json => {
  switch json->JSON.Classify.classify {
  | Object(dict) => dict
  | _ => Dict.make()
  }
}

let getDictfromDict = (dict, key) => {
  switch dict->Dict.get(key) {
  | Some(json) => getDictFromJsonObject(json)
  | None => Dict.make()
  }
}

let getString = (dict, key, defaultValue) => {
  switch dict->Dict.get(key) {
  | Some(json) => {
      switch json->JSON.Classify.classify {
      | String(s) => s
      | _ => defaultValue
      }
    }
  | None => defaultValue
  }
}

let getInt = (dict, key, defaultValue) => {
  switch dict->Dict.get(key) {
  | Some(json) => {
      switch json->JSON.Classify.classify {
      | Number(f) => f->Float.toInt
      | _ => defaultValue
      }
    }
  | None => defaultValue
  }
}

let getBool = (dict, key, defaultValue) => {
  switch dict->Dict.get(key) {
  | Some(json) => {
      switch json->JSON.Classify.classify {
      | Bool(b) => b
      | _ => defaultValue
      }
    }
  | None => defaultValue
  }
}

let getFloat = (dict, key, defaultValue) => {
  switch dict->Dict.get(key) {
  | Some(json) => {
      switch json->JSON.Classify.classify {
      | Number(f) => f
      | _ => defaultValue
      }
    }
  | None => defaultValue
  }
}

let getArray = (dict, key) => {
  switch dict->Dict.get(key) {
  | Some(json) => {
      switch json->JSON.Classify.classify {
      | Array(arr) => arr
      | _ => []
      }
    }
  | None => []
  }
}

// URL 工具
let getDictFromUrlSearchParams = searchParams => {
  let urlSearchParams = Webapi.Url.URLSearchParams.make(searchParams)
  let dict = Dict.make()
  
  urlSearchParams->Webapi.Url.URLSearchParams.forEach((value, key) => {
    dict->Dict.set(key, value)
  })
  
  dict
}

let getSearchParamsFromDict = dict => {
  let searchParams = Webapi.Url.URLSearchParams.make("")
  
  dict->Dict.toArray->Array.forEach(((key, value)) => {
    searchParams->Webapi.Url.URLSearchParams.set(key, value)
  })
  
  searchParams->Webapi.Url.URLSearchParams.toString
}

// 数组工具
let getUniqueArray = arr => {
  arr->Array.reduce([], (acc, item) => {
    acc->Array.includes(item) ? acc : acc->Array.concat([item])
  })
}

let chunkArray = (arr, size) => {
  let chunks = []
  let length = arr->Array.length
  let i = ref(0)
  
  while i.contents < length {
    let chunk = arr->Array.slice(~start=i.contents, ~end=i.contents + size)
    chunks->Array.push(chunk)->ignore
    i := i.contents + size
  }
  
  chunks
}

// 对象工具
let removeEmptyKeys = dict => {
  let newDict = Dict.make()
  
  dict->Dict.toArray->Array.forEach(((key, value)) => {
    switch value {
    | "" | "null" | "undefined" => ()
    | _ => newDict->Dict.set(key, value)
    }
  })
  
  newDict
}

// 时间工具
let getCurrentTimestamp = () => Date.now()

let formatTimestamp = (timestamp, _format) => {
  let date = Date.fromTime(timestamp)
  // 这里可以根据需要实现具体的格式化逻辑
  date->Date.toISOString
}

// 验证工具
let isValidEmail = email => {
  let emailRegex = %re("/^[^\s@]+@[^\s@]+\.[^\s@]+$/")
  emailRegex->RegExp.test(email)
}

let isValidUrl = url => {
  try {
    let _ = Webapi.Url.make(url)
    true
  } catch {
  | _ => false
  }
}

let isValidPhoneNumber = phone => {
  let phoneRegex = %re("/^\+?[\d\s\-\(\)]+$/")
  phoneRegex->RegExp.test(phone) && phone->String.length >= 10
}

// 错误处理
let safeParseJson = jsonString => {
  try {
    Some(JSON.parseExn(jsonString))
  } catch {
  | _ => None
  }
}

let safeStringify = value => {
  try {
    Some(JSON.stringifyAny(value)->Option.getOr(""))
  } catch {
  | _ => None
  }
}
