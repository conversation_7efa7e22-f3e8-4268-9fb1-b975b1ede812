// Generated by ReScript, PLEASE EDIT WITH CARE

import * as Core__JSON from "@rescript/core/src/Core__JSON.res.js";
import * as LogicUtils from "./LogicUtils.res.js";
import * as Caml_option from "rescript/lib/es6/caml_option.js";
import * as Core__Array from "@rescript/core/src/Core__Array.res.js";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";

function getItem(key) {
  try {
    return Caml_option.nullable_to_opt(localStorage.getItem(key));
  }
  catch (exn){
    return ;
  }
}

function setItem(key, value) {
  try {
    localStorage.setItem(key, value);
    return ;
  }
  catch (exn){
    console.error("Failed to set localStorage item: " + key);
    return ;
  }
}

function removeItem(key) {
  try {
    localStorage.removeItem(key);
    return ;
  }
  catch (exn){
    console.error("Failed to remove localStorage item: " + key);
    return ;
  }
}

function clear() {
  try {
    localStorage.clear();
    return ;
  }
  catch (exn){
    console.error("Failed to clear localStorage");
    return ;
  }
}

function isAvailable() {
  try {
    var testKey = "__localStorage_test__";
    setItem(testKey, "test");
    removeItem(testKey);
    return true;
  }
  catch (exn){
    return false;
  }
}

function getAllKeys() {
  return [];
}

function getStorageInfo() {
  try {
    var allKeys = [];
    var totalSize = Core__Array.reduce(allKeys, 0, (function (acc, key) {
            var value = getItem(key);
            if (value !== undefined) {
              return (acc + key.length | 0) + value.length | 0;
            } else {
              return acc;
            }
          }));
    return {
            used: totalSize,
            available: 5242880 - totalSize | 0
          };
  }
  catch (exn){
    return {
            used: 0,
            available: 0
          };
  }
}

function getJsonItem(key) {
  var jsonString = getItem(key);
  if (jsonString !== undefined) {
    return LogicUtils.safeParseJson(jsonString);
  }
  
}

function setJsonItem(key, value) {
  var jsonString = LogicUtils.safeStringify(value);
  if (jsonString !== undefined) {
    return setItem(key, jsonString);
  } else {
    console.error("Failed to stringify JSON for key: " + key);
    return ;
  }
}

function getObjectItem(key) {
  var json = getJsonItem(key);
  if (json !== undefined) {
    return Core__JSON.Decode.object(json);
  }
  
}

function setObjectItem(key, value) {
  setJsonItem(key, value);
}

function getArrayItem(key) {
  var json = getJsonItem(key);
  if (json !== undefined) {
    return Core__JSON.Decode.array(json);
  }
  
}

function setArrayItem(key, value) {
  setJsonItem(key, value);
}

var authToken = "admin_auth_token";

var userInfo = "admin_user_info";

var language = "admin_language";

var theme = "admin_theme";

var sidebarCollapsed = "admin_sidebar_collapsed";

var recentOrganizations = "admin_recent_organizations";

var recentMerchants = "admin_recent_merchants";

var dashboardPreferences = "admin_dashboard_preferences";

var tableSettings = "admin_table_settings";

var Keys = {
  authToken: authToken,
  userInfo: userInfo,
  language: language,
  theme: theme,
  sidebarCollapsed: sidebarCollapsed,
  recentOrganizations: recentOrganizations,
  recentMerchants: recentMerchants,
  dashboardPreferences: dashboardPreferences,
  tableSettings: tableSettings
};

function getAuthToken() {
  return getItem(authToken);
}

function setAuthToken(token) {
  setItem(authToken, token);
}

function removeAuthToken() {
  removeItem(authToken);
}

function getUserInfo() {
  return getObjectItem(userInfo);
}

function setUserInfo(userInfo$1) {
  setJsonItem(userInfo, userInfo$1);
}

function removeUserInfo() {
  removeItem(userInfo);
}

function getLanguage() {
  return Core__Option.getOr(getItem(language), "en");
}

function setLanguage(language$1) {
  setItem(language, language$1);
}

function getTheme() {
  return Core__Option.getOr(getItem(theme), "light");
}

function setTheme(theme$1) {
  setItem(theme, theme$1);
}

function getSidebarCollapsed() {
  var match = getItem(sidebarCollapsed);
  if (match === "true") {
    return true;
  } else {
    return false;
  }
}

function setSidebarCollapsed(collapsed) {
  setItem(sidebarCollapsed, collapsed ? "true" : "false");
}

function clearAppData() {
  removeAuthToken();
  removeUserInfo();
  removeItem(recentOrganizations);
  removeItem(recentMerchants);
  removeItem(dashboardPreferences);
  removeItem(tableSettings);
}

export {
  getItem ,
  setItem ,
  removeItem ,
  clear ,
  isAvailable ,
  getAllKeys ,
  getStorageInfo ,
  getJsonItem ,
  setJsonItem ,
  getObjectItem ,
  setObjectItem ,
  getArrayItem ,
  setArrayItem ,
  Keys ,
  getAuthToken ,
  setAuthToken ,
  removeAuthToken ,
  getUserInfo ,
  setUserInfo ,
  removeUserInfo ,
  getLanguage ,
  setLanguage ,
  getTheme ,
  setTheme ,
  getSidebarCollapsed ,
  setSidebarCollapsed ,
  clearAppData ,
}
/* No side effect */
