// LocalStorage 工具函数

// 外部绑定
@val @scope("localStorage") external getItem: string => Nullable.t<string> = "getItem"
@val @scope("localStorage") external setItem: (string, string) => unit = "setItem"
@val @scope("localStorage") external removeItem: string => unit = "removeItem"
@val @scope("localStorage") external clear: unit => unit = "clear"

// 获取 localStorage 项
let getItem = (key: string): option<string> => {
  try {
    getItem(key)->Nullable.toOption
  } catch {
  | _ => None
  }
}

// 设置 localStorage 项
let setItem = (key: string, value: string): unit => {
  try {
    setItem(key, value)
  } catch {
  | _ => Console.error(`Failed to set localStorage item: ${key}`)
  }
}

// 删除 localStorage 项
let removeItem = (key: string): unit => {
  try {
    removeItem(key)
  } catch {
  | _ => Console.error(`Failed to remove localStorage item: ${key}`)
  }
}

// 清空 localStorage
let clear = (): unit => {
  try {
    clear()
  } catch {
  | _ => Console.error("Failed to clear localStorage")
  }
}

// 检查 localStorage 是否可用
let isAvailable = (): bool => {
  try {
    let testKey = "__localStorage_test__"
    setItem(testKey, "test")
    removeItem(testKey)
    true
  } catch {
  | _ => false
  }
}

// 获取所有 localStorage 键
let getAllKeys = (): array<string> => {
  []
}

// 存储信息类型
type storageInfo = {used: int, available: int}

// 获取 localStorage 使用情况（估算）
let getStorageInfo = (): storageInfo => {
  try {
    let allKeys = getAllKeys()
    let totalSize = allKeys->Array.reduce(0, (acc, key) => {
      switch getItem(key) {
      | Some(value) => acc + key->String.length + value->String.length
      | None => acc
      }
    })
    
    // 大多数浏览器的 localStorage 限制约为 5-10MB
    let estimatedLimit = 5 * 1024 * 1024 // 5MB
    
    {used: totalSize, available: estimatedLimit - totalSize}
  } catch {
  | _ => {used: 0, available: 0}
  }
}

// JSON 相关的便捷函数
let getJsonItem = (key: string): option<JSON.t> => {
  switch getItem(key) {
  | Some(jsonString) => LogicUtils.safeParseJson(jsonString)
  | None => None
  }
}

let setJsonItem = (key: string, value: JSON.t): unit => {
  switch LogicUtils.safeStringify(value) {
  | Some(jsonString) => setItem(key, jsonString)
  | None => Console.error(`Failed to stringify JSON for key: ${key}`)
  }
}

// 对象相关的便捷函数
let getObjectItem = (key: string): option<Dict.t<JSON.t>> => {
  switch getJsonItem(key) {
  | Some(json) => json->JSON.Decode.object
  | None => None
  }
}

let setObjectItem = (key: string, value: Dict.t<JSON.t>): unit => {
  setJsonItem(key, value->JSON.Encode.object)
}

// 数组相关的便捷函数
let getArrayItem = (key: string): option<array<JSON.t>> => {
  switch getJsonItem(key) {
  | Some(json) => json->JSON.Decode.array
  | None => None
  }
}

let setArrayItem = (key: string, value: array<JSON.t>): unit => {
  setJsonItem(key, value->JSON.Encode.array)
}

// 特定于应用的键名常量
module Keys = {
  let authToken = "admin_auth_token"
  let userInfo = "admin_user_info"
  let language = "admin_language"
  let theme = "admin_theme"
  let sidebarCollapsed = "admin_sidebar_collapsed"
  let recentOrganizations = "admin_recent_organizations"
  let recentMerchants = "admin_recent_merchants"
  let dashboardPreferences = "admin_dashboard_preferences"
  let tableSettings = "admin_table_settings"
}

// 应用特定的便捷函数
let getAuthToken = () => getItem(Keys.authToken)
let setAuthToken = (token: string) => setItem(Keys.authToken, token)
let removeAuthToken = () => removeItem(Keys.authToken)

let getUserInfo = () => getObjectItem(Keys.userInfo)
let setUserInfo = (userInfo: Dict.t<JSON.t>) => setObjectItem(Keys.userInfo, userInfo)
let removeUserInfo = () => removeItem(Keys.userInfo)

let getLanguage = () => getItem(Keys.language)->Option.getOr("en")
let setLanguage = (language: string) => setItem(Keys.language, language)

let getTheme = () => getItem(Keys.theme)->Option.getOr("light")
let setTheme = (theme: string) => setItem(Keys.theme, theme)

let getSidebarCollapsed = () => {
  switch getItem(Keys.sidebarCollapsed) {
  | Some("true") => true
  | _ => false
  }
}
let setSidebarCollapsed = (collapsed: bool) => setItem(Keys.sidebarCollapsed, collapsed ? "true" : "false")

// 清理应用数据
let clearAppData = () => {
  removeAuthToken()
  removeUserInfo()
  removeItem(Keys.recentOrganizations)
  removeItem(Keys.recentMerchants)
  removeItem(Keys.dashboardPreferences)
  removeItem(Keys.tableSettings)
}
