// 全局变量和配置
type urlThemeConfig = {
  faviconUrl: option<string>,
  logoUrl: option<string>,
}

type urlConfig = {
  apiBaseUrl: string,
  mixpanelToken: string,
  sdkBaseUrl: option<string>,
  agreementUrl: option<string>,
  dssCertificateUrl: option<string>,
  dynamoSimulationTemplateUrl: option<string>,
  applePayCertificateUrl: option<string>,
  agreementVersion: option<string>,
  reconIframeUrl: option<string>,
  urlThemeConfig: urlThemeConfig,
  hypersenseUrl: string,
  clarityBaseUrl: option<string>,
}

// 默认配置
let defaultConfig: urlConfig = {
  apiBaseUrl: "http://localhost:8080",
  mixpanelToken: "",
  sdkBaseUrl: None,
  agreementUrl: None,
  dssCertificateUrl: None,
  dynamoSimulationTemplateUrl: None,
  applePayCertificateUrl: None,
  agreementVersion: None,
  reconIframeUrl: None,
  urlThemeConfig: {
    faviconUrl: Some("/PayProjectFavicon.svg"),
    logoUrl: Some("/PayProjectLogo.svg"),
  },
  hypersenseUrl: "",
  clarityBaseUrl: None,
}

// 获取主机URL
let getHostUrlWithBasePath = () => {
  let protocol = Webapi.Dom.location->Webapi.Dom.Location.protocol
  let host = Webapi.Dom.location->Webapi.Dom.Location.host
  `${protocol}//${host}`
}

// 获取API基础URL
let getApiBaseUrl = () => {
  defaultConfig.apiBaseUrl
}

// 提取模块路径
let extractModulePath = (~path: list<string>, ~query: string, ~end: int) => {
  let pathArray = path->List.toArray
  let extractedPath = pathArray->Array.slice(~start=0, ~end)
  let pathString = extractedPath->Array.joinWith("/")
  query->String.length > 0 ? `/${pathString}?${query}` : `/${pathString}`
}

// 应用路径前缀
let appendDashboardPath = (~url: string) => {
  `/admin${url}`
}

// 环境变量 - 使用webpack DefinePlugin定义的变量
let isDevelopment = %raw(`typeof dashboardAppEnv !== "undefined" && dashboardAppEnv === "development"`)
let isProduction = %raw(`typeof dashboardAppEnv !== "undefined" && dashboardAppEnv === "production"`)
let appVersion = %raw(`typeof appVersion !== "undefined" ? appVersion : "1.0.0"`)
let gitCommitHash = %raw(`typeof GIT_COMMIT_HASH !== "undefined" ? GIT_COMMIT_HASH : ""`)

// 应用配置
let appConfig = {
  "name": "Pay Project Admin",
  "version": appVersion,
  "description": "Pay Project 总后台管理系统",
  "author": "Pay Project Team",
  "contact": {
    "email": "<EMAIL>",
    "telegram": "@yeeu",
  },
}
