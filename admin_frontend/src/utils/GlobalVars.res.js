// Generated by ReScript, PLEASE EDIT WITH CARE

import * as Core__List from "@rescript/core/src/Core__List.res.js";

function getHostUrlWithBasePath() {
  var protocol = window.location.protocol;
  var host = window.location.host;
  return protocol + "//" + host;
}

function getApiBaseUrl() {
  return "http://localhost:8080";
}

function extractModulePath(path, query, end) {
  var pathArray = Core__List.toArray(path);
  var extractedPath = pathArray.slice(0, end);
  var pathString = extractedPath.join("/");
  if (query.length > 0) {
    return "/" + pathString + "?" + query;
  } else {
    return "/" + pathString;
  }
}

function appendDashboardPath(url) {
  return "/admin" + url;
}

var isDevelopment = (typeof dashboardAppEnv !== "undefined" && dashboardAppEnv === "development");

var isProduction = (typeof dashboardAppEnv !== "undefined" && dashboardAppEnv === "production");

var appVersion = (typeof appVersion !== "undefined" ? appVersion : "1.0.0");

var gitCommitHash = (typeof GIT_COMMIT_HASH !== "undefined" ? GIT_COMMIT_HASH : "");

var appConfig = {
  name: "Pay Project Admin",
  version: appVersion,
  description: "Pay Project 总后台管理系统",
  author: "Pay Project Team",
  contact: {
    email: "<EMAIL>",
    telegram: "@yeeu"
  }
};

var defaultConfig = {
  apiBaseUrl: "http://localhost:8080",
  mixpanelToken: "",
  sdkBaseUrl: undefined,
  agreementUrl: undefined,
  dssCertificateUrl: undefined,
  dynamoSimulationTemplateUrl: undefined,
  applePayCertificateUrl: undefined,
  agreementVersion: undefined,
  reconIframeUrl: undefined,
  urlThemeConfig: {
    faviconUrl: "/PayProjectFavicon.svg",
    logoUrl: "/PayProjectLogo.svg"
  },
  hypersenseUrl: "",
  clarityBaseUrl: undefined
};

export {
  defaultConfig ,
  getHostUrlWithBasePath ,
  getApiBaseUrl ,
  extractModulePath ,
  appendDashboardPath ,
  isDevelopment ,
  isProduction ,
  appVersion ,
  gitCommitHash ,
  appConfig ,
}
/* isDevelopment Not a pure module */
