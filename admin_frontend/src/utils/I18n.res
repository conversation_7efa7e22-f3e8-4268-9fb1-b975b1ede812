// 国际化工具

// 支持的语言
type language = En | Zh

// 语言转字符串
let languageToString = lang => {
  switch lang {
  | En => "en"
  | Zh => "zh"
  }
}

// 字符串转语言
let stringToLanguage = str => {
  switch str {
  | "zh" => Zh
  | _ => En // 默认英文
  }
}

// 获取当前语言
let getCurrentLanguage = () => {
  LocalStorage.getLanguage()->stringToLanguage
}

// 设置当前语言
let setCurrentLanguage = (lang: language) => {
  LocalStorage.setLanguage(lang->languageToString)
}

// 翻译函数
let t = (key: string, ~params: array<string>=[], ()) => {
  let currentLang = getCurrentLanguage()
  
  switch currentLang {
  | Zh => Zh.t(key, ~params, ())
  | En => En.t(key, ~params, ())
  }
}

// 带语言参数的翻译函数
let tWithLang = (key: string, lang: language, ~params: array<string>=[], ()) => {
  switch lang {
  | Zh => Zh.t(key, ~params, ())
  | En => En.t(key, ~params, ())
  }
}

// 获取语言显示名称
let getLanguageDisplayName = (lang: language) => {
  switch lang {
  | En => "English"
  | Zh => "中文"
  }
}

// 获取所有支持的语言
let getSupportedLanguages = () => [
  (En, "English"),
  (Zh, "中文"),
]

// 语言切换组件
module LanguageSelector = {
  @react.component
  let make = (~className: string="") => {
    let (currentLang, setCurrentLang) = React.useState(() => getCurrentLanguage())
    
    let handleLanguageChange = React.useCallback((event: ReactEvent.Form.t) => {
      let target = event->ReactEvent.Form.target
      let value = target["value"]
      let newLang = value->stringToLanguage
      setCurrentLang(_ => newLang)
      setCurrentLanguage(newLang)
    }, [])
    
    <select
      className={`block rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 ${className}`}
      value={currentLang->languageToString}
      onChange={handleLanguageChange}>
      {getSupportedLanguages()
        ->Array.map(((lang, displayName)) => 
          <option key={lang->languageToString} value={lang->languageToString}>
            {React.string(displayName)}
          </option>
        )
        ->React.array}
    </select>
  }
}

// 语言切换按钮组件
module LanguageToggle = {
  @react.component
  let make = (~className: string="") => {
    let (currentLang, setCurrentLang) = React.useState(() => getCurrentLanguage())
    
    let toggleLanguage = React.useCallback(() => {
      let newLang = switch currentLang {
      | En => Zh
      | Zh => En
      }
      setCurrentLang(_ => newLang)
      setCurrentLanguage(newLang)
      
      // 刷新页面以应用新语言
      Webapi.Dom.location->Webapi.Dom.Location.reload
    }, [currentLang])
    
    <button
      className={`inline-flex items-center px-3 py-2 border border-secondary-300 rounded-md text-sm font-medium text-text-secondary bg-surface hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${className}`}
      onClick={_ => toggleLanguage()}>
      <i className="fas fa-globe mr-2" />
      {React.string(currentLang->getLanguageDisplayName)}
    </button>
  }
}

// 国际化上下文
type i18nContextType = {
  currentLanguage: language,
  setLanguage: language => unit,
  t: (string, ~params: array<string>=?) => string,
}

let i18nContext = React.createContext({
  currentLanguage: En,
  setLanguage: _ => (),
  t: (key, ~params=?) => {
    let _ = params
    key
  },
})

module Provider = {
  let make = React.Context.provider(i18nContext)
}

// 国际化提供者组件
module I18nProvider = {
  @react.component
  let make = (~children) => {
    let (currentLanguage, setCurrentLanguage) = React.useState(() => getCurrentLanguage())
    
    let setLanguage = React.useCallback((lang: language) => {
      setCurrentLanguage(_ => lang)
      LocalStorage.setLanguage(lang->languageToString)
    }, [])
    
    let tFunc = React.useCallback((key: string, ~params: array<string>=[]) => {
      tWithLang(key, currentLanguage, ~params, ())
    }, [currentLanguage])
    
    let contextValue = {
      currentLanguage,
      setLanguage,
      t: tFunc,
    }

    <Provider value={contextValue}>
      {children}
    </Provider>
  }
}

// 使用国际化的 Hook
let useI18n = () => {
  React.useContext(i18nContext)
}



// 格式化相对时间（根据语言环境）
let formatRelativeTime = (date: Date.t, _lang: language) => {
  date->Date.toISOString->String.slice(~start=0, ~end=10)
}

// 简化的格式化函数
let formatNumber = (number: float, _lang: language) => {
  number->Float.toString
}

let formatCurrency = (amount: float, currency: string, _lang: language) => {
  `${currency} ${amount->Float.toString}`
}

let formatDate = (date: Date.t, _lang: language) => {
  date->Date.toISOString->String.slice(~start=0, ~end=10)
}
