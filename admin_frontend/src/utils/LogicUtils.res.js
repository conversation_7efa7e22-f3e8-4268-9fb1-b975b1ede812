// Generated by ReScript, PLEASE EDIT WITH CARE

import * as Core__JSON from "@rescript/core/src/Core__JSON.res.js";
import * as Caml_option from "rescript/lib/es6/caml_option.js";
import * as Core__Array from "@rescript/core/src/Core__Array.res.js";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";

function isNonEmptyString(str) {
  return str.length > 0;
}

function getNonEmptyString(str) {
  if (str.length > 0) {
    return str;
  }
  
}

function getValFromNullableValue(value, defaultValue) {
  if (value == null) {
    return defaultValue;
  } else {
    return value;
  }
}

function snakeToTitle(str) {
  return str.split("_").map(function (word) {
                var firstChar = word.charAt(0).toUpperCase();
                var restChars = word.slice(1);
                return firstChar + restChars;
              }).join(" ");
}

function camelToSnake(str) {
  return str.replace(/([A-Z])/g, "_$1").toLowerCase();
}

function kebabToCamel(str) {
  return Core__Array.reduceWithIndex(str.split("-"), "", (function (acc, word, index) {
                if (index === 0) {
                  return acc + word;
                }
                var firstChar = word.charAt(0).toUpperCase();
                var restChars = word.slice(1);
                return acc + firstChar + restChars;
              }));
}

function getDictFromJsonObject(json) {
  var dict = Core__JSON.Classify.classify(json);
  if (typeof dict !== "object") {
    return {};
  } else if (dict.TAG === "Object") {
    return dict._0;
  } else {
    return {};
  }
}

function getDictfromDict(dict, key) {
  var json = dict[key];
  if (json !== undefined) {
    return getDictFromJsonObject(Caml_option.valFromOption(json));
  } else {
    return {};
  }
}

function getString(dict, key, defaultValue) {
  var json = dict[key];
  if (json === undefined) {
    return defaultValue;
  }
  var s = Core__JSON.Classify.classify(Caml_option.valFromOption(json));
  if (typeof s !== "object" || s.TAG !== "String") {
    return defaultValue;
  } else {
    return s._0;
  }
}

function getInt(dict, key, defaultValue) {
  var json = dict[key];
  if (json === undefined) {
    return defaultValue;
  }
  var f = Core__JSON.Classify.classify(Caml_option.valFromOption(json));
  if (typeof f !== "object" || f.TAG !== "Number") {
    return defaultValue;
  } else {
    return f._0 | 0;
  }
}

function getBool(dict, key, defaultValue) {
  var json = dict[key];
  if (json === undefined) {
    return defaultValue;
  }
  var b = Core__JSON.Classify.classify(Caml_option.valFromOption(json));
  if (typeof b !== "object" || b.TAG !== "Bool") {
    return defaultValue;
  } else {
    return b._0;
  }
}

function getFloat(dict, key, defaultValue) {
  var json = dict[key];
  if (json === undefined) {
    return defaultValue;
  }
  var f = Core__JSON.Classify.classify(Caml_option.valFromOption(json));
  if (typeof f !== "object" || f.TAG !== "Number") {
    return defaultValue;
  } else {
    return f._0;
  }
}

function getArray(dict, key) {
  var json = dict[key];
  if (json === undefined) {
    return [];
  }
  var arr = Core__JSON.Classify.classify(Caml_option.valFromOption(json));
  if (typeof arr !== "object") {
    return [];
  } else if (arr.TAG === "Array") {
    return arr._0;
  } else {
    return [];
  }
}

function getDictFromUrlSearchParams(searchParams) {
  var urlSearchParams = new URLSearchParams(searchParams);
  var dict = {};
  urlSearchParams.forEach(function (value, key) {
        dict[key] = value;
      });
  return dict;
}

function getSearchParamsFromDict(dict) {
  var searchParams = new URLSearchParams("");
  Object.entries(dict).forEach(function (param) {
        searchParams.set(param[0], param[1]);
      });
  return searchParams.toString();
}

function getUniqueArray(arr) {
  return Core__Array.reduce(arr, [], (function (acc, item) {
                if (acc.includes(item)) {
                  return acc;
                } else {
                  return acc.concat([item]);
                }
              }));
}

function chunkArray(arr, size) {
  var chunks = [];
  var length = arr.length;
  var i = 0;
  while(i < length) {
    var chunk = arr.slice(i, i + size | 0);
    chunks.push(chunk);
    i = i + size | 0;
  };
  return chunks;
}

function removeEmptyKeys(dict) {
  var newDict = {};
  Object.entries(dict).forEach(function (param) {
        var value = param[1];
        switch (value) {
          case "" :
          case "null" :
          case "undefined" :
              return ;
          default:
            newDict[param[0]] = value;
            return ;
        }
      });
  return newDict;
}

function getCurrentTimestamp() {
  return Date.now();
}

function formatTimestamp(timestamp, _format) {
  return new Date(timestamp).toISOString();
}

function isValidEmail(email) {
  var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  }
  catch (exn){
    return false;
  }
}

function isValidPhoneNumber(phone) {
  var phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  if (phoneRegex.test(phone)) {
    return phone.length >= 10;
  } else {
    return false;
  }
}

function safeParseJson(jsonString) {
  try {
    return JSON.parse(jsonString);
  }
  catch (exn){
    return ;
  }
}

function safeStringify(value) {
  try {
    return Core__Option.getOr(JSON.stringify(value), "");
  }
  catch (exn){
    return ;
  }
}

export {
  isNonEmptyString ,
  getNonEmptyString ,
  getValFromNullableValue ,
  snakeToTitle ,
  camelToSnake ,
  kebabToCamel ,
  getDictFromJsonObject ,
  getDictfromDict ,
  getString ,
  getInt ,
  getBool ,
  getFloat ,
  getArray ,
  getDictFromUrlSearchParams ,
  getSearchParamsFromDict ,
  getUniqueArray ,
  chunkArray ,
  removeEmptyKeys ,
  getCurrentTimestamp ,
  formatTimestamp ,
  isValidEmail ,
  isValidUrl ,
  isValidPhoneNumber ,
  safeParseJson ,
  safeStringify ,
}
/* No side effect */
