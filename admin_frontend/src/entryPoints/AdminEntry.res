// 总后台入口文件

// 导入样式文件
%%raw(`require("../styles/main.css")`)

// 登录页面组件
module LoginPage = {
  @react.component
  let make = () => {
    let {login, isLoading, error, clearError} = AuthContext.useAuth()
    let (email, setEmail) = React.useState(_ => "")
    let (password, setPassword) = React.useState(_ => "")
    let (rememberMe, setRememberMe) = React.useState(_ => false)

    let handleSubmit = React.useCallback((e: ReactEvent.Form.t) => {
      e->ReactEvent.Form.preventDefault
      clearError()
      Console.log("Form submitted, calling login function...")
      Console.log(`Email: ${email}, Password: ${password}`)
      let _ = login(email, password, rememberMe)
      ()
    }, (email, password, rememberMe, login, clearError))

    <div className="min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <i className="fas fa-shield-alt text-primary-600 text-xl" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-text-primary">
            {React.string("管理员登录")}
          </h2>
          <p className="mt-2 text-center text-sm text-text-secondary">
            {React.string("Pay Project 总后台管理系统")}
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {switch error {
          | Some(message) =>
            <div className="bg-error-50 border border-error-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="fas fa-exclamation-circle text-error-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-error-800">
                    {React.string(message)}
                  </p>
                </div>
              </div>
            </div>
          | None => React.null
          }}

          <div className="space-y-4">
            <Input.Email
              value={email}
              onChange={value => setEmail(_ => value)}
              placeholder="邮箱地址"
            />

            <Input.Password
              value={password}
              onChange={value => setPassword(_ => value)}
              placeholder="密码"
            />

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type_="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                  checked={rememberMe}
                  onChange={e => {
                    let target = e->ReactEvent.Form.target
                    let checked = target["checked"]
                    setRememberMe(_ => checked)
                  }}
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-text-secondary">
                  {React.string("记住我")}
                </label>
              </div>

              <div className="text-sm">
                <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
                  {React.string("忘记密码？")}
                </a>
              </div>
            </div>
          </div>

          <div>
            <button
              type_="submit"
              disabled={isLoading}
              className="w-full inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white border-transparent focus:ring-primary-500 px-4 py-2 text-sm">
              {React.string(isLoading ? "登录中..." : "登录")}
            </button>
          </div>
        </form>

        <div className="text-center">
          <p className="text-xs text-text-secondary">
            {React.string("Powered by Pay Project Inc.")}
          </p>
        </div>
      </div>
    </div>
  }
}

// 侧边栏导航组件
module AdminSidebar = {
  @react.component
  let make = (~user: AuthTypes.userInfo) => {
    let url = RescriptReactRouter.useUrl()
    let currentPath = url.path

    let isActive = (path: list<string>) => {
      switch (currentPath, path) {
      | (list{"admin"}, list{"admin"}) => true
      | (list{"admin", section, ..._}, list{"admin", targetSection}) => section === targetSection
      | _ => false
      }
    }

    let navItemClass = (path: list<string>) => {
      let baseClass = "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
      let activeClass = "bg-primary-100 text-primary-700 border-r-2 border-primary-500"
      let inactiveClass = "text-text-secondary hover:bg-secondary-50 hover:text-text-primary"

      if isActive(path) {
        `${baseClass} ${activeClass}`
      } else {
        `${baseClass} ${inactiveClass}`
      }
    }

    <div className="bg-surface w-64 shadow-lg h-full flex flex-col">
      // 顶部logo区域
      <div className="p-6 border-b border-border">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
            <i className="fas fa-shield-alt text-white text-sm" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-text-primary">
              {React.string("Pay Project")}
            </h2>
            <p className="text-xs text-text-secondary">
              {React.string("管理后台")}
            </p>
          </div>
        </div>
      </div>

      // 导航菜单
      <nav className="flex-1 px-4 py-6 space-y-2">
        <a
          href="/admin"
          className={navItemClass(list{"admin"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin")
          }}>
          <i className="fas fa-tachometer-alt mr-3" />
          {React.string("仪表板")}
        </a>

        <a
          href="/admin/organizations"
          className={navItemClass(list{"admin", "organizations"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin/organizations")
          }}>
          <i className="fas fa-building mr-3" />
          {React.string("组织管理")}
        </a>

        <a
          href="/admin/merchants"
          className={navItemClass(list{"admin", "merchants"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin/merchants")
          }}>
          <i className="fas fa-store mr-3" />
          {React.string("租户管理")}
        </a>

        <a
          href="/admin/users"
          className={navItemClass(list{"admin", "users"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin/users")
          }}>
          <i className="fas fa-users mr-3" />
          {React.string("用户管理")}
        </a>

        <a
          href="/admin/analytics"
          className={navItemClass(list{"admin", "analytics"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin/analytics")
          }}>
          <i className="fas fa-chart-bar mr-3" />
          {React.string("数据分析")}
        </a>

        {/* 支付管理菜单 */}
        <div className="mb-2">
          <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            {React.string("支付管理")}
          </div>

          <a
            href="/admin/payments"
            className={navItemClass(list{"admin", "payments"})}
            onClick={e => {
              e->ReactEvent.Mouse.preventDefault
              RescriptReactRouter.push("/admin/payments")
            }}>
            <i className="fas fa-credit-card mr-3" />
            {React.string("支付记录")}
          </a>

          <a
            href="/admin/connectors"
            className={navItemClass(list{"admin", "connectors"})}
            onClick={e => {
              e->ReactEvent.Mouse.preventDefault
              RescriptReactRouter.push("/admin/connectors")
            }}>
            <i className="fas fa-plug mr-3" />
            {React.string("连接器管理")}
          </a>

          <a
            href="/admin/payment-methods"
            className={navItemClass(list{"admin", "payment-methods"})}
            onClick={e => {
              e->ReactEvent.Mouse.preventDefault
              RescriptReactRouter.push("/admin/payment-methods")
            }}>
            <i className="fas fa-wallet mr-3" />
            {React.string("支付方法")}
          </a>
        </div>

        <a
          href="/admin/settings"
          className={navItemClass(list{"admin", "settings"})}
          onClick={e => {
            e->ReactEvent.Mouse.preventDefault
            RescriptReactRouter.push("/admin/settings")
          }}>
          <i className="fas fa-cog mr-3" />
          {React.string("系统设置")}
        </a>
      </nav>

      // 底部用户信息
      <div className="p-4 border-t border-border">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
            <i className="fas fa-user text-primary-600 text-sm" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-text-primary truncate">
              {React.string(user.name)}
            </p>
            <p className="text-xs text-text-secondary truncate">
              {React.string("超级管理员")}
            </p>
          </div>
        </div>
      </div>
    </div>
  }
}

module AdminNavbar = {
  @react.component
  let make = (~user: AuthTypes.userInfo) => {
    let {logout} = AuthContext.useAuth()
    let url = RescriptReactRouter.useUrl()

    let getPageTitle = () => {
      switch url.path {
      | list{"admin"} => "仪表板"
      | list{"admin", "organizations", ..._} => "组织管理"
      | list{"admin", "merchants", ..._} => "租户管理"
      | list{"admin", "users", ..._} => "用户管理"
      | list{"admin", "analytics", ..._} => "数据分析"
      | list{"admin", "payments", ..._} => "支付记录"
      | list{"admin", "connectors", ..._} => "连接器管理"
      | list{"admin", "payment-methods", ..._} => "支付方法"
      | list{"admin", "settings", ..._} => "系统设置"
      | _ => "Pay Project Admin"
      }
    }

    <nav className="bg-surface shadow-sm border-b border-border">
      <div className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-semibold text-text-primary">
              {React.string(getPageTitle())}
            </h1>
            <p className="text-sm text-text-secondary mt-1">
              {React.string("管理和监控您的支付系统")}
            </p>
          </div>

          <div className="flex items-center space-x-4">
            // 通知按钮
            <button className="p-2 text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg transition-colors">
              <i className="fas fa-bell" />
            </button>

            // 用户菜单
            <div className="relative">
              <button className="flex items-center space-x-2 p-2 text-text-secondary hover:text-text-primary hover:bg-secondary-50 rounded-lg transition-colors">
                <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <i className="fas fa-user text-primary-600 text-xs" />
                </div>
                <span className="text-sm font-medium">
                  {React.string(user.name)}
                </span>
                <i className="fas fa-chevron-down text-xs" />
              </button>

              // 这里可以添加下拉菜单
            </div>

            // 退出按钮
            <button
              onClick={_ => logout()}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-error-600 hover:text-error-700 hover:bg-error-50 rounded-lg transition-colors">
              <i className="fas fa-sign-out-alt" />
              <span>{React.string("退出")}</span>
            </button>
          </div>
        </div>
      </div>
    </nav>
  }
}

module DashboardHome = {
  @react.component
  let make = () => {
    <div className="space-y-6">
      // 欢迎区域
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">
          {React.string("欢迎使用 Pay Project 管理后台")}
        </h2>
        <p className="text-primary-100">
          {React.string("管理您的支付系统，监控业务数据，配置系统设置")}
        </p>
      </div>

      // 统计卡片
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <i className="fas fa-building text-xl" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">
                {React.string("总组织数")}
              </p>
              <p className="text-2xl font-bold text-text-primary">
                {React.string("12")}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <i className="fas fa-store text-xl" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">
                {React.string("活跃租户")}
              </p>
              <p className="text-2xl font-bold text-text-primary">
                {React.string("48")}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <i className="fas fa-users text-xl" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">
                {React.string("总用户数")}
              </p>
              <p className="text-2xl font-bold text-text-primary">
                {React.string("1,234")}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-orange-100 text-orange-600">
              <i className="fas fa-credit-card text-xl" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">
                {React.string("今日交易")}
              </p>
              <p className="text-2xl font-bold text-text-primary">
                {React.string("5,678")}
              </p>
            </div>
          </div>
        </div>
      </div>

      // 快速操作
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            {React.string("快速操作")}
          </h3>
          <div className="space-y-3">
            <button
              onClick={_ => RescriptReactRouter.push("/admin/organizations")}
              className="w-full flex items-center justify-between p-3 text-left hover:bg-secondary-50 rounded-lg transition-colors">
              <div className="flex items-center">
                <i className="fas fa-plus-circle text-primary-600 mr-3" />
                <span className="font-medium text-text-primary">
                  {React.string("创建新组织")}
                </span>
              </div>
              <i className="fas fa-chevron-right text-text-secondary" />
            </button>

            <button
              onClick={_ => RescriptReactRouter.push("/admin/merchants")}
              className="w-full flex items-center justify-between p-3 text-left hover:bg-secondary-50 rounded-lg transition-colors">
              <div className="flex items-center">
                <i className="fas fa-store text-primary-600 mr-3" />
                <span className="font-medium text-text-primary">
                  {React.string("管理租户")}
                </span>
              </div>
              <i className="fas fa-chevron-right text-text-secondary" />
            </button>

            <button
              onClick={_ => RescriptReactRouter.push("/admin/users")}
              className="w-full flex items-center justify-between p-3 text-left hover:bg-secondary-50 rounded-lg transition-colors">
              <div className="flex items-center">
                <i className="fas fa-user-plus text-primary-600 mr-3" />
                <span className="font-medium text-text-primary">
                  {React.string("添加用户")}
                </span>
              </div>
              <i className="fas fa-chevron-right text-text-secondary" />
            </button>
          </div>
        </div>

        <div className="bg-surface rounded-lg p-6 shadow-sm border border-border">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            {React.string("系统状态")}
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">
                {React.string("系统运行状态")}
              </span>
              <span className="flex items-center text-sm text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                {React.string("正常")}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">
                {React.string("数据库连接")}
              </span>
              <span className="flex items-center text-sm text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                {React.string("正常")}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">
                {React.string("支付网关")}
              </span>
              <span className="flex items-center text-sm text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                {React.string("正常")}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  }
}

module OrganizationRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "organizations"} => <OrganizationList />
    | _ => <div> {React.string("组织管理页面")} </div>
    }
  }
}

module MerchantRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "merchants"} => <MerchantList />
    | _ => <div> {React.string("租户管理页面")} </div>
    }
  }
}

module UserRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "users"} => <UserList />
    | _ => <div> {React.string("用户管理页面")} </div>
    }
  }
}

module AnalyticsRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "analytics"} => <Analytics />
    | _ => <div> {React.string("数据分析页面")} </div>
    }
  }
}

module SettingsRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "settings"} => <Settings />
    | _ => <div> {React.string("系统设置页面")} </div>
    }
  }
}

module PaymentRoutes = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()

    switch url.path {
    | list{"admin", "payments"} => <PaymentManagement />
    | list{"admin", "connectors"} => <ConnectorManagement />
    | list{"admin", "payment-methods"} => <PaymentMethodManagement />
    | _ => <div> {React.string("支付管理页面")} </div>
    }
  }
}

module NotFoundPage = {
  @react.component
  let make = () => {
    <div className="text-center py-12">
      <h2 className="text-2xl font-bold text-text-primary mb-4">
        {React.string("页面未找到")}
      </h2>
      <p className="text-text-secondary">
        {React.string("您访问的页面不存在")}
      </p>
    </div>
  }
}

// 仪表板布局组件
module DashboardLayout = {
  @react.component
  let make = (~user: AuthTypes.userInfo) => {
    let url = RescriptReactRouter.useUrl()

    <div className="min-h-screen bg-background">
      <div className="flex h-screen">
        // 侧边栏
        <AdminSidebar user />

        // 主内容区域
        <div className="flex-1 flex flex-col overflow-hidden">
          // 顶部导航栏
          <AdminNavbar user />

          // 主内容
          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-background">
            <div className="container mx-auto px-6 py-8">
              {switch url.path {
              | list{"admin"} | list{} => <DashboardHome />
              | list{"admin", "organizations", ..._} => <OrganizationRoutes />
              | list{"admin", "merchants", ..._} => <MerchantRoutes />
              | list{"admin", "users", ..._} => <UserRoutes />
              | list{"admin", "analytics", ..._} => <AnalyticsRoutes />
              | list{"admin", "payments", ..._}
              | list{"admin", "connectors", ..._}
              | list{"admin", "payment-methods", ..._} => <PaymentRoutes />
              | list{"admin", "settings", ..._} => <SettingsRoutes />
              | _ => <NotFoundPage />
              }}
            </div>
          </main>
        </div>
      </div>
    </div>
  }
}

// 主应用组件
module AdminApp = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()
    let {authState} = AuthContext.useAuth()

    // 路由处理
    switch (authState, url.path) {
    | (NotAuthenticated | AuthError(_), _) => <LoginPage />
    | (Authenticating, _) =>
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4" />
          <p className="text-text-secondary">
            {React.string("正在验证身份...")}
          </p>
        </div>
      </div>
    | (Authenticated(_), list{"admin", "login"}) => {
        // 已登录用户访问登录页，重定向到仪表板
        React.useEffect(() => {
          RescriptReactRouter.replace("/admin")
          None
        }, [])
        React.null
      }
    | (Authenticated(user), _) => <DashboardLayout user />
    }
  }
}

// 主入口组件
module AdminEntryComponent = {
  @react.component
  let make = () => {
    let url = RescriptReactRouter.useUrl()
    let (screenState, setScreenState) = React.useState(_ => PageLoaderWrapper.Loading)
    
    // 配置应用环境
    let configureApp = () => {
      try {
        setScreenState(_ => PageLoaderWrapper.Success)
      } catch {
      | _ => setScreenState(_ => PageLoaderWrapper.Error("Failed to configure app"))
      }
    }
    
    React.useEffect(() => {
      configureApp()
      None
    }, [])
    
    // 设置页面标题
    let setPageTitle = (pageTitle: string) => {
      let title = `${pageTitle} - Pay Project Admin`
      Console.log(`Setting page title: ${title}`)
    }
    
    React.useEffect(() => {
      switch url.path {
      | list{"admin", "login"} => setPageTitle("登录")
      | list{"admin", "organizations", ..._} => setPageTitle("组织管理")
      | list{"admin", "merchants", ..._} => setPageTitle("租户管理")
      | list{"admin", "users", ..._} => setPageTitle("用户管理")
      | list{"admin", "analytics", ..._} => setPageTitle("数据分析")
      | list{"admin", "payments", ..._} => setPageTitle("支付记录")
      | list{"admin", "connectors", ..._} => setPageTitle("连接器管理")
      | list{"admin", "payment-methods", ..._} => setPageTitle("支付方法")
      | list{"admin", "settings", ..._} => setPageTitle("系统设置")
      | list{"admin"} | list{} => setPageTitle("仪表板")
      | _ => setPageTitle("Pay Project Admin")
      }
      None
    }, [url.path])
    
    <PageLoaderWrapper
      state={screenState}>
      <div className="min-h-screen bg-background">
        <AuthContext.make>
          <AdminApp />
        </AuthContext.make>
      </div>
    </PageLoaderWrapper>
  }
}






// 渲染应用 - 注意：使用旧的ReactDOM.render API，因为ReScript绑定尚未支持React 18的createRoot
switch ReactDOM.querySelector("#admin-root") {
| Some(rootElement) => {
    ReactDOM.render(<AdminEntryComponent />, rootElement)
  }
| None => Console.error("Root element not found")
}
