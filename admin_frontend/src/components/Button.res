// 按钮组件

// 按钮类型
type buttonType = Primary | Secondary | Success | Warning | Error | Ghost | Link

// 按钮大小
type buttonSize = Small | Medium | Large

// 按钮状态
type buttonState = Normal | Loading | Disabled

// 按钮属性
type buttonProps = {
  text: string,
  onClick: option<ReactEvent.Mouse.t => unit>,
  buttonType: buttonType,
  buttonSize: buttonSize,
  buttonState: buttonState,
  leftIcon: option<string>,
  rightIcon: option<string>,
  fullWidth: bool,
  className: option<string>,
  id: option<string>,
  testId: option<string>,
  form: option<string>,
  buttonHtmlType: option<string>, // "button" | "submit" | "reset"
}

// 默认属性
let defaultProps: buttonProps = {
  text: "",
  onClick: None,
  buttonType: Primary,
  buttonSize: Medium,
  buttonState: Normal,
  leftIcon: None,
  rightIcon: None,
  fullWidth: false,
  className: None,
  id: None,
  testId: None,
  form: None,
  buttonHtmlType: Some("button"),
}

// 获取按钮类型样式
let getButtonTypeClass = buttonType => {
  switch buttonType {
  | Primary => "bg-primary-600 hover:bg-primary-700 text-white border-transparent focus:ring-primary-500"
  | Secondary => "bg-secondary-100 hover:bg-secondary-200 text-secondary-900 border-secondary-300 focus:ring-secondary-500"
  | Success => "bg-success-600 hover:bg-success-700 text-white border-transparent focus:ring-success-500"
  | Warning => "bg-warning-600 hover:warning-700 text-white border-transparent focus:ring-warning-500"
  | Error => "bg-error-600 hover:bg-error-700 text-white border-transparent focus:ring-error-500"
  | Ghost => "bg-transparent hover:bg-secondary-50 text-secondary-700 border-secondary-300 focus:ring-secondary-500"
  | Link => "bg-transparent hover:bg-transparent text-primary-600 hover:text-primary-700 border-transparent focus:ring-primary-500 p-0"
  }
}

// 获取按钮大小样式
let getButtonSizeClass = buttonSize => {
  switch buttonSize {
  | Small => "px-3 py-1.5 text-sm"
  | Medium => "px-4 py-2 text-sm"
  | Large => "px-6 py-3 text-base"
  }
}

// 获取按钮状态样式
let getButtonStateClass = buttonState => {
  switch buttonState {
  | Normal => ""
  | Loading => "cursor-wait opacity-75"
  | Disabled => "cursor-not-allowed opacity-50"
  }
}

// 基础按钮样式
let baseButtonClass = "inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"

// 加载图标组件
module LoadingIcon = {
  @react.component
  let make = (~size: string="w-4 h-4") => {
    <svg
      className={`animate-spin ${size}`}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24">
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  }
}

// 图标组件
module Icon = {
  @react.component
  let make = (~name: string, ~size: string="w-4 h-4", ~className: string="") => {
    <i className={`${name} ${size} ${className}`} />
  }
}

// 主按钮组件
@react.component
let make = (
  ~text: string,
  ~onClick: option<ReactEvent.Mouse.t => unit>=?,
  ~buttonType: buttonType=Primary,
  ~buttonSize: buttonSize=Medium,
  ~buttonState: buttonState=Normal,
  ~leftIcon: option<string>=?,
  ~rightIcon: option<string>=?,
  ~fullWidth: bool=false,
  ~className: option<string>=?,
  ~id: option<string>=?,
  ~testId: option<string>=?,
  ~form: option<string>=?,
  ~buttonHtmlType: option<string>=?,
) => {
  let _ = testId // Suppress unused warning
  let isDisabled = buttonState === Disabled || buttonState === Loading
  
  let buttonClasses = [
    baseButtonClass,
    getButtonTypeClass(buttonType),
    getButtonSizeClass(buttonSize),
    getButtonStateClass(buttonState),
    fullWidth ? "w-full" : "",
    className->Option.getOr(""),
  ]->Array.filter(cls => cls !== "")->Array.joinWith(" ")

  let handleClick = React.useCallback((event: ReactEvent.Mouse.t) => {
    if !isDisabled {
      switch onClick {
      | Some(handler) => handler(event)
      | None => ()
      }
    } else {
      event->ReactEvent.Mouse.preventDefault
    }
  }, [])

  <button
    className={buttonClasses}
    onClick={handleClick}
    disabled={isDisabled}
    ?id
    ?form
    type_={buttonHtmlType->Option.getOr("button")}
    >
    {switch (leftIcon, buttonState) {
    | (Some(icon), Normal) => <Icon name={icon} className="mr-2" />
    | (_, Loading) => <LoadingIcon size="w-4 h-4" />
    | _ => React.null
    }}
    
    {buttonState === Loading ? React.null : React.string(text)}
    
    {switch (rightIcon, buttonState) {
    | (Some(icon), Normal) => <Icon name={icon} className="ml-2" />
    | _ => React.null
    }}
  </button>
}

// 便捷的按钮变体
module Primary = {
  @react.component
  let make = (~text: string, ~onClick: option<ReactEvent.Mouse.t => unit>=?, ~className: option<string>=?) => {
    <button
      className={`inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white border-transparent focus:ring-primary-500 px-4 py-2 text-sm ${className->Option.getOr("")}`}
      onClick={e => {
        switch onClick {
        | Some(handler) => handler(e)
        | None => ()
        }
      }}>
      {React.string(text)}
    </button>
  }
}

module Secondary = {
  @react.component
  let make = (~text: string, ~onClick: option<ReactEvent.Mouse.t => unit>=?, ~className: option<string>=?) => {
    <button
      className={`inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-secondary-100 hover:bg-secondary-200 text-secondary-900 border-secondary-300 focus:ring-secondary-500 px-4 py-2 text-sm ${className->Option.getOr("")}`}
      onClick={e => {
        switch onClick {
        | Some(handler) => handler(e)
        | None => ()
        }
      }}>
      {React.string(text)}
    </button>
  }
}

module Ghost = {
  @react.component
  let make = (~text: string, ~onClick: option<ReactEvent.Mouse.t => unit>=?, ~className: option<string>=?) => {
    <button
      className={`inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-transparent hover:bg-secondary-50 text-secondary-700 border-secondary-300 focus:ring-secondary-500 px-4 py-2 text-sm ${className->Option.getOr("")}`}
      onClick={e => {
        switch onClick {
        | Some(handler) => handler(e)
        | None => ()
        }
      }}>
      {React.string(text)}
    </button>
  }
}
