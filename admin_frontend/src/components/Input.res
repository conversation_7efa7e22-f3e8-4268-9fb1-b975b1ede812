// 输入框组件

// 输入框类型
type inputType = Text | Email | Password | Number | Tel | Url | Search

// 输入框大小
type inputSize = Small | Medium | Large

// 输入框状态
type inputState = Normal | Error | Success | Disabled

// 输入框属性
type inputProps = {
  value: string,
  onChange: string => unit,
  placeholder: option<string>,
  inputType: inputType,
  inputSize: inputSize,
  inputState: inputState,
  label: option<string>,
  helperText: option<string>,
  errorText: option<string>,
  leftIcon: option<string>,
  rightIcon: option<string>,
  required: bool,
  disabled: bool,
  readOnly: bool,
  autoFocus: bool,
  maxLength: option<int>,
  minLength: option<int>,
  pattern: option<string>,
  className: option<string>,
  id: option<string>,
  name: option<string>,
  testId: option<string>,
}

// 获取输入框类型字符串
let getInputTypeString = inputType => {
  switch inputType {
  | Text => "text"
  | Email => "email"
  | Password => "password"
  | Number => "number"
  | Tel => "tel"
  | Url => "url"
  | Search => "search"
  }
}

// 获取输入框大小样式
let getInputSizeClass = inputSize => {
  switch inputSize {
  | Small => "px-3 py-1.5 text-sm"
  | Medium => "px-3 py-2 text-sm"
  | Large => "px-4 py-3 text-base"
  }
}

// 获取输入框状态样式
let getInputStateClass = inputState => {
  switch inputState {
  | Normal => "border-secondary-300 focus:border-primary-500 focus:ring-primary-500"
  | Error => "border-error-300 focus:border-error-500 focus:ring-error-500"
  | Success => "border-success-300 focus:border-success-500 focus:ring-success-500"
  | Disabled => "border-secondary-200 bg-secondary-50 text-secondary-500 cursor-not-allowed"
  }
}

// 基础输入框样式
let baseInputClass = "block w-full rounded-md border transition-colors duration-200 focus:outline-none focus:ring-1 placeholder-secondary-400"

// 标签组件
module Label = {
  @react.component
  let make = (~text: string, ~required: bool=false, ~htmlFor: option<string>=?, ~className: string="") => {
    <label
      className={`block text-sm font-medium text-secondary-700 mb-1 ${className}`}
      ?htmlFor>
      {React.string(text)}
      {required ? <span className="text-error-500 ml-1"> {React.string("*")} </span> : React.null}
    </label>
  }
}

// 帮助文本组件
module HelperText = {
  @react.component
  let make = (~text: string, ~className: string="") => {
    <p className={`mt-1 text-xs text-secondary-500 ${className}`}>
      {React.string(text)}
    </p>
  }
}

// 错误文本组件
module ErrorText = {
  @react.component
  let make = (~text: string, ~className: string="") => {
    <p className={`mt-1 text-xs text-error-600 ${className}`}>
      {React.string(text)}
    </p>
  }
}

// 图标组件
module Icon = {
  @react.component
  let make = (~name: string, ~position: [#left | #right], ~className: string="") => {
    let positionClass = switch position {
    | #left => "left-3"
    | #right => "right-3"
    }
    
    <div className={`absolute inset-y-0 ${positionClass} flex items-center pointer-events-none`}>
      <i className={`${name} w-4 h-4 text-secondary-400 ${className}`} />
    </div>
  }
}

// 主输入框组件
@react.component
let make = (
  ~value: string,
  ~onChange: string => unit,
  ~placeholder: option<string>=?,
  ~inputType: inputType=Text,
  ~inputSize: inputSize=Medium,
  ~inputState: inputState=Normal,
  ~label: option<string>=?,
  ~helperText: option<string>=?,
  ~errorText: option<string>=?,
  ~leftIcon: option<string>=?,
  ~rightIcon: option<string>=?,
  ~required: bool=false,
  ~disabled: bool=false,
  ~readOnly: bool=false,
  ~autoFocus: bool=false,
  ~maxLength: option<int>=?,
  ~minLength: option<int>=?,
  ~pattern: option<string>=?,
  ~className: option<string>=?,
  ~id: option<string>=?,
  ~name: option<string>=?,
  ~testId: option<string>=?,
) => {
  let _ = testId // Suppress unused warning
  let inputId = id->Option.getOr(`input-${Date.now()->Float.toString}`)
  let isDisabled = disabled || inputState === Disabled
  let hasError = errorText->Option.isSome || inputState === Error
  let currentInputState = hasError ? Error : inputState
  
  let inputClasses = [
    baseInputClass,
    getInputSizeClass(inputSize),
    getInputStateClass(currentInputState),
    leftIcon->Option.isSome ? "pl-10" : "",
    rightIcon->Option.isSome ? "pr-10" : "",
    className->Option.getOr(""),
  ]->Array.filter(cls => cls !== "")->Array.joinWith(" ")

  let handleChange = React.useCallback((event: ReactEvent.Form.t) => {
    let target = event->ReactEvent.Form.target
    let value = target["value"]
    onChange(value)
  }, [onChange])

  <div className="w-full">
    {switch label {
    | Some(labelText) => <Label text={labelText} required htmlFor={inputId} />
    | None => React.null
    }}
    
    <div className="relative">
      {switch leftIcon {
      | Some(icon) => <Icon name={icon} position=#left />
      | None => React.null
      }}
      
      <input
        id={inputId}
        className={inputClasses}
        type_={getInputTypeString(inputType)}
        value
        onChange={handleChange}
        ?placeholder
        disabled={isDisabled}
        readOnly
        autoFocus
        ?maxLength
        ?minLength
        ?pattern
        ?name
        required

      />
      
      {switch rightIcon {
      | Some(icon) => <Icon name={icon} position=#right />
      | None => React.null
      }}
    </div>
    
    {switch (errorText, helperText) {
    | (Some(error), _) => <ErrorText text={error} />
    | (None, Some(helper)) => <HelperText text={helper} />
    | (None, None) => React.null
    }}
  </div>
}

// 便捷的输入框变体
module Text = {
  @react.component
  let make = (
    ~value: string,
    ~onChange: string => unit,
    ~placeholder: option<string>=?,
    ~label: option<string>=?,
    ~className: option<string>=?,
  ) => {
    let _ = label // Suppress unused warning
    <input
      className={`block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 ${className->Option.getOr("")}`}
      type_="text"
      value
      onChange={e => {
        let target = e->ReactEvent.Form.target
        let value = target["value"]
        onChange(value)
      }}
      ?placeholder
    />
  }
}

module Email = {
  @react.component
  let make = (
    ~value: string,
    ~onChange: string => unit,
    ~placeholder: option<string>=?,
    ~label: option<string>=?,
    ~className: option<string>=?,
  ) => {
    let _ = label // Suppress unused warning
    <input
      className={`block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 ${className->Option.getOr("")}`}
      type_="email"
      value
      onChange={e => {
        let target = e->ReactEvent.Form.target
        let value = target["value"]
        onChange(value)
      }}
      ?placeholder
    />
  }
}

module Password = {
  @react.component
  let make = (
    ~value: string,
    ~onChange: string => unit,
    ~placeholder: option<string>=?,
    ~label: option<string>=?,
    ~className: option<string>=?,
  ) => {
    let _ = label // Suppress unused warning
    <input
      className={`block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 ${className->Option.getOr("")}`}
      type_="password"
      value
      onChange={e => {
        let target = e->ReactEvent.Form.target
        let value = target["value"]
        onChange(value)
      }}
      ?placeholder
    />
  }
}

module Search = {
  @react.component
  let make = (
    ~value: string,
    ~onChange: string => unit,
    ~placeholder: option<string>=?,
    ~label: option<string>=?,
    ~className: option<string>=?,
  ) => {
    let _ = label // Suppress unused warning
    <input
      className={`block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 ${className->Option.getOr("")}`}
      type_="search"
      value
      onChange={e => {
        let target = e->ReactEvent.Form.target
        let value = target["value"]
        onChange(value)
      }}
      ?placeholder
    />
  }
}
