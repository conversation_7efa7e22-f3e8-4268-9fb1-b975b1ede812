// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";

function getButtonTypeClass(buttonType) {
  switch (buttonType) {
    case "Primary" :
        return "bg-primary-600 hover:bg-primary-700 text-white border-transparent focus:ring-primary-500";
    case "Secondary" :
        return "bg-secondary-100 hover:bg-secondary-200 text-secondary-900 border-secondary-300 focus:ring-secondary-500";
    case "Success" :
        return "bg-success-600 hover:bg-success-700 text-white border-transparent focus:ring-success-500";
    case "Warning" :
        return "bg-warning-600 hover:warning-700 text-white border-transparent focus:ring-warning-500";
    case "Error" :
        return "bg-error-600 hover:bg-error-700 text-white border-transparent focus:ring-error-500";
    case "Ghost" :
        return "bg-transparent hover:bg-secondary-50 text-secondary-700 border-secondary-300 focus:ring-secondary-500";
    case "Link" :
        return "bg-transparent hover:bg-transparent text-primary-600 hover:text-primary-700 border-transparent focus:ring-primary-500 p-0";
    
  }
}

function getButtonSizeClass(buttonSize) {
  switch (buttonSize) {
    case "Small" :
        return "px-3 py-1.5 text-sm";
    case "Medium" :
        return "px-4 py-2 text-sm";
    case "Large" :
        return "px-6 py-3 text-base";
    
  }
}

function getButtonStateClass(buttonState) {
  switch (buttonState) {
    case "Normal" :
        return "";
    case "Loading" :
        return "cursor-wait opacity-75";
    case "Disabled" :
        return "cursor-not-allowed opacity-50";
    
  }
}

var baseButtonClass = "inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

function Button$LoadingIcon(props) {
  var __size = props.size;
  var size = __size !== undefined ? __size : "w-4 h-4";
  return React.createElement("svg", {
              className: "animate-spin " + size,
              fill: "none",
              viewBox: "0 0 24 24",
              xmlns: "http://www.w3.org/2000/svg"
            }, React.createElement("circle", {
                  className: "opacity-25",
                  cx: "12",
                  cy: "12",
                  r: "10",
                  stroke: "currentColor",
                  strokeWidth: "4"
                }), React.createElement("path", {
                  className: "opacity-75",
                  d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",
                  fill: "currentColor"
                }));
}

var LoadingIcon = {
  make: Button$LoadingIcon
};

function Button$Icon(props) {
  var __className = props.className;
  var __size = props.size;
  var size = __size !== undefined ? __size : "w-4 h-4";
  var className = __className !== undefined ? __className : "";
  return React.createElement("i", {
              className: props.name + " " + size + " " + className
            });
}

var Icon = {
  make: Button$Icon
};

function Button(props) {
  var __fullWidth = props.fullWidth;
  var rightIcon = props.rightIcon;
  var leftIcon = props.leftIcon;
  var __buttonState = props.buttonState;
  var __buttonSize = props.buttonSize;
  var __buttonType = props.buttonType;
  var onClick = props.onClick;
  var buttonType = __buttonType !== undefined ? __buttonType : "Primary";
  var buttonSize = __buttonSize !== undefined ? __buttonSize : "Medium";
  var buttonState = __buttonState !== undefined ? __buttonState : "Normal";
  var fullWidth = __fullWidth !== undefined ? __fullWidth : false;
  var isDisabled = buttonState === "Disabled" || buttonState === "Loading";
  var buttonClasses = [
        baseButtonClass,
        getButtonTypeClass(buttonType),
        getButtonSizeClass(buttonSize),
        getButtonStateClass(buttonState),
        fullWidth ? "w-full" : "",
        Core__Option.getOr(props.className, "")
      ].filter(function (cls) {
          return cls !== "";
        }).join(" ");
  var handleClick = React.useCallback((function ($$event) {
          if (isDisabled) {
            $$event.preventDefault();
            return ;
          } else if (onClick !== undefined) {
            return onClick($$event);
          } else {
            return ;
          }
        }), []);
  var tmp;
  var exit = 0;
  if (leftIcon !== undefined) {
    switch (buttonState) {
      case "Normal" :
          tmp = React.createElement(Button$Icon, {
                name: leftIcon,
                className: "mr-2"
              });
          break;
      case "Loading" :
          exit = 1;
          break;
      case "Disabled" :
          tmp = null;
          break;
      
    }
  } else {
    exit = 1;
  }
  if (exit === 1) {
    switch (buttonState) {
      case "Loading" :
          tmp = React.createElement(Button$LoadingIcon, {
                size: "w-4 h-4"
              });
          break;
      case "Normal" :
      case "Disabled" :
          tmp = null;
          break;
      
    }
  }
  var tmp$1;
  if (rightIcon !== undefined) {
    switch (buttonState) {
      case "Normal" :
          tmp$1 = React.createElement(Button$Icon, {
                name: rightIcon,
                className: "ml-2"
              });
          break;
      case "Loading" :
      case "Disabled" :
          tmp$1 = null;
          break;
      
    }
  } else {
    tmp$1 = null;
  }
  return React.createElement("button", {
              className: buttonClasses,
              id: props.id,
              disabled: isDisabled,
              form: props.form,
              type: Core__Option.getOr(props.buttonHtmlType, "button"),
              onClick: handleClick
            }, tmp, buttonState === "Loading" ? null : props.text, tmp$1);
}

function Button$Primary(props) {
  var onClick = props.onClick;
  return React.createElement("button", {
              className: "inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 hover:bg-primary-700 text-white border-transparent focus:ring-primary-500 px-4 py-2 text-sm " + Core__Option.getOr(props.className, ""),
              onClick: (function (e) {
                  if (onClick !== undefined) {
                    return onClick(e);
                  }
                  
                })
            }, props.text);
}

var Primary = {
  make: Button$Primary
};

function Button$Secondary(props) {
  var onClick = props.onClick;
  return React.createElement("button", {
              className: "inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-secondary-100 hover:bg-secondary-200 text-secondary-900 border-secondary-300 focus:ring-secondary-500 px-4 py-2 text-sm " + Core__Option.getOr(props.className, ""),
              onClick: (function (e) {
                  if (onClick !== undefined) {
                    return onClick(e);
                  }
                  
                })
            }, props.text);
}

var Secondary = {
  make: Button$Secondary
};

function Button$Ghost(props) {
  var onClick = props.onClick;
  return React.createElement("button", {
              className: "inline-flex items-center justify-center font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-transparent hover:bg-secondary-50 text-secondary-700 border-secondary-300 focus:ring-secondary-500 px-4 py-2 text-sm " + Core__Option.getOr(props.className, ""),
              onClick: (function (e) {
                  if (onClick !== undefined) {
                    return onClick(e);
                  }
                  
                })
            }, props.text);
}

var Ghost = {
  make: Button$Ghost
};

var defaultProps = {
  text: "",
  onClick: undefined,
  buttonType: "Primary",
  buttonSize: "Medium",
  buttonState: "Normal",
  leftIcon: undefined,
  rightIcon: undefined,
  fullWidth: false,
  className: undefined,
  id: undefined,
  testId: undefined,
  form: undefined,
  buttonHtmlType: "button"
};

var make = Button;

export {
  defaultProps ,
  getButtonTypeClass ,
  getButtonSizeClass ,
  getButtonStateClass ,
  baseButtonClass ,
  LoadingIcon ,
  Icon ,
  make ,
  Primary ,
  Secondary ,
  Ghost ,
}
/* react Not a pure module */
