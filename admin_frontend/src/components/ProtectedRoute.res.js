// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";
import * as <PERSON><PERSON> from "./Button.res.js";
import * as AuthTypes from "../types/AuthTypes.res.js";
import * as AuthContext from "../context/AuthContext.res.js";
import * as Caml_option from "rescript/lib/es6/caml_option.js";
import * as RescriptReactRouter from "@rescript/react/src/RescriptReactRouter.res.js";

function checkRouteAccess(user, protection) {
  if (user === undefined) {
    return false;
  }
  if (typeof protection !== "object") {
    return true;
  }
  switch (protection.TAG) {
    case "RequireRole" :
        return user.role === protection._0;
    case "RequirePermission" :
        return AuthTypes.hasPermission(user, protection._0);
    case "RequireAnyPermission" :
        return AuthTypes.hasAnyPermission(user, protection._0);
    case "RequireAllPermissions" :
        return AuthTypes.hasAllPermissions(user, protection._0);
    
  }
}

function ProtectedRoute$UnauthorizedPage(props) {
  var __showLoginButton = props.showLoginButton;
  var __message = props.message;
  var message = __message !== undefined ? __message : "您没有权限访问此页面";
  var showLoginButton = __showLoginButton !== undefined ? __showLoginButton : true;
  var match = AuthContext.useAuth();
  var logout = match.logout;
  var handleLogin = React.useCallback((function () {
          RescriptReactRouter.push("/admin/login");
        }), []);
  var handleLogout = React.useCallback((function () {
          logout();
          RescriptReactRouter.push("/admin/login");
        }), [logout]);
  return React.createElement("div", {
              className: "min-h-screen flex items-center justify-center bg-background"
            }, React.createElement("div", {
                  className: "max-w-md w-full bg-surface rounded-lg shadow-lg p-8 text-center"
                }, React.createElement("div", {
                      className: "mb-6"
                    }, React.createElement("div", {
                          className: "mx-auto w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mb-4"
                        }, React.createElement("i", {
                              className: "fas fa-lock text-error-600 text-2xl"
                            })), React.createElement("h1", {
                          className: "text-2xl font-bold text-text-primary mb-2"
                        }, "访问被拒绝"), React.createElement("p", {
                          className: "text-text-secondary"
                        }, message)), React.createElement("div", {
                      className: "space-y-3"
                    }, showLoginButton ? React.createElement(Button.Primary.make, {
                            text: "重新登录",
                            onClick: (function (param) {
                                handleLogin();
                              }),
                            className: "w-full"
                          }) : null, React.createElement(Button.Secondary.make, {
                          text: "返回首页",
                          onClick: (function (param) {
                              RescriptReactRouter.push("/admin");
                            }),
                          className: "w-full"
                        }), React.createElement(Button.Ghost.make, {
                          text: "退出登录",
                          onClick: (function (param) {
                              handleLogout();
                            }),
                          className: "w-full"
                        }))));
}

var UnauthorizedPage = {
  make: ProtectedRoute$UnauthorizedPage
};

function ProtectedRoute$LoadingPage(props) {
  return React.createElement("div", {
              className: "min-h-screen flex items-center justify-center bg-background"
            }, React.createElement("div", {
                  className: "text-center"
                }, React.createElement("div", {
                      className: "inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"
                    }), React.createElement("p", {
                      className: "text-text-secondary"
                    }, "正在验证权限...")));
}

var LoadingPage = {
  make: ProtectedRoute$LoadingPage
};

function ProtectedRoute(props) {
  var loadingComponent = props.loadingComponent;
  var fallback = props.fallback;
  var __protection = props.protection;
  var protection = __protection !== undefined ? __protection : "RequireAuth";
  var match = AuthContext.useAuth();
  var authState = match.authState;
  if (match.isLoading) {
    if (loadingComponent !== undefined) {
      return Caml_option.valFromOption(loadingComponent);
    } else {
      return React.createElement(ProtectedRoute$LoadingPage, {});
    }
  }
  if (typeof authState === "object") {
    if (authState.TAG === "Authenticated") {
      if (checkRouteAccess(authState._0, protection)) {
        return props.children;
      } else if (fallback !== undefined) {
        return Caml_option.valFromOption(fallback);
      } else {
        return React.createElement(ProtectedRoute$UnauthorizedPage, {});
      }
    } else if (fallback !== undefined) {
      return Caml_option.valFromOption(fallback);
    } else {
      return React.createElement(ProtectedRoute$UnauthorizedPage, {
                  message: authState._0,
                  showLoginButton: true
                });
    }
  }
  if (authState !== "NotAuthenticated") {
    if (loadingComponent !== undefined) {
      return Caml_option.valFromOption(loadingComponent);
    } else {
      return React.createElement(ProtectedRoute$LoadingPage, {});
    }
  }
  React.useEffect((function () {
          RescriptReactRouter.push("/admin/login");
        }), []);
  if (fallback !== undefined) {
    return Caml_option.valFromOption(fallback);
  } else {
    return React.createElement(ProtectedRoute$LoadingPage, {});
  }
}

function ProtectedRoute$RequireAuth(props) {
  return React.createElement("div", undefined, props.children);
}

var RequireAuth = {
  make: ProtectedRoute$RequireAuth
};

function ProtectedRoute$RequireRole(props) {
  return React.createElement("div", undefined, props.children);
}

var RequireRole = {
  make: ProtectedRoute$RequireRole
};

function ProtectedRoute$RequirePermission(props) {
  return React.createElement("div", undefined, props.children);
}

var RequirePermission = {
  make: ProtectedRoute$RequirePermission
};

function ProtectedRoute$RequireSuperAdmin(props) {
  return React.createElement("div", undefined, props.children);
}

var RequireSuperAdmin = {
  make: ProtectedRoute$RequireSuperAdmin
};

function ProtectedRoute$RequireAdmin(props) {
  return React.createElement("div", undefined, props.children);
}

var RequireAdmin = {
  make: ProtectedRoute$RequireAdmin
};

function usePermission(permission) {
  var match = AuthContext.useAuth();
  var authState = match.authState;
  return React.useMemo((function () {
                if (typeof authState !== "object" || authState.TAG !== "Authenticated") {
                  return false;
                } else {
                  return AuthTypes.hasPermission(authState._0, permission);
                }
              }), []);
}

function useRole(role) {
  var match = AuthContext.useAuth();
  var authState = match.authState;
  return React.useMemo((function () {
                if (typeof authState !== "object" || authState.TAG !== "Authenticated") {
                  return false;
                } else {
                  return authState._0.role === role;
                }
              }), []);
}

function useCurrentUser() {
  var match = AuthContext.useAuth();
  var authState = match.authState;
  return React.useMemo((function () {
                if (typeof authState !== "object" || authState.TAG !== "Authenticated") {
                  return ;
                } else {
                  return authState._0;
                }
              }), [authState]);
}

var make = ProtectedRoute;

export {
  checkRouteAccess ,
  UnauthorizedPage ,
  LoadingPage ,
  make ,
  RequireAuth ,
  RequireRole ,
  RequirePermission ,
  RequireSuperAdmin ,
  RequireAdmin ,
  usePermission ,
  useRole ,
  useCurrentUser ,
}
/* react Not a pure module */
