// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";

function getInputTypeString(inputType) {
  switch (inputType) {
    case "Text" :
        return "text";
    case "Email" :
        return "email";
    case "Password" :
        return "password";
    case "Number" :
        return "number";
    case "Tel" :
        return "tel";
    case "Url" :
        return "url";
    case "Search" :
        return "search";
    
  }
}

function getInputSizeClass(inputSize) {
  switch (inputSize) {
    case "Small" :
        return "px-3 py-1.5 text-sm";
    case "Medium" :
        return "px-3 py-2 text-sm";
    case "Large" :
        return "px-4 py-3 text-base";
    
  }
}

function getInputStateClass(inputState) {
  switch (inputState) {
    case "Normal" :
        return "border-secondary-300 focus:border-primary-500 focus:ring-primary-500";
    case "Error" :
        return "border-error-300 focus:border-error-500 focus:ring-error-500";
    case "Success" :
        return "border-success-300 focus:border-success-500 focus:ring-success-500";
    case "Disabled" :
        return "border-secondary-200 bg-secondary-50 text-secondary-500 cursor-not-allowed";
    
  }
}

var baseInputClass = "block w-full rounded-md border transition-colors duration-200 focus:outline-none focus:ring-1 placeholder-secondary-400";

function Input$Label(props) {
  var __className = props.className;
  var __required = props.required;
  var required = __required !== undefined ? __required : false;
  var className = __className !== undefined ? __className : "";
  return React.createElement("label", {
              className: "block text-sm font-medium text-secondary-700 mb-1 " + className,
              htmlFor: props.htmlFor
            }, props.text, required ? React.createElement("span", {
                    className: "text-error-500 ml-1"
                  }, "*") : null);
}

var Label = {
  make: Input$Label
};

function Input$HelperText(props) {
  var __className = props.className;
  var className = __className !== undefined ? __className : "";
  return React.createElement("p", {
              className: "mt-1 text-xs text-secondary-500 " + className
            }, props.text);
}

var HelperText = {
  make: Input$HelperText
};

function Input$ErrorText(props) {
  var __className = props.className;
  var className = __className !== undefined ? __className : "";
  return React.createElement("p", {
              className: "mt-1 text-xs text-error-600 " + className
            }, props.text);
}

var ErrorText = {
  make: Input$ErrorText
};

function Input$Icon(props) {
  var __className = props.className;
  var className = __className !== undefined ? __className : "";
  var positionClass = props.position === "right" ? "right-3" : "left-3";
  return React.createElement("div", {
              className: "absolute inset-y-0 " + positionClass + " flex items-center pointer-events-none"
            }, React.createElement("i", {
                  className: props.name + " w-4 h-4 text-secondary-400 " + className
                }));
}

var Icon = {
  make: Input$Icon
};

function Input(props) {
  var __autoFocus = props.autoFocus;
  var __readOnly = props.readOnly;
  var __disabled = props.disabled;
  var __required = props.required;
  var rightIcon = props.rightIcon;
  var leftIcon = props.leftIcon;
  var errorText = props.errorText;
  var helperText = props.helperText;
  var label = props.label;
  var __inputState = props.inputState;
  var __inputSize = props.inputSize;
  var __inputType = props.inputType;
  var onChange = props.onChange;
  var inputType = __inputType !== undefined ? __inputType : "Text";
  var inputSize = __inputSize !== undefined ? __inputSize : "Medium";
  var inputState = __inputState !== undefined ? __inputState : "Normal";
  var required = __required !== undefined ? __required : false;
  var disabled = __disabled !== undefined ? __disabled : false;
  var readOnly = __readOnly !== undefined ? __readOnly : false;
  var autoFocus = __autoFocus !== undefined ? __autoFocus : false;
  var inputId = Core__Option.getOr(props.id, "input-" + Date.now().toString());
  var isDisabled = disabled || inputState === "Disabled";
  var hasError = Core__Option.isSome(errorText) || inputState === "Error";
  var currentInputState = hasError ? "Error" : inputState;
  var inputClasses = [
        baseInputClass,
        getInputSizeClass(inputSize),
        getInputStateClass(currentInputState),
        Core__Option.isSome(leftIcon) ? "pl-10" : "",
        Core__Option.isSome(rightIcon) ? "pr-10" : "",
        Core__Option.getOr(props.className, "")
      ].filter(function (cls) {
          return cls !== "";
        }).join(" ");
  var handleChange = React.useCallback((function ($$event) {
          var target = $$event.target;
          var value = target.value;
          onChange(value);
        }), [onChange]);
  return React.createElement("div", {
              className: "w-full"
            }, label !== undefined ? React.createElement(Input$Label, {
                    text: label,
                    required: required,
                    htmlFor: inputId
                  }) : null, React.createElement("div", {
                  className: "relative"
                }, leftIcon !== undefined ? React.createElement(Input$Icon, {
                        name: leftIcon,
                        position: "left"
                      }) : null, React.createElement("input", {
                      className: inputClasses,
                      id: inputId,
                      autoFocus: autoFocus,
                      disabled: isDisabled,
                      maxLength: props.maxLength,
                      minLength: props.minLength,
                      name: props.name,
                      pattern: props.pattern,
                      placeholder: props.placeholder,
                      readOnly: readOnly,
                      required: required,
                      type: getInputTypeString(inputType),
                      value: props.value,
                      onChange: handleChange
                    }), rightIcon !== undefined ? React.createElement(Input$Icon, {
                        name: rightIcon,
                        position: "right"
                      }) : null), errorText !== undefined ? React.createElement(Input$ErrorText, {
                    text: errorText
                  }) : (
                helperText !== undefined ? React.createElement(Input$HelperText, {
                        text: helperText
                      }) : null
              ));
}

function Input$Text(props) {
  var onChange = props.onChange;
  return React.createElement("input", {
              className: "block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 " + Core__Option.getOr(props.className, ""),
              placeholder: props.placeholder,
              type: "text",
              value: props.value,
              onChange: (function (e) {
                  var target = e.target;
                  var value = target.value;
                  onChange(value);
                })
            });
}

var $$Text = {
  make: Input$Text
};

function Input$Email(props) {
  var onChange = props.onChange;
  return React.createElement("input", {
              className: "block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 " + Core__Option.getOr(props.className, ""),
              placeholder: props.placeholder,
              type: "email",
              value: props.value,
              onChange: (function (e) {
                  var target = e.target;
                  var value = target.value;
                  onChange(value);
                })
            });
}

var Email = {
  make: Input$Email
};

function Input$Password(props) {
  var onChange = props.onChange;
  return React.createElement("input", {
              className: "block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 " + Core__Option.getOr(props.className, ""),
              placeholder: props.placeholder,
              type: "password",
              value: props.value,
              onChange: (function (e) {
                  var target = e.target;
                  var value = target.value;
                  onChange(value);
                })
            });
}

var Password = {
  make: Input$Password
};

function Input$Search(props) {
  var onChange = props.onChange;
  return React.createElement("input", {
              className: "block w-full rounded-md border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 " + Core__Option.getOr(props.className, ""),
              placeholder: props.placeholder,
              type: "search",
              value: props.value,
              onChange: (function (e) {
                  var target = e.target;
                  var value = target.value;
                  onChange(value);
                })
            });
}

var Search = {
  make: Input$Search
};

var make = Input;

export {
  getInputTypeString ,
  getInputSizeClass ,
  getInputStateClass ,
  baseInputClass ,
  Label ,
  HelperText ,
  ErrorText ,
  Icon ,
  make ,
  $$Text ,
  Email ,
  Password ,
  Search ,
}
/* react Not a pure module */
