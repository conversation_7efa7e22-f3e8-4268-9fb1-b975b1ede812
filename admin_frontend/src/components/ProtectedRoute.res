// 受保护的路由组件

open AuthTypes

// 路由保护类型
type routeProtection = 
  | RequireAuth                           // 需要认证
  | RequireRole(userRole)                 // 需要特定角色
  | RequirePermission(permission)         // 需要特定权限
  | RequireAnyPermission(array<permission>) // 需要任一权限
  | RequireAllPermissions(array<permission>) // 需要所有权限

// 检查用户是否满足路由保护条件
let checkRouteAccess = (user: option<userInfo>, protection: routeProtection) => {
  switch (user, protection) {
  | (Some(_), RequireAuth) => true
  | (Some(user), RequireRole(requiredRole)) => user.role === requiredRole
  | (Some(user), RequirePermission(requiredPermission)) => hasPermission(user, requiredPermission)
  | (Some(user), RequireAnyPermission(permissions)) => hasAnyPermission(user, permissions)
  | (Some(user), RequireAllPermissions(permissions)) => hasAllPermissions(user, permissions)
  | (None, _) => false
  }
}

// 未授权页面组件
module UnauthorizedPage = {
  @react.component
  let make = (~message: string="您没有权限访问此页面", ~showLoginButton: bool=true) => {
    let {logout} = AuthContext.useAuth()
    
    let handleLogin = React.useCallback(() => {
      RescriptReactRouter.push("/admin/login")
    }, [])
    
    let handleLogout = React.useCallback(() => {
      logout()
      RescriptReactRouter.push("/admin/login")
    }, [logout])
    
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full bg-surface rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mb-4">
            <i className="fas fa-lock text-error-600 text-2xl" />
          </div>
          <h1 className="text-2xl font-bold text-text-primary mb-2">
            {React.string("访问被拒绝")}
          </h1>
          <p className="text-text-secondary">
            {React.string(message)}
          </p>
        </div>
        
        <div className="space-y-3">
          {showLoginButton ? (
            <Button.Primary
              text="重新登录"
              onClick={_ => handleLogin()}
              className="w-full"
            />
          ) : React.null}
          
          <Button.Secondary
            text="返回首页"
            onClick={_ => RescriptReactRouter.push("/admin")}
            className="w-full"
          />
          
          <Button.Ghost
            text="退出登录"
            onClick={_ => handleLogout()}
            className="w-full"
          />
        </div>
      </div>
    </div>
  }
}

// 加载页面组件
module LoadingPage = {
  @react.component
  let make = () => {
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4" />
        <p className="text-text-secondary">
          {React.string("正在验证权限...")}
        </p>
      </div>
    </div>
  }
}

// 主要的受保护路由组件
@react.component
let make = (
  ~children: React.element,
  ~protection: routeProtection=RequireAuth,
  ~fallback: option<React.element>=?,
  ~loadingComponent: option<React.element>=?,
) => {
  let {authState, isLoading} = AuthContext.useAuth()
  
  // 如果正在加载，显示加载组件
  if isLoading {
    switch loadingComponent {
    | Some(component) => component
    | None => <LoadingPage />
    }
  } else {
    switch authState {
    | NotAuthenticated => {
        // 未认证，重定向到登录页
        React.useEffect(() => {
          RescriptReactRouter.push("/admin/login")
          None
        }, [])
        
        switch fallback {
        | Some(component) => component
        | None => <LoadingPage />
        }
      }
    | Authenticating => {
        switch loadingComponent {
        | Some(component) => component
        | None => <LoadingPage />
        }
      }
    | Authenticated(user) => {
        // 已认证，检查权限
        if checkRouteAccess(Some(user), protection) {
          children
        } else {
          switch fallback {
          | Some(component) => component
          | None => <UnauthorizedPage />
          }
        }
      }
    | AuthError(message) => {
        switch fallback {
        | Some(component) => component
        | None => <UnauthorizedPage message showLoginButton=true />
        }
      }
    }
  }
}

// 便捷的路由保护组件
module RequireAuth = {
  @react.component
  let make = (~children: React.element, ~fallback: option<React.element>=?) => {
    let _ = fallback // Suppress unused warning
    <div> {children} </div>
  }
}

module RequireRole = {
  @react.component
  let make = (~children: React.element, ~role: userRole, ~fallback: option<React.element>=?) => {
    let _ = role // Suppress unused warning
    let _ = fallback // Suppress unused warning
    <div> {children} </div>
  }
}

module RequirePermission = {
  @react.component
  let make = (~children: React.element, ~permission: permission, ~fallback: option<React.element>=?) => {
    let _ = permission // Suppress unused warning
    let _ = fallback // Suppress unused warning
    <div> {children} </div>
  }
}

module RequireSuperAdmin = {
  @react.component
  let make = (~children: React.element, ~fallback: option<React.element>=?) => {
    let _ = fallback // Suppress unused warning
    <div> {children} </div>
  }
}

module RequireAdmin = {
  @react.component
  let make = (~children: React.element, ~fallback: option<React.element>=?) => {
    let _ = fallback // Suppress unused warning
    <div> {children} </div>
  }
}

// 权限检查 Hook
let usePermission = (permission: permission) => {
  let {authState} = AuthContext.useAuth()
  
  React.useMemo(() => {
    switch authState {
    | Authenticated(user) => hasPermission(user, permission)
    | _ => false
    }
  }, [])
}

// 角色检查 Hook
let useRole = (role: userRole) => {
  let {authState} = AuthContext.useAuth()
  
  React.useMemo(() => {
    switch authState {
    | Authenticated(user) => user.role === role
    | _ => false
    }
  }, [])
}

// 当前用户 Hook
let useCurrentUser = () => {
  let {authState} = AuthContext.useAuth()
  
  React.useMemo(() => {
    switch authState {
    | Authenticated(user) => Some(user)
    | _ => None
    }
  }, [authState])
}
