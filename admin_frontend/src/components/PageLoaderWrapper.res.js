// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";

function PageLoaderWrapper$Loader(props) {
  return React.createElement("div", {
              className: "flex items-center justify-center min-h-screen"
            }, React.createElement("div", {
                  className: "text-center"
                }, React.createElement("div", {
                      className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"
                    }), React.createElement("p", {
                      className: "text-secondary-600"
                    }, Core__Option.getOr(props.message, "加载中..."))));
}

var Loader = {
  make: PageLoaderWrapper$Loader
};

function PageLoaderWrapper$Error(props) {
  var onRetry = props.onRetry;
  return React.createElement("div", {
              className: "flex items-center justify-center min-h-screen"
            }, React.createElement("div", {
                  className: "text-center"
                }, React.createElement("div", {
                      className: "text-error-600 mb-4"
                    }, React.createElement("i", {
                          className: "fas fa-exclamation-triangle text-4xl"
                        })), React.createElement("h2", {
                      className: "text-xl font-semibold text-secondary-900 mb-2"
                    }, "出错了"), React.createElement("p", {
                      className: "text-secondary-600 mb-4"
                    }, props.message), onRetry !== undefined ? React.createElement("button", {
                        className: "bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md",
                        onClick: (function (param) {
                            onRetry();
                          })
                      }, "重试") : null));
}

var $$Error = {
  make: PageLoaderWrapper$Error
};

function PageLoaderWrapper(props) {
  var state = props.state;
  if (typeof state !== "object") {
    if (state === "Loading") {
      return React.createElement(PageLoaderWrapper$Loader, {});
    } else {
      return props.children;
    }
  } else {
    return React.createElement(PageLoaderWrapper$Error, {
                message: state._0,
                onRetry: props.onRetry
              });
  }
}

var make = PageLoaderWrapper;

export {
  Loader ,
  $$Error ,
  make ,
}
/* react Not a pure module */
