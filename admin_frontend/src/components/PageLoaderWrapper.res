// 页面加载器包装组件

// 页面状态类型
type pageState = 
  | Loading
  | Success
  | Error(string)

// 加载器组件
module Loader = {
  @react.component
  let make = (~message: option<string>=?) => {
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4" />
        <p className="text-secondary-600">
          {React.string(message->Option.getOr("加载中..."))}
        </p>
      </div>
    </div>
  }
}

// 错误组件
module Error = {
  @react.component
  let make = (~message: string, ~onRetry: option<unit => unit>=?) => {
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="text-error-600 mb-4">
          <i className="fas fa-exclamation-triangle text-4xl" />
        </div>
        <h2 className="text-xl font-semibold text-secondary-900 mb-2">
          {React.string("出错了")}
        </h2>
        <p className="text-secondary-600 mb-4">
          {React.string(message)}
        </p>
        {switch onRetry {
        | Some(retry) =>
          <button
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
            onClick={_ => retry()}>
            {React.string("重试")}
          </button>
        | None => React.null
        }}
      </div>
    </div>
  }
}

// 主包装组件
@react.component
let make = (~state: pageState, ~children: React.element, ~onRetry: option<unit => unit>=?) => {
  switch state {
  | Loading => <Loader />
  | Success => children
  | Error(message) => <Error message ?onRetry />
  }
}
