// Generated by ReScript, PLEASE EDIT WITH CARE

import * as Core__Array from "@rescript/core/src/Core__Array.res.js";

var translations = Object.fromEntries([
      [
        "loading",
        "Loading..."
      ],
      [
        "error",
        "Error"
      ],
      [
        "success",
        "Success"
      ],
      [
        "warning",
        "Warning"
      ],
      [
        "info",
        "Info"
      ],
      [
        "confirm",
        "Confirm"
      ],
      [
        "cancel",
        "Cancel"
      ],
      [
        "save",
        "Save"
      ],
      [
        "delete",
        "Delete"
      ],
      [
        "edit",
        "Edit"
      ],
      [
        "view",
        "View"
      ],
      [
        "create",
        "Create"
      ],
      [
        "update",
        "Update"
      ],
      [
        "search",
        "Search"
      ],
      [
        "filter",
        "Filter"
      ],
      [
        "reset",
        "Reset"
      ],
      [
        "refresh",
        "Refresh"
      ],
      [
        "back",
        "Back"
      ],
      [
        "next",
        "Next"
      ],
      [
        "previous",
        "Previous"
      ],
      [
        "close",
        "Close"
      ],
      [
        "submit",
        "Submit"
      ],
      [
        "clear",
        "Clear"
      ],
      [
        "select",
        "Select"
      ],
      [
        "all",
        "All"
      ],
      [
        "none",
        "None"
      ],
      [
        "yes",
        "Yes"
      ],
      [
        "no",
        "No"
      ],
      [
        "ok",
        "OK"
      ],
      [
        "login",
        "Login"
      ],
      [
        "logout",
        "Logout"
      ],
      [
        "email",
        "Email"
      ],
      [
        "password",
        "Password"
      ],
      [
        "remember_me",
        "Remember me"
      ],
      [
        "forgot_password",
        "Forgot password?"
      ],
      [
        "admin_login",
        "Admin Login"
      ],
      [
        "login_subtitle",
        "Pay Project Admin Dashboard"
      ],
      [
        "logging_in",
        "Logging in..."
      ],
      [
        "welcome",
        "Welcome"
      ],
      [
        "authenticating",
        "Authenticating..."
      ],
      [
        "dashboard",
        "Dashboard"
      ],
      [
        "organizations",
        "Organizations"
      ],
      [
        "merchants",
        "Merchants"
      ],
      [
        "users",
        "Users"
      ],
      [
        "analytics",
        "Analytics"
      ],
      [
        "settings",
        "Settings"
      ],
      [
        "home",
        "Home"
      ],
      [
        "organization_management",
        "Organization Management"
      ],
      [
        "manage_organizations",
        "Manage all organizations on the platform"
      ],
      [
        "create_organization",
        "Create Organization"
      ],
      [
        "organization_list",
        "Organization List"
      ],
      [
        "organization_details",
        "Organization Details"
      ],
      [
        "organization_name",
        "Organization Name"
      ],
      [
        "organization_type",
        "Type"
      ],
      [
        "organization_status",
        "Status"
      ],
      [
        "display_name",
        "Display Name"
      ],
      [
        "description",
        "Description"
      ],
      [
        "website",
        "Website"
      ],
      [
        "contact_email",
        "Contact Email"
      ],
      [
        "contact_phone",
        "Contact Phone"
      ],
      [
        "merchant_count",
        "Merchants"
      ],
      [
        "user_count",
        "Users"
      ],
      [
        "created_at",
        "Created"
      ],
      [
        "updated_at",
        "Updated"
      ],
      [
        "actions",
        "Actions"
      ],
      [
        "standard",
        "Standard"
      ],
      [
        "enterprise",
        "Enterprise"
      ],
      [
        "partner",
        "Partner"
      ],
      [
        "active",
        "Active"
      ],
      [
        "inactive",
        "Inactive"
      ],
      [
        "suspended",
        "Suspended"
      ],
      [
        "pending",
        "Pending"
      ],
      [
        "all_types",
        "All Types"
      ],
      [
        "all_statuses",
        "All Statuses"
      ],
      [
        "search_organizations",
        "Search organizations..."
      ],
      [
        "no_organizations",
        "No Organizations"
      ],
      [
        "no_organizations_desc",
        "Get started by creating your first organization"
      ],
      [
        "no_data",
        "No data available"
      ],
      [
        "load_failed",
        "Failed to load"
      ],
      [
        "create_failed",
        "Failed to create"
      ],
      [
        "update_failed",
        "Failed to update"
      ],
      [
        "delete_failed",
        "Failed to delete"
      ],
      [
        "network_error",
        "Network error"
      ],
      [
        "unauthorized",
        "Unauthorized"
      ],
      [
        "access_denied",
        "Access Denied"
      ],
      [
        "access_denied_desc",
        "You don't have permission to access this page"
      ],
      [
        "page_not_found",
        "Page Not Found"
      ],
      [
        "page_not_found_desc",
        "The page you're looking for doesn't exist"
      ],
      [
        "required_field",
        "This field is required"
      ],
      [
        "invalid_email",
        "Please enter a valid email address"
      ],
      [
        "invalid_url",
        "Please enter a valid URL"
      ],
      [
        "min_length",
        "Minimum length is {0} characters"
      ],
      [
        "max_length",
        "Maximum length is {0} characters"
      ],
      [
        "delete_confirm",
        "Are you sure you want to delete this item?"
      ],
      [
        "delete_confirm_desc",
        "This action cannot be undone"
      ],
      [
        "unsaved_changes",
        "You have unsaved changes"
      ],
      [
        "unsaved_changes_desc",
        "Are you sure you want to leave without saving?"
      ],
      [
        "operation_success",
        "Operation completed successfully"
      ],
      [
        "operation_failed",
        "Operation failed"
      ],
      [
        "saved_successfully",
        "Saved successfully"
      ],
      [
        "deleted_successfully",
        "Deleted successfully"
      ],
      [
        "created_successfully",
        "Created successfully"
      ],
      [
        "updated_successfully",
        "Updated successfully"
      ],
      [
        "page",
        "Page"
      ],
      [
        "of",
        "of"
      ],
      [
        "items_per_page",
        "Items per page"
      ],
      [
        "showing",
        "Showing"
      ],
      [
        "to",
        "to"
      ],
      [
        "results",
        "results"
      ],
      [
        "today",
        "Today"
      ],
      [
        "yesterday",
        "Yesterday"
      ],
      [
        "last_week",
        "Last week"
      ],
      [
        "last_month",
        "Last month"
      ],
      [
        "last_year",
        "Last year"
      ],
      [
        "custom_range",
        "Custom range"
      ],
      [
        "permission_denied",
        "Permission denied"
      ],
      [
        "insufficient_permissions",
        "You don't have sufficient permissions"
      ],
      [
        "admin_required",
        "Administrator access required"
      ],
      [
        "system_error",
        "System error"
      ],
      [
        "maintenance_mode",
        "System is under maintenance"
      ],
      [
        "try_again",
        "Try again"
      ],
      [
        "contact_support",
        "Contact support"
      ],
      [
        "powered_by",
        "Powered by Pay Project Inc."
      ],
      [
        "super_admin",
        "Super Admin"
      ],
      [
        "platform_admin",
        "Platform Admin"
      ],
      [
        "org_admin",
        "Organization Admin"
      ],
      [
        "support",
        "Support"
      ],
      [
        "read_only",
        "Read Only"
      ],
      [
        "admin_dashboard",
        "Admin Dashboard"
      ],
      [
        "welcome_admin",
        "Welcome to Pay Project Admin Dashboard"
      ],
      [
        "total_organizations",
        "Total Organizations"
      ],
      [
        "total_merchants",
        "Total Merchants"
      ],
      [
        "total_users",
        "Total Users"
      ],
      [
        "recent_activity",
        "Recent Activity"
      ],
      [
        "quick_actions",
        "Quick Actions"
      ],
      [
        "system_status",
        "System Status"
      ],
      [
        "platform_stats",
        "Platform Statistics"
      ],
      [
        "general_settings",
        "General Settings"
      ],
      [
        "security_settings",
        "Security Settings"
      ],
      [
        "notification_settings",
        "Notification Settings"
      ],
      [
        "system_settings",
        "System Settings"
      ],
      [
        "user_preferences",
        "User Preferences"
      ],
      [
        "language",
        "Language"
      ],
      [
        "timezone",
        "Timezone"
      ],
      [
        "currency",
        "Currency"
      ],
      [
        "address",
        "Address"
      ],
      [
        "street",
        "Street"
      ],
      [
        "city",
        "City"
      ],
      [
        "state",
        "State"
      ],
      [
        "country",
        "Country"
      ],
      [
        "postal_code",
        "Postal Code"
      ]
    ]);

function t(key, paramsOpt, param) {
  var params = paramsOpt !== undefined ? paramsOpt : [];
  var text = translations[key];
  if (text !== undefined) {
    return Core__Array.reduceWithIndex(params, text, (function (acc, param, index) {
                  var pattern = "{" + index.toString() + "}";
                  return acc.replace(pattern, param);
                }));
  } else {
    return key;
  }
}

export {
  translations ,
  t ,
}
/* translations Not a pure module */
