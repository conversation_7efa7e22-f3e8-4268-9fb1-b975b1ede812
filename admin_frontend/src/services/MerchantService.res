open MerchantTypes
open APIUtils

// API 基础路径 - v1版本
let baseUrl = "/accounts"

// 获取商户列表 - v1版本使用 /accounts/list
let getMerchantList = async (~limit=?, ~offset=?, ~organizationId=?, ()) => {
  try {
    let params = []

    let params = switch limit {
    | Some(l) => [("limit", l->Int.toString), ...params]
    | None => params
    }

    let params = switch offset {
    | Some(o) => [("offset", o->Int.toString), ...params]
    | None => params
    }

    let params = switch organizationId {
    | Some(orgId) => [("organization_id", orgId), ...params]
    | None => params
    }

    let queryString = params->Array.length > 0
      ? "?" ++ (params->Array.map(((key, value)) => `${key}=${value}`)->Array.joinWith("&"))
      : ""

    // v1版本正确路径
    let url = `${baseUrl}/list${queryString}`
    
    let response = await APIUtils.getRequest(url)
    
    switch response {
    | Ok(data) => {
        // v1 API返回的是数组格式，需要转换为标准响应格式
        let merchants = switch data->JSON.Decode.array(JSON.Decode.id) {
        | Some(jsonArray) => jsonArray->Array.map(decodeMerchantAccount)
        | None => []
        }
        
        let result: merchantListResponse = {
          data: merchants,
          total_count: merchants->Array.length,
          count: merchants->Array.length,
        }
        
        Ok(result)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`获取商户列表失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 获取单个商户详情
let getMerchant = async (merchantId: string) => {
  try {
    let url = `${baseUrl}/${merchantId}`
    let response = await APIUtils.getRequest(url)
    
    switch response {
    | Ok(data) => {
        let merchant = data->decodeMerchantAccount
        Ok(merchant)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`获取商户详情失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 创建商户
let createMerchant = async (request: createMerchantRequest) => {
  try {
    let payload = request->encodeCreateMerchantRequest
    let response = await APIUtils.postRequest(baseUrl, payload)
    
    switch response {
    | Ok(data) => {
        let merchant = data->decodeMerchantAccount
        Ok(merchant)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`创建商户失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 更新商户
let updateMerchant = async (merchantId: string, request: updateMerchantRequest) => {
  try {
    let url = `${baseUrl}/${merchantId}`
    let payload = request->encodeUpdateMerchantRequest
    let response = await APIUtils.postRequest(url, payload) // v1 API使用POST进行更新
    
    switch response {
    | Ok(data) => {
        let merchant = data->decodeMerchantAccount
        Ok(merchant)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`更新商户失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 删除商户
let deleteMerchant = async (merchantId: string) => {
  try {
    let url = `${baseUrl}/${merchantId}`
    let response = await APIUtils.deleteRequest(url)
    
    switch response {
    | Ok(_) => Ok()
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`删除商户失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 解码商户数据
let decodeMerchantAccount = (json: JSON.t): merchantAccount => {
  {
    merchant_id: json->JSON.Decode.field("merchant_id", JSON.Decode.string)->Option.getOr(""),
    merchant_name: json->JSON.Decode.field("merchant_name", JSON.Decode.optional(JSON.Decode.string)),
    merchant_details: json->JSON.Decode.field("merchant_details", JSON.Decode.optional(decodeMerchantDetails)),
    metadata: json->JSON.Decode.field("metadata", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id))),
    created_at: json->JSON.Decode.field("created_at", JSON.Decode.string)->Option.getOr(""),
    modified_at: json->JSON.Decode.field("modified_at", JSON.Decode.string)->Option.getOr(""),
    organization_id: json->JSON.Decode.field("organization_id", JSON.Decode.optional(JSON.Decode.string)),
    // 从merchant_details中解析扩展字段
    status: parseMerchantStatus(json),
    merchantType: parseMerchantType(json),
    isActive: parseMerchantIsActive(json),
    profileCount: parseMerchantProfileCount(json),
    connectorCount: parseMerchantConnectorCount(json),
    paymentCount: parseMerchantPaymentCount(json),
  }
}

// 解码商户详情
let decodeMerchantDetails = (json: JSON.t): merchantDetails => {
  {
    business_type: json->JSON.Decode.field("business_type", JSON.Decode.optional(JSON.Decode.string)),
    business_category: json->JSON.Decode.field("business_category", JSON.Decode.optional(JSON.Decode.string)),
    website: json->JSON.Decode.field("website", JSON.Decode.optional(JSON.Decode.string)),
    description: json->JSON.Decode.field("description", JSON.Decode.optional(JSON.Decode.string)),
    contact_person: json->JSON.Decode.field("contact_person", JSON.Decode.optional(JSON.Decode.string)),
    contact_email: json->JSON.Decode.field("contact_email", JSON.Decode.optional(JSON.Decode.string)),
    contact_phone: json->JSON.Decode.field("contact_phone", JSON.Decode.optional(JSON.Decode.string)),
    address: json->JSON.Decode.field("address", JSON.Decode.optional(JSON.Decode.string)),
    country: json->JSON.Decode.field("country", JSON.Decode.optional(JSON.Decode.string)),
    timezone: json->JSON.Decode.field("timezone", JSON.Decode.optional(JSON.Decode.string)),
    currency: json->JSON.Decode.field("currency", JSON.Decode.optional(JSON.Decode.string)),
  }
}

// 编码创建商户请求
let encodeCreateMerchantRequest = (request: createMerchantRequest): JSON.t => {
  let fields = [
    ("merchant_name", request.merchant_name->JSON.Encode.string),
    ("organization_id", request.organization_id->JSON.Encode.string),
  ]

  let fields = switch request.merchant_details {
  | Some(details) => [("merchant_details", details->encodeMerchantDetails), ...fields]
  | None => fields
  }

  let fields = switch request.metadata {
  | Some(meta) => [("metadata", meta->Dict.toArray->JSON.Encode.object), ...fields]
  | None => fields
  }

  fields->JSON.Encode.object
}

// 编码更新商户请求
let encodeUpdateMerchantRequest = (request: updateMerchantRequest): JSON.t => {
  let fields = []

  let fields = switch request.merchant_name {
  | Some(name) => [("merchant_name", name->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch request.merchant_details {
  | Some(details) => [("merchant_details", details->encodeMerchantDetails), ...fields]
  | None => fields
  }

  let fields = switch request.metadata {
  | Some(meta) => [("metadata", meta->Dict.toArray->JSON.Encode.object), ...fields]
  | None => fields
  }

  fields->JSON.Encode.object
}

// 编码商户详情
let encodeMerchantDetails = (details: merchantDetails): JSON.t => {
  let fields = []

  let fields = switch details.business_type {
  | Some(bt) => [("business_type", bt->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.business_category {
  | Some(bc) => [("business_category", bc->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.website {
  | Some(site) => [("website", site->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.description {
  | Some(desc) => [("description", desc->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.contact_person {
  | Some(person) => [("contact_person", person->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.contact_email {
  | Some(email) => [("contact_email", email->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.contact_phone {
  | Some(phone) => [("contact_phone", phone->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.address {
  | Some(addr) => [("address", addr->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.country {
  | Some(country) => [("country", country->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.timezone {
  | Some(tz) => [("timezone", tz->JSON.Encode.string), ...fields]
  | None => fields
  }

  let fields = switch details.currency {
  | Some(curr) => [("currency", curr->JSON.Encode.string), ...fields]
  | None => fields
  }

  fields->JSON.Encode.object
}

// 辅助解析函数
let parseMerchantStatus = (json: JSON.t): option<merchantStatus> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("status"))
  ->Option.flatMap(JSON.Decode.string)
  ->Option.flatMap(stringToMerchantStatus)
}

let parseMerchantType = (json: JSON.t): option<merchantType> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("merchant_type"))
  ->Option.flatMap(JSON.Decode.string)
  ->Option.flatMap(stringToMerchantType)
}

let parseMerchantIsActive = (json: JSON.t): option<bool> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("is_active"))
  ->Option.flatMap(JSON.Decode.bool)
}

let parseMerchantProfileCount = (json: JSON.t): option<int> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("profile_count"))
  ->Option.flatMap(JSON.Decode.int)
}

let parseMerchantConnectorCount = (json: JSON.t): option<int> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("connector_count"))
  ->Option.flatMap(JSON.Decode.int)
}

let parseMerchantPaymentCount = (json: JSON.t): option<int> => {
  json
  ->JSON.Decode.field("merchant_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("payment_count"))
  ->Option.flatMap(JSON.Decode.int)
}
