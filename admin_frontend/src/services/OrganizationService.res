open OrganizationTypes
open APIUtils

// API 基础路径
let baseUrl = "/organization"

// 获取组织列表
let getOrganizationList = async (~limit=?, ~offset=?, ()) => {
  try {
    let params = []

    let params = switch limit {
    | Some(l) => [("limit", l->Int.toString), ...params]
    | None => params
    }

    let params = switch offset {
    | Some(o) => [("offset", o->Int.toString), ...params]
    | None => params
    }

    let queryString = params->Array.length > 0
      ? "?" ++ (params->Array.map(((key, value)) => `${key}=${value}`)->Array.joinWith("&"))
      : ""
    
    let url = `${baseUrl}${queryString}`
    
    let response = await APIUtils.getRequest(url)
    
    switch response {
    | Ok(data) => {
        // 解析响应数据 - v1 API直接返回数组
        let organizations = switch JSON.Decode.array(JSON.Decode.id, data) {
        | Some(jsonArray) => jsonArray->Array.map(decodeOrganization)
        | None => []
        }

        let totalCount = organizations->Array.length
        let count = organizations->Array.length
        
        let result: organizationListResponse = {
          data: organizations,
          total_count: totalCount,
          count: count,
        }
        
        Ok(result)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`获取组织列表失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 获取单个组织详情
let getOrganization = async (orgId: string) => {
  try {
    let url = `${baseUrl}/${orgId}`
    let response = await APIUtils.getRequest(url)
    
    switch response {
    | Ok(data) => {
        let organization = data->decodeOrganization
        Ok(organization)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`获取组织详情失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 创建组织
let createOrganization = async (request: createOrganizationRequest) => {
  try {
    let payload = request->encodeCreateOrganizationRequest
    let response = await APIUtils.postRequest(baseUrl, payload)
    
    switch response {
    | Ok(data) => {
        let organization = data->decodeOrganization
        Ok(organization)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`创建组织失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 更新组织
let updateOrganization = async (orgId: string, request: updateOrganizationRequest) => {
  try {
    let url = `${baseUrl}/${orgId}`
    let payload = request->encodeUpdateOrganizationRequest
    let response = await APIUtils.putRequest(url, payload)
    
    switch response {
    | Ok(data) => {
        let organization = data->decodeOrganization
        Ok(organization)
      }
    | Error(err) => Error(err)
    }
  } catch {
  | exn => Error(`更新组织失败: ${exn->Exn.message->Option.getOr("未知错误")}`)
  }
}

// 解码组织数据
let decodeOrganization = (json: JSON.t): organization => {
  {
    id: json->JSON.Decode.field("id", JSON.Decode.string)->Option.getOr(""),
    organization_name: json->JSON.Decode.field("organization_name", JSON.Decode.optional(JSON.Decode.string)),
    organization_details: json->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id))),
    metadata: json->JSON.Decode.field("metadata", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id))),
    created_at: json->JSON.Decode.field("created_at", JSON.Decode.string)->Option.getOr(""),
    modified_at: json->JSON.Decode.field("modified_at", JSON.Decode.string)->Option.getOr(""),
    // 从organization_details中解析扩展字段
    organizationType: parseOrganizationType(json),
    status: parseOrganizationStatus(json),
    website: parseWebsite(json),
    contactEmail: parseContactEmail(json),
    contactPhone: parseContactPhone(json),
    address: parseAddress(json),
    settings: parseSettings(json),
    merchantCount: parseMerchantCount(json),
    userCount: parseUserCount(json),
    isActive: parseIsActive(json),
  }
}

// 编码创建组织请求
let encodeCreateOrganizationRequest = (request: createOrganizationRequest): JSON.t => {
  let details = Dict.make()
  
  // 将前端字段编码到organization_details中
  request.displayName->Option.forEach(name => details->Dict.set("display_name", name->JSON.Encode.string))
  request.description->Option.forEach(desc => details->Dict.set("description", desc->JSON.Encode.string))
  details->Dict.set("organization_type", request.organizationType->organizationTypeToString->JSON.Encode.string)
  request.website->Option.forEach(site => details->Dict.set("website", site->JSON.Encode.string))
  request.contactEmail->Option.forEach(email => details->Dict.set("contact_email", email->JSON.Encode.string))
  request.contactPhone->Option.forEach(phone => details->Dict.set("contact_phone", phone->JSON.Encode.string))
  
  JSON.Encode.object([
    ("organization_name", request.name->JSON.Encode.string),
    ("organization_details", details->Dict.toArray->JSON.Encode.object),
    ("metadata", request.metadata->Option.getOr(Dict.make())->Dict.toArray->JSON.Encode.object),
  ])
}

// 编码更新组织请求
let encodeUpdateOrganizationRequest = (request: updateOrganizationRequest): JSON.t => {
  let details = Dict.make()
  let fields = []
  
  // 处理organization_name
  let fields = switch request.name {
  | Some(name) => [("organization_name", name->JSON.Encode.string), ...fields]
  | None => fields
  }
  
  // 将其他字段编码到organization_details中
  request.displayName->Option.forEach(name => details->Dict.set("display_name", name->JSON.Encode.string))
  request.description->Option.forEach(desc => details->Dict.set("description", desc->JSON.Encode.string))
  request.organizationType->Option.forEach(orgType => 
    details->Dict.set("organization_type", orgType->organizationTypeToString->JSON.Encode.string)
  )
  request.status->Option.forEach(status => 
    details->Dict.set("status", status->organizationStatusToString->JSON.Encode.string)
  )
  request.website->Option.forEach(site => details->Dict.set("website", site->JSON.Encode.string))
  request.contactEmail->Option.forEach(email => details->Dict.set("contact_email", email->JSON.Encode.string))
  request.contactPhone->Option.forEach(phone => details->Dict.set("contact_phone", phone->JSON.Encode.string))
  
  let fields = if details->Dict.keysToArray->Array.length > 0 {
    [("organization_details", details->Dict.toArray->JSON.Encode.object), ...fields]
  } else {
    fields
  }

  let fields = switch request.metadata {
  | Some(meta) => [("metadata", meta->Dict.toArray->JSON.Encode.object), ...fields]
  | None => fields
  }

  fields->JSON.Encode.object
}

// 辅助解析函数
let parseOrganizationType = (json: JSON.t): option<organizationType> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("organization_type"))
  ->Option.flatMap(JSON.Decode.string)
  ->Option.flatMap(stringToOrganizationType)
}

let parseOrganizationStatus = (json: JSON.t): option<organizationStatus> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("status"))
  ->Option.flatMap(JSON.Decode.string)
  ->Option.flatMap(stringToOrganizationStatus)
}

let parseWebsite = (json: JSON.t): option<string> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("website"))
  ->Option.flatMap(JSON.Decode.string)
}

let parseContactEmail = (json: JSON.t): option<string> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("contact_email"))
  ->Option.flatMap(JSON.Decode.string)
}

let parseContactPhone = (json: JSON.t): option<string> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("contact_phone"))
  ->Option.flatMap(JSON.Decode.string)
}

let parseAddress = (json: JSON.t): option<address> => {
  // 暂时返回None，后续可以实现详细的地址解析
  None
}

let parseSettings = (json: JSON.t): option<organizationSettings> => {
  // 暂时返回None，后续可以实现详细的设置解析
  None
}

let parseMerchantCount = (json: JSON.t): option<int> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("merchant_count"))
  ->Option.flatMap(JSON.Decode.int)
}

let parseUserCount = (json: JSON.t): option<int> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("user_count"))
  ->Option.flatMap(JSON.Decode.int)
}

let parseIsActive = (json: JSON.t): option<bool> => {
  json
  ->JSON.Decode.field("organization_details", JSON.Decode.optional(JSON.Decode.dict(JSON.Decode.id)))
  ->Option.flatMap(details => details->Dict.get("is_active"))
  ->Option.flatMap(JSON.Decode.bool)
}
