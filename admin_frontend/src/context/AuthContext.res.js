// Generated by ReScript, PLEASE EDIT WITH CARE

import * as React from "react";
import * as Js_exn from "rescript/lib/es6/js_exn.js";
import * as APIUtils from "../api/APIUtils.res.js";
import * as AuthTypes from "../types/AuthTypes.res.js";
import * as Core__JSON from "@rescript/core/src/Core__JSON.res.js";
import * as LogicUtils from "../utils/LogicUtils.res.js";
import * as Core__Array from "@rescript/core/src/Core__Array.res.js";
import * as Core__Option from "@rescript/core/src/Core__Option.res.js";
import * as LocalStorage from "../utils/LocalStorage.res.js";
import * as Core__Promise from "@rescript/core/src/Core__Promise.res.js";
import * as Caml_js_exceptions from "rescript/lib/es6/caml_js_exceptions.js";

var authContext = React.createContext({
      authState: "NotAuthenticated",
      login: (function (param, param$1, param$2) {
          return Promise.resolve();
        }),
      logout: (function () {
          
        }),
      refreshToken: (function () {
          return Promise.resolve();
        }),
      checkAuth: (function () {
          return Promise.resolve();
        }),
      isLoading: false,
      error: undefined,
      clearError: (function () {
          
        })
    });

var make = authContext.Provider;

var Provider = {
  make: make
};

async function login(email, password, _rememberMe) {
  console.log("AuthContext.login called");
  console.log("Login attempt for email: " + email);
  var bodyDict = {};
  bodyDict["email"] = email;
  bodyDict["password"] = password;
  var json = await APIUtils.post("/user/signin", bodyDict);
  if (json.TAG !== "Ok") {
    return {
            TAG: "Error",
            _0: json._0.message
          };
  }
  var dict = LogicUtils.getDictFromJsonObject(json._0);
  var token = LogicUtils.getString(dict, "token", "");
  var user_permissions = [
    "OrganizationRead",
    "OrganizationWrite",
    "MerchantRead",
    "MerchantWrite",
    "UserRead",
    "UserWrite",
    "AnalyticsRead"
  ];
  var user_organizationId = "default-org";
  var user = {
    id: "admin-user",
    email: email,
    name: "Admin User",
    role: "PlatformAdmin",
    permissions: user_permissions,
    organizationId: user_organizationId,
    isActive: true,
    lastLoginAt: undefined,
    createdAt: "",
    updatedAt: ""
  };
  LocalStorage.setAuthToken(token);
  return {
          TAG: "Ok",
          _0: user
        };
}

async function refreshToken() {
  var refreshToken$1 = LocalStorage.getItem("admin_refresh_token");
  if (refreshToken$1 === undefined) {
    return {
            TAG: "Error",
            _0: "No refresh token available"
          };
  }
  var bodyDict = {};
  bodyDict["refresh_token"] = refreshToken$1;
  var json = await APIUtils.post("/admin/auth/refresh", bodyDict);
  if (json.TAG !== "Ok") {
    return {
            TAG: "Error",
            _0: json._0.message
          };
  }
  var dict = LogicUtils.getDictFromJsonObject(json._0);
  var newToken = LogicUtils.getString(dict, "token", "");
  LocalStorage.setAuthToken(newToken);
  return {
          TAG: "Ok",
          _0: undefined
        };
}

async function logout() {
  var match = LocalStorage.getAuthToken();
  if (match !== undefined) {
    await APIUtils.post("/admin/auth/logout", undefined);
    LocalStorage.clearAppData();
    return {
            TAG: "Ok",
            _0: undefined
          };
  } else {
    LocalStorage.clearAppData();
    return {
            TAG: "Ok",
            _0: undefined
          };
  }
}

async function getCurrentUser() {
  var json = await APIUtils.get("/admin/auth/me");
  if (json.TAG !== "Ok") {
    return {
            TAG: "Error",
            _0: json._0.message
          };
  }
  var dict = LogicUtils.getDictFromJsonObject(json._0);
  var user_id = LogicUtils.getString(dict, "id", "");
  var user_email = LogicUtils.getString(dict, "email", "");
  var user_name = LogicUtils.getString(dict, "name", "");
  var user_role = Core__Option.getOr(AuthTypes.stringToUserRole(LogicUtils.getString(dict, "role", "")), "ReadOnly");
  var user_permissions = Core__Array.filterMap(LogicUtils.getArray(dict, "permissions").map(function (p) {
            return Core__Option.flatMap(Core__JSON.Decode.string(p), AuthTypes.stringToPermission);
          }), (function (x) {
          return x;
        }));
  var user_organizationId = LogicUtils.getNonEmptyString(LogicUtils.getString(dict, "organization_id", ""));
  var user_isActive = LogicUtils.getBool(dict, "is_active", true);
  var user_lastLoginAt = LogicUtils.getNonEmptyString(LogicUtils.getString(dict, "last_login_at", ""));
  var user_createdAt = LogicUtils.getString(dict, "created_at", "");
  var user_updatedAt = LogicUtils.getString(dict, "updated_at", "");
  var user = {
    id: user_id,
    email: user_email,
    name: user_name,
    role: user_role,
    permissions: user_permissions,
    organizationId: user_organizationId,
    isActive: user_isActive,
    lastLoginAt: user_lastLoginAt,
    createdAt: user_createdAt,
    updatedAt: user_updatedAt
  };
  return {
          TAG: "Ok",
          _0: user
        };
}

var AuthAPI = {
  login: login,
  refreshToken: refreshToken,
  logout: logout,
  getCurrentUser: getCurrentUser
};

function AuthContext(props) {
  var match = React.useState(function () {
        return "NotAuthenticated";
      });
  var setAuthState = match[1];
  var authState = match[0];
  var match$1 = React.useState(function () {
        return false;
      });
  var setIsLoading = match$1[1];
  var match$2 = React.useState(function () {
        
      });
  var setError = match$2[1];
  var login$1 = React.useCallback((async function (email, password, rememberMe) {
          setIsLoading(function (param) {
                return true;
              });
          setError(function (param) {
                
              });
          try {
            var user = await login(email, password, rememberMe);
            if (user.TAG === "Ok") {
              var user$1 = user._0;
              setAuthState(function (param) {
                    return {
                            TAG: "Authenticated",
                            _0: user$1
                          };
                  });
              setError(function (param) {
                    
                  });
            } else {
              var message = user._0;
              setAuthState(function (param) {
                    return {
                            TAG: "AuthError",
                            _0: message
                          };
                  });
              setError(function (param) {
                    return message;
                  });
            }
          }
          catch (raw_obj){
            var obj = Caml_js_exceptions.internalToOCamlException(raw_obj);
            if (obj.RE_EXN_ID === Js_exn.$$Error) {
              var message$1 = Core__Option.getOr(obj._1.message, "Login failed");
              setAuthState(function (param) {
                    return {
                            TAG: "AuthError",
                            _0: message$1
                          };
                  });
              setError(function (param) {
                    return message$1;
                  });
            } else {
              throw obj;
            }
          }
          return setIsLoading(function (param) {
                      return false;
                    });
        }), []);
  var logout$1 = React.useCallback((function () {
          setIsLoading(function (param) {
                return true;
              });
          Core__Promise.$$catch(logout().then(function (param) {
                    setAuthState(function (param) {
                          return "NotAuthenticated";
                        });
                    setError(function (param) {
                          
                        });
                    setIsLoading(function (param) {
                          return false;
                        });
                    return Promise.resolve();
                  }), (function (param) {
                  setAuthState(function (param) {
                        return "NotAuthenticated";
                      });
                  setError(function (param) {
                        
                      });
                  setIsLoading(function (param) {
                        return false;
                      });
                  return Promise.resolve();
                }));
        }), []);
  var refreshToken$1 = React.useCallback((async function () {
          try {
            var match = await refreshToken();
            if (match.TAG === "Ok") {
              return ;
            } else {
              return logout$1();
            }
          }
          catch (exn){
            return logout$1();
          }
        }), [logout$1]);
  var checkAuth = React.useCallback((async function () {
          var match = LocalStorage.getAuthToken();
          if (match === undefined) {
            return setAuthState(function (param) {
                        return "NotAuthenticated";
                      });
          }
          setIsLoading(function (param) {
                return true;
              });
          try {
            var user = await getCurrentUser();
            if (user.TAG === "Ok") {
              var user$1 = user._0;
              setAuthState(function (param) {
                    return {
                            TAG: "Authenticated",
                            _0: user$1
                          };
                  });
            } else {
              var match$1 = await refreshToken();
              if (match$1.TAG === "Ok") {
                var user$2 = await getCurrentUser();
                if (user$2.TAG === "Ok") {
                  var user$3 = user$2._0;
                  setAuthState(function (param) {
                        return {
                                TAG: "Authenticated",
                                _0: user$3
                              };
                      });
                } else {
                  logout$1();
                }
              } else {
                logout$1();
              }
            }
          }
          catch (exn){
            logout$1();
          }
          return setIsLoading(function (param) {
                      return false;
                    });
        }), [logout$1]);
  var clearError = React.useCallback((function () {
          setError(function (param) {
                
              });
        }), []);
  React.useEffect((function () {
          checkAuth();
        }), []);
  React.useEffect((function () {
          var interval = setInterval((function () {
                  if (typeof authState !== "object") {
                    return ;
                  }
                  if (authState.TAG !== "Authenticated") {
                    return ;
                  }
                  refreshToken$1();
                }), 900000);
          return (function () {
                    clearInterval(interval);
                  });
        }), []);
  var contextValue_isLoading = match$1[0];
  var contextValue_error = match$2[0];
  var contextValue = {
    authState: authState,
    login: login$1,
    logout: logout$1,
    refreshToken: refreshToken$1,
    checkAuth: checkAuth,
    isLoading: contextValue_isLoading,
    error: contextValue_error,
    clearError: clearError
  };
  return React.createElement(make, {
              value: contextValue,
              children: props.children
            });
}

function useAuth() {
  return React.useContext(authContext);
}

var make$1 = AuthContext;

export {
  authContext ,
  Provider ,
  AuthAPI ,
  make$1 as make,
  useAuth ,
}
/* authContext Not a pure module */
