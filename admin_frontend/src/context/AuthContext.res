// 认证上下文

open AuthTypes

// 认证上下文类型
type authContextType = {
  authState: authState,
  login: (string, string, bool) => Promise.t<unit>,
  logout: unit => unit,
  refreshToken: unit => Promise.t<unit>,
  checkAuth: unit => Promise.t<unit>,
  isLoading: bool,
  error: option<string>,
  clearError: unit => unit,
}

// 创建认证上下文
let authContext = React.createContext({
  authState: NotAuthenticated,
  login: (_, _, _) => Promise.resolve(),
  logout: () => (),
  refreshToken: () => Promise.resolve(),
  checkAuth: () => Promise.resolve(),
  isLoading: false,
  error: None,
  clearError: () => (),
})

module Provider = {
  let make = React.Context.provider(authContext)
}

// 认证 API 函数
module AuthAPI = {
  let login = async (email: string, password: string, _rememberMe: bool) => {
    Console.log("AuthContext.login called")
    Console.log(`Login attempt for email: ${email}`)
    let bodyDict = Dict.make()
    bodyDict->Dict.set("email", email->JSON.Encode.string)
    bodyDict->Dict.set("password", password->JSON.Encode.string)
    let body = bodyDict->JSON.Encode.object
    
    switch await APIUtils.post("/user/signin", ~body) {
    | Ok(json) => {
        let dict = json->LogicUtils.getDictFromJsonObject
        let token = dict->LogicUtils.getString("token", "")

        // 创建基本用户信息（从token登录成功推断）
        let user: userInfo = {
          id: "admin-user",
          email: email,
          name: "Admin User",
          role: PlatformAdmin,
          permissions: [OrganizationRead, OrganizationWrite, MerchantRead, MerchantWrite, UserRead, UserWrite, AnalyticsRead],
          organizationId: Some("default-org"),
          isActive: true,
          lastLoginAt: None,
          createdAt: "",
          updatedAt: "",
        }

        // 保存认证信息
        LocalStorage.setAuthToken(token)

        Ok(user)
      }
    | Error(error) => Error(error.message)
    }
  }
  
  let refreshToken = async () => {
    switch LocalStorage.getItem("admin_refresh_token") {
    | Some(refreshToken) => {
        let bodyDict = Dict.make()
        bodyDict->Dict.set("refresh_token", refreshToken->JSON.Encode.string)
        let body = bodyDict->JSON.Encode.object
        
        switch await APIUtils.post("/admin/auth/refresh", ~body) {
        | Ok(json) => {
            let dict = json->LogicUtils.getDictFromJsonObject
            let newToken = dict->LogicUtils.getString("token", "")
            LocalStorage.setAuthToken(newToken)
            Ok()
          }
        | Error(error) => Error(error.message)
        }
      }
    | None => Error("No refresh token available")
    }
  }
  
  let logout = async () => {
    switch LocalStorage.getAuthToken() {
    | Some(_) => {
        let _ = await APIUtils.post("/admin/auth/logout")
        LocalStorage.clearAppData()
        Ok()
      }
    | None => {
        LocalStorage.clearAppData()
        Ok()
      }
    }
  }
  
  let getCurrentUser = async () => {
    switch await APIUtils.get("/admin/auth/me") {
    | Ok(json) => {
        let dict = json->LogicUtils.getDictFromJsonObject
        let user: userInfo = {
          id: dict->LogicUtils.getString("id", ""),
          email: dict->LogicUtils.getString("email", ""),
          name: dict->LogicUtils.getString("name", ""),
          role: dict->LogicUtils.getString("role", "")->stringToUserRole->Option.getOr(ReadOnly),
          permissions: dict->LogicUtils.getArray("permissions")
            ->Array.map(p => p->JSON.Decode.string->Option.flatMap(stringToPermission))
            ->Array.filterMap(x => x),
          organizationId: dict->LogicUtils.getString("organization_id", "")->LogicUtils.getNonEmptyString,
          isActive: dict->LogicUtils.getBool("is_active", true),
          lastLoginAt: dict->LogicUtils.getString("last_login_at", "")->LogicUtils.getNonEmptyString,
          createdAt: dict->LogicUtils.getString("created_at", ""),
          updatedAt: dict->LogicUtils.getString("updated_at", ""),
        }
        Ok(user)
      }
    | Error(error) => Error(error.message)
    }
  }
}

// 认证提供者组件
@react.component
let make = (~children) => {
  let (authState, setAuthState) = React.useState(_ => NotAuthenticated)
  let (isLoading, setIsLoading) = React.useState(_ => false)
  let (error, setError) = React.useState(_ => None)
  
  // 登录函数
  let login = React.useCallback(async (email: string, password: string, rememberMe: bool) => {
    setIsLoading(_ => true)
    setError(_ => None)
    
    try {
      switch await AuthAPI.login(email, password, rememberMe) {
      | Ok(user) => {
          setAuthState(_ => Authenticated(user))
          setError(_ => None)
        }
      | Error(message) => {
          setAuthState(_ => AuthError(message))
          setError(_ => Some(message))
        }
      }
    } catch {
    | Exn.Error(obj) => {
        let message = obj->Exn.message->Option.getOr("Login failed")
        setAuthState(_ => AuthError(message))
        setError(_ => Some(message))
      }
    }
    
    setIsLoading(_ => false)
  }, [])
  
  // 登出函数
  let logout = React.useCallback(() => {
    setIsLoading(_ => true)
    
    let _ = AuthAPI.logout()->Promise.then(_ => {
      setAuthState(_ => NotAuthenticated)
      setError(_ => None)
      setIsLoading(_ => false)
      Promise.resolve()
    })->Promise.catch(_ => {
      setAuthState(_ => NotAuthenticated)
      setError(_ => None)
      setIsLoading(_ => false)
      Promise.resolve()
    })
    
    ()
  }, [])
  
  // 刷新令牌函数
  let refreshToken = React.useCallback(async () => {
    try {
      switch await AuthAPI.refreshToken() {
      | Ok() => ()
      | Error(_) => logout()
      }
    } catch {
    | _ => logout()
    }
  }, [logout])
  
  // 检查认证状态
  let checkAuth = React.useCallback(async () => {
    switch LocalStorage.getAuthToken() {
    | Some(_) => {
        setIsLoading(_ => true)
        try {
          switch await AuthAPI.getCurrentUser() {
          | Ok(user) => setAuthState(_ => Authenticated(user))
          | Error(_) => {
              // 尝试刷新令牌
              switch await AuthAPI.refreshToken() {
              | Ok() => {
                  switch await AuthAPI.getCurrentUser() {
                  | Ok(user) => setAuthState(_ => Authenticated(user))
                  | Error(_) => {
                      logout()
                    }
                  }
                }
              | Error(_) => logout()
              }
            }
          }
        } catch {
        | _ => logout()
        }
        setIsLoading(_ => false)
      }
    | None => setAuthState(_ => NotAuthenticated)
    }
  }, [logout])
  
  // 清除错误
  let clearError = React.useCallback(() => {
    setError(_ => None)
  }, [])
  
  // 初始化时检查认证状态
  React.useEffect(() => {
    checkAuth()->ignore
    None
  }, [])
  
  // 定期刷新令牌
  React.useEffect(() => {
    let interval = setInterval(() => {
      switch authState {
      | Authenticated(_) => refreshToken()->ignore
      | _ => ()
      }
    }, 15 * 60 * 1000) // 每15分钟刷新一次
    
    Some(() => clearInterval(interval))
  }, [])
  
  let contextValue = {
    authState,
    login,
    logout,
    refreshToken,
    checkAuth,
    isLoading,
    error,
    clearError,
  }

  <Provider value={contextValue}>
    {children}
  </Provider>
}

// 使用认证上下文的 Hook
let useAuth = () => {
  React.useContext(authContext)
}
