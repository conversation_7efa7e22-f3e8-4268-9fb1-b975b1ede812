// 简化的 API 工具函数

// API 错误类型
type apiError = {
  status: int,
  message: string,
  code: option<string>,
}

// 简化的 GET 请求
let get = async (url: string) => {
  try {
    let fullUrl = `${GlobalVars.getApiBaseUrl()}${url}`
    let init = Fetch.RequestInit.make(
      ~method_=Get,
      ~headers=Fetch.HeadersInit.make({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "api-key": "hyperswitch",
      }),
      ()
    )
    let response = await Fetch.fetchWithInit(fullUrl, init)
    let text = await Fetch.Response.text(response)
    let status = Fetch.Response.status(response)

    if status >= 200 && status < 300 {
      switch LogicUtils.safeParseJson(text) {
      | Some(json) => Ok(json)
      | None => Error({status, message: "Invalid JSON response", code: Some("PARSE_ERROR")})
      }
    } else {
      Error({status, message: `HTTP Error ${status->Int.toString}`, code: Some("HTTP_ERROR")})
    }
  } catch {
  | _ => Error({status: 0, message: "Network error", code: Some("NETWORK_ERROR")})
  }
}

// 简化的 POST 请求
let post = async (url: string, ~body: option<JSON.t>=?) => {
  try {
    let fullUrl = `${GlobalVars.getApiBaseUrl()}${url}`

    // 添加调试日志
    Console.log(`Making POST request to: ${fullUrl}`)
    switch body {
    | Some(b) => Console.log(`Request body: ${JSON.stringify(b)}`)
    | None => Console.log("No request body")
    }

    let bodyString = switch body {
    | Some(b) => JSON.stringify(b)
    | None => ""
    }

    // 使用原生fetch API
    Console.log("About to call fetch...")
    let fetchOptions = {
      "method": "POST",
      "headers": {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "api-key": "hyperswitch",
      },
      "body": bodyString,
    }

    Console.log("Fetch options:")
    Console.log(fetchOptions)

    let (text, status) = await %raw(`
      (async () => {
        const response = await fetch(fullUrl, fetchOptions);
        const text = await response.text();
        const status = response.status;
        return [text, status];
      })()
    `)

    Console.log(`Response status: ${status->Int.toString}`)
    Console.log(`Response text: ${text}`)

    if status >= 200 && status < 300 {
      switch LogicUtils.safeParseJson(text) {
      | Some(json) => {
          Console.log(`Parsed JSON successfully`)
          Ok(json)
        }
      | None => {
          Console.log(`Failed to parse JSON: ${text}`)
          Error({status, message: "Invalid JSON response", code: Some("PARSE_ERROR")})
        }
      }
    } else {
      Console.log(`HTTP Error ${status->Int.toString}: ${text}`)
      Error({status, message: `HTTP Error ${status->Int.toString}`, code: Some("HTTP_ERROR")})
    }
  } catch {
  | error => {
      Console.log("Caught error in fetch:")
      Console.error(error)
      Error({status: 0, message: "Network error", code: Some("NETWORK_ERROR")})
    }
  }
}

// 管理员认证的 GET 请求
let getRequestWithAdminAuth = async (url: string) => {
  try {
    let fullUrl = `${GlobalVars.getApiBaseUrl()}${url}`
    let init = Fetch.RequestInit.make(
      ~method_=Get,
      ~headers=Fetch.HeadersInit.make({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": "Bearer admin_api_key_here", // TODO: 从配置或认证上下文获取
      }),
      ()
    )
    let response = await Fetch.fetchWithInit(fullUrl, init)
    let text = await Fetch.Response.text(response)
    let status = Fetch.Response.status(response)

    if status >= 200 && status < 300 {
      switch LogicUtils.safeParseJson(text) {
      | Some(json) => Ok(json)
      | None => Error({status, message: "Invalid JSON response", code: Some("PARSE_ERROR")})
      }
    } else {
      Error({status, message: `HTTP Error ${status->Int.toString}`, code: Some("HTTP_ERROR")})
    }
  } catch {
  | _ => Error({status: 0, message: "Network error", code: Some("NETWORK_ERROR")})
  }
}

// 管理员认证的 POST 请求
let postRequestWithAdminAuth = async (url: string, ~body: option<JSON.t>=?) => {
  try {
    let fullUrl = `${GlobalVars.getApiBaseUrl()}${url}`
    let bodyString = switch body {
    | Some(b) => JSON.stringify(b)
    | None => ""
    }

    let (text, status) = await %raw(`
      (async () => {
        const fullUrl = arguments[0];
        const bodyString = arguments[1];
        const response = await fetch(fullUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": "Bearer admin_api_key_here"
          },
          body: bodyString
        });
        const text = await response.text();
        const status = response.status;
        return [text, status];
      })()
    `)(fullUrl, bodyString)

    if status >= 200 && status < 300 {
      switch LogicUtils.safeParseJson(text) {
      | Some(json) => Ok(json)
      | None => Error({status, message: "Invalid JSON response", code: Some("PARSE_ERROR")})
      }
    } else {
      Error({status, message: `HTTP Error ${status->Int.toString}`, code: Some("HTTP_ERROR")})
    }
  } catch {
  | _ => Error({status: 0, message: "Network error", code: Some("NETWORK_ERROR")})
  }
}

// 兼容性别名
let getRequest = get
let postRequest = post
let putRequest = post // PUT 请求可以复用 POST 逻辑
let deleteRequest = async (url: string) => {
  try {
    let fullUrl = `${GlobalVars.getApiBaseUrl()}${url}`
    let init = Fetch.RequestInit.make(
      ~method_=Delete,
      ~headers=Fetch.HeadersInit.make({
        "Content-Type": "application/json",
        "Accept": "application/json",
        "api-key": "hyperswitch",
      }),
      ()
    )
    let response = await Fetch.fetchWithInit(fullUrl, init)
    let text = await Fetch.Response.text(response)
    let status = Fetch.Response.status(response)

    if status >= 200 && status < 300 {
      switch LogicUtils.safeParseJson(text) {
      | Some(json) => Ok(json)
      | None => Ok(JSON.Encode.null) // DELETE 请求可能返回空响应
      }
    } else {
      Error({status, message: `HTTP Error ${status->Int.toString}`, code: Some("HTTP_ERROR")})
    }
  } catch {
  | _ => Error({status: 0, message: "Network error", code: Some("NETWORK_ERROR")})
  }
}
