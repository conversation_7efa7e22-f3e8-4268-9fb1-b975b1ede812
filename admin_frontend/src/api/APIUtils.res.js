// Generated by ReScript, PLEASE EDIT WITH CARE

import * as Fetch from "bs-fetch/src/Fetch.res.js";
import * as GlobalVars from "../utils/GlobalVars.res.js";
import * as LogicUtils from "../utils/LogicUtils.res.js";
import * as Caml_js_exceptions from "rescript/lib/es6/caml_js_exceptions.js";

async function get(url) {
  try {
    var fullUrl = GlobalVars.getApiBaseUrl() + url;
    var init = Fetch.RequestInit.make("Get", {
            "Content-Type": "application/json",
            Accept: "application/json",
            "api-key": "hyperswitch"
          }, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined)();
    var response = await fetch(fullUrl, init);
    var text = await Fetch.$$Response.text(response);
    var status = response.status;
    if (!(status >= 200 && status < 300)) {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "HTTP Error " + status.toString(),
                code: "HTTP_ERROR"
              }
            };
    }
    var json = LogicUtils.safeParseJson(text);
    if (json !== undefined) {
      return {
              TAG: "Ok",
              _0: json
            };
    } else {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "Invalid JSON response",
                code: "PARSE_ERROR"
              }
            };
    }
  }
  catch (exn){
    return {
            TAG: "Error",
            _0: {
              status: 0,
              message: "Network error",
              code: "NETWORK_ERROR"
            }
          };
  }
}

async function post(url, body) {
  try {
    var fullUrl = GlobalVars.getApiBaseUrl() + url;
    console.log("Making POST request to: " + fullUrl);
    if (body !== undefined) {
      console.log("Request body: " + JSON.stringify(body));
    } else {
      console.log("No request body");
    }
    var bodyString = body !== undefined ? JSON.stringify(body) : "";
    console.log("About to call fetch...");
    var fetchOptions = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "api-key": "hyperswitch"
      },
      body: bodyString
    };
    console.log("Fetch options:");
    console.log(fetchOptions);
    var match = await ((async () => {
        const response = await fetch(fullUrl, fetchOptions);
        const text = await response.text();
        const status = response.status;
        return [text, status];
      })());
    var status = match[1];
    var text = match[0];
    console.log("Response status: " + status.toString());
    console.log("Response text: " + text);
    if (status >= 200 && status < 300) {
      var json = LogicUtils.safeParseJson(text);
      if (json !== undefined) {
        console.log("Parsed JSON successfully");
        return {
                TAG: "Ok",
                _0: json
              };
      } else {
        console.log("Failed to parse JSON: " + text);
        return {
                TAG: "Error",
                _0: {
                  status: status,
                  message: "Invalid JSON response",
                  code: "PARSE_ERROR"
                }
              };
      }
    }
    console.log("HTTP Error " + status.toString() + ": " + text);
    return {
            TAG: "Error",
            _0: {
              status: status,
              message: "HTTP Error " + status.toString(),
              code: "HTTP_ERROR"
            }
          };
  }
  catch (raw_error){
    var error = Caml_js_exceptions.internalToOCamlException(raw_error);
    console.log("Caught error in fetch:");
    console.error(error);
    return {
            TAG: "Error",
            _0: {
              status: 0,
              message: "Network error",
              code: "NETWORK_ERROR"
            }
          };
  }
}

async function getRequestWithAdminAuth(url) {
  try {
    var fullUrl = GlobalVars.getApiBaseUrl() + url;
    var init = Fetch.RequestInit.make("Get", {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: "Bearer admin_api_key_here"
          }, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined)();
    var response = await fetch(fullUrl, init);
    var text = await Fetch.$$Response.text(response);
    var status = response.status;
    if (!(status >= 200 && status < 300)) {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "HTTP Error " + status.toString(),
                code: "HTTP_ERROR"
              }
            };
    }
    var json = LogicUtils.safeParseJson(text);
    if (json !== undefined) {
      return {
              TAG: "Ok",
              _0: json
            };
    } else {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "Invalid JSON response",
                code: "PARSE_ERROR"
              }
            };
    }
  }
  catch (exn){
    return {
            TAG: "Error",
            _0: {
              status: 0,
              message: "Network error",
              code: "NETWORK_ERROR"
            }
          };
  }
}

async function postRequestWithAdminAuth(url, body) {
  try {
    var fullUrl = GlobalVars.getApiBaseUrl() + url;
    var bodyString = body !== undefined ? JSON.stringify(body) : "";
    var match = await ((async () => {
        const fullUrl = arguments[0];
        const bodyString = arguments[1];
        const response = await fetch(fullUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": "Bearer admin_api_key_here"
          },
          body: bodyString
        });
        const text = await response.text();
        const status = response.status;
        return [text, status];
      })())(fullUrl, bodyString);
    var status = match[1];
    if (!(status >= 200 && status < 300)) {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "HTTP Error " + status.toString(),
                code: "HTTP_ERROR"
              }
            };
    }
    var json = LogicUtils.safeParseJson(match[0]);
    if (json !== undefined) {
      return {
              TAG: "Ok",
              _0: json
            };
    } else {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "Invalid JSON response",
                code: "PARSE_ERROR"
              }
            };
    }
  }
  catch (exn){
    return {
            TAG: "Error",
            _0: {
              status: 0,
              message: "Network error",
              code: "NETWORK_ERROR"
            }
          };
  }
}

async function deleteRequest(url) {
  try {
    var fullUrl = GlobalVars.getApiBaseUrl() + url;
    var init = Fetch.RequestInit.make("Delete", {
            "Content-Type": "application/json",
            Accept: "application/json",
            "api-key": "hyperswitch"
          }, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined)();
    var response = await fetch(fullUrl, init);
    var text = await Fetch.$$Response.text(response);
    var status = response.status;
    if (!(status >= 200 && status < 300)) {
      return {
              TAG: "Error",
              _0: {
                status: status,
                message: "HTTP Error " + status.toString(),
                code: "HTTP_ERROR"
              }
            };
    }
    var json = LogicUtils.safeParseJson(text);
    if (json !== undefined) {
      return {
              TAG: "Ok",
              _0: json
            };
    } else {
      return {
              TAG: "Ok",
              _0: null
            };
    }
  }
  catch (exn){
    return {
            TAG: "Error",
            _0: {
              status: 0,
              message: "Network error",
              code: "NETWORK_ERROR"
            }
          };
  }
}

var getRequest = get;

var postRequest = post;

var putRequest = post;

export {
  get ,
  post ,
  getRequestWithAdminAuth ,
  postRequestWithAdminAuth ,
  getRequest ,
  postRequest ,
  putRequest ,
  deleteRequest ,
}
/* GlobalVars Not a pure module */
