// 认证相关类型定义

// 用户角色类型
type userRole = 
  | SuperAdmin      // 超级管理员
  | PlatformAdmin   // 平台管理员
  | OrgAdmin        // 组织管理员
  | Support         // 支持人员
  | ReadOnly        // 只读用户

// 权限类型
type permission = 
  | OrganizationRead
  | OrganizationWrite
  | OrganizationDelete
  | MerchantRead
  | MerchantWrite
  | MerchantDelete
  | UserRead
  | UserWrite
  | UserDelete
  | AnalyticsRead
  | SystemConfigRead
  | SystemConfigWrite
  | AuditLogRead

// 用户信息类型
type userInfo = {
  id: string,
  email: string,
  name: string,
  role: userRole,
  permissions: array<permission>,
  organizationId: option<string>,
  isActive: bool,
  lastLoginAt: option<string>,
  createdAt: string,
  updatedAt: string,
}

// 认证状态类型
type authState = 
  | NotAuthenticated
  | Authenticating
  | Authenticated(userInfo)
  | AuthError(string)

// 登录请求类型
type loginRequest = {
  email: string,
  password: string,
  rememberMe: bool,
}

// 登录响应类型
type loginResponse = {
  token: string,
  refreshToken: string,
  user: userInfo,
  expiresIn: int,
}

// 刷新令牌请求类型
type refreshTokenRequest = {
  refreshToken: string,
}

// 刷新令牌响应类型
type refreshTokenResponse = {
  token: string,
  expiresIn: int,
}

// 密码重置请求类型
type passwordResetRequest = {
  email: string,
}

// 密码重置确认类型
type passwordResetConfirmRequest = {
  token: string,
  newPassword: string,
  confirmPassword: string,
}

// 修改密码请求类型
type changePasswordRequest = {
  currentPassword: string,
  newPassword: string,
  confirmPassword: string,
}

// 用户角色转字符串
let userRoleToString = role => {
  switch role {
  | SuperAdmin => "super_admin"
  | PlatformAdmin => "platform_admin"
  | OrgAdmin => "org_admin"
  | Support => "support"
  | ReadOnly => "read_only"
  }
}

// 字符串转用户角色
let stringToUserRole = str => {
  switch str {
  | "super_admin" => Some(SuperAdmin)
  | "platform_admin" => Some(PlatformAdmin)
  | "org_admin" => Some(OrgAdmin)
  | "support" => Some(Support)
  | "read_only" => Some(ReadOnly)
  | _ => None
  }
}

// 权限转字符串
let permissionToString = permission => {
  switch permission {
  | OrganizationRead => "organization:read"
  | OrganizationWrite => "organization:write"
  | OrganizationDelete => "organization:delete"
  | MerchantRead => "merchant:read"
  | MerchantWrite => "merchant:write"
  | MerchantDelete => "merchant:delete"
  | UserRead => "user:read"
  | UserWrite => "user:write"
  | UserDelete => "user:delete"
  | AnalyticsRead => "analytics:read"
  | SystemConfigRead => "system_config:read"
  | SystemConfigWrite => "system_config:write"
  | AuditLogRead => "audit_log:read"
  }
}

// 字符串转权限
let stringToPermission = str => {
  switch str {
  | "organization:read" => Some(OrganizationRead)
  | "organization:write" => Some(OrganizationWrite)
  | "organization:delete" => Some(OrganizationDelete)
  | "merchant:read" => Some(MerchantRead)
  | "merchant:write" => Some(MerchantWrite)
  | "merchant:delete" => Some(MerchantDelete)
  | "user:read" => Some(UserRead)
  | "user:write" => Some(UserWrite)
  | "user:delete" => Some(UserDelete)
  | "analytics:read" => Some(AnalyticsRead)
  | "system_config:read" => Some(SystemConfigRead)
  | "system_config:write" => Some(SystemConfigWrite)
  | "audit_log:read" => Some(AuditLogRead)
  | _ => None
  }
}

// 检查用户是否有特定权限
let hasPermission = (user: userInfo, permission: permission) => {
  user.permissions->Array.includes(permission)
}

// 检查用户是否有任一权限
let hasAnyPermission = (user: userInfo, permissions: array<permission>) => {
  permissions->Array.some(permission => hasPermission(user, permission))
}

// 检查用户是否有所有权限
let hasAllPermissions = (user: userInfo, permissions: array<permission>) => {
  permissions->Array.every(permission => hasPermission(user, permission))
}

// 根据角色获取默认权限
let getDefaultPermissionsByRole = role => {
  switch role {
  | SuperAdmin => [
      OrganizationRead, OrganizationWrite, OrganizationDelete,
      MerchantRead, MerchantWrite, MerchantDelete,
      UserRead, UserWrite, UserDelete,
      AnalyticsRead,
      SystemConfigRead, SystemConfigWrite,
      AuditLogRead
    ]
  | PlatformAdmin => [
      OrganizationRead, OrganizationWrite,
      MerchantRead, MerchantWrite,
      UserRead, UserWrite,
      AnalyticsRead,
      SystemConfigRead,
      AuditLogRead
    ]
  | OrgAdmin => [
      OrganizationRead,
      MerchantRead, MerchantWrite,
      UserRead, UserWrite,
      AnalyticsRead
    ]
  | Support => [
      OrganizationRead,
      MerchantRead,
      UserRead,
      AnalyticsRead,
      AuditLogRead
    ]
  | ReadOnly => [
      OrganizationRead,
      MerchantRead,
      UserRead,
      AnalyticsRead
    ]
  }
}

// 检查用户是否为管理员
let isAdmin = (user: userInfo) => {
  switch user.role {
  | SuperAdmin | PlatformAdmin | OrgAdmin => true
  | _ => false
  }
}

// 检查用户是否为超级管理员
let isSuperAdmin = (user: userInfo) => {
  user.role === SuperAdmin
}

// 检查用户是否可以管理组织
let canManageOrganizations = (user: userInfo) => {
  hasAnyPermission(user, [OrganizationWrite, OrganizationDelete])
}

// 检查用户是否可以管理商户
let canManageMerchants = (user: userInfo) => {
  hasAnyPermission(user, [MerchantWrite, MerchantDelete])
}

// 检查用户是否可以管理用户
let canManageUsers = (user: userInfo) => {
  hasAnyPermission(user, [UserWrite, UserDelete])
}
