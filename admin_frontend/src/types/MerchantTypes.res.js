// Generated by ReScript, PLEASE EDIT WITH CARE

import * as LogicUtils from "../utils/LogicUtils.res.js";

function merchantStatusToString(status) {
  switch (status) {
    case "Active" :
        return "active";
    case "Inactive" :
        return "inactive";
    case "Suspended" :
        return "suspended";
    case "Pending" :
        return "pending";
    
  }
}

function stringToMerchantStatus(str) {
  switch (str) {
    case "active" :
        return "Active";
    case "inactive" :
        return "Inactive";
    case "pending" :
        return "Pending";
    case "suspended" :
        return "Suspended";
    default:
      return ;
  }
}

function merchantTypeToString(merchantType) {
  switch (merchantType) {
    case "Standard" :
        return "standard";
    case "Premium" :
        return "premium";
    case "Enterprise" :
        return "enterprise";
    
  }
}

function stringToMerchantType(str) {
  switch (str) {
    case "enterprise" :
        return "Enterprise";
    case "premium" :
        return "Premium";
    case "standard" :
        return "Standard";
    default:
      return ;
  }
}

function getMerchantStatusColor(status) {
  switch (status) {
    case "Active" :
        return "success";
    case "Inactive" :
        return "secondary";
    case "Suspended" :
        return "error";
    case "Pending" :
        return "warning";
    
  }
}

function getMerchantStatusDisplayName(status, lang) {
  switch (status) {
    case "Active" :
        if (lang === "zh") {
          return "活跃";
        } else {
          return "Active";
        }
    case "Inactive" :
        if (lang === "zh") {
          return "非活跃";
        } else {
          return "Inactive";
        }
    case "Suspended" :
        if (lang === "zh") {
          return "暂停";
        } else {
          return "Suspended";
        }
    case "Pending" :
        if (lang === "zh") {
          return "待审核";
        } else {
          return "Pending";
        }
    
  }
}

function getMerchantTypeDisplayName(merchantType, lang) {
  switch (merchantType) {
    case "Standard" :
        if (lang === "zh") {
          return "标准商户";
        } else {
          return "Standard";
        }
    case "Premium" :
        if (lang === "zh") {
          return "高级商户";
        } else {
          return "Premium";
        }
    case "Enterprise" :
        if (lang === "zh") {
          return "企业商户";
        } else {
          return "Enterprise";
        }
    
  }
}

function validateMerchantName(name) {
  var trimmedName = name.trim();
  if (trimmedName.length < 2) {
    return {
            TAG: "Error",
            _0: "商户名称至少需要2个字符"
          };
  } else if (trimmedName.length > 100) {
    return {
            TAG: "Error",
            _0: "商户名称不能超过100个字符"
          };
  } else {
    return {
            TAG: "Ok",
            _0: trimmedName
          };
  }
}

function validateContactEmail(email) {
  if (email === undefined) {
    return {
            TAG: "Ok",
            _0: undefined
          };
  }
  var trimmedEmail = email.trim();
  if (LogicUtils.isValidEmail(trimmedEmail)) {
    return {
            TAG: "Ok",
            _0: trimmedEmail
          };
  } else {
    return {
            TAG: "Error",
            _0: "请输入有效的邮箱地址"
          };
  }
}

function validateWebsite(website) {
  if (website === undefined) {
    return {
            TAG: "Ok",
            _0: undefined
          };
  }
  var trimmedUrl = website.trim();
  if (LogicUtils.isValidUrl(trimmedUrl)) {
    return {
            TAG: "Ok",
            _0: trimmedUrl
          };
  } else {
    return {
            TAG: "Error",
            _0: "请输入有效的网站URL"
          };
  }
}

var defaultMerchantDetails = {
  business_type: undefined,
  business_category: undefined,
  website: undefined,
  description: undefined,
  contact_person: undefined,
  contact_email: undefined,
  contact_phone: undefined,
  address: undefined,
  country: undefined,
  timezone: "UTC",
  currency: "USD"
};

export {
  merchantStatusToString ,
  stringToMerchantStatus ,
  merchantTypeToString ,
  stringToMerchantType ,
  getMerchantStatusColor ,
  getMerchantStatusDisplayName ,
  getMerchantTypeDisplayName ,
  validateMerchantName ,
  validateContactEmail ,
  validateWebsite ,
  defaultMerchantDetails ,
}
/* No side effect */
