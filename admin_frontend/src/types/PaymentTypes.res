// 支付状态
type paymentStatus = 
  | Succeeded
  | Failed
  | Processing
  | RequiresAction
  | RequiresPaymentMethod
  | RequiresConfirmation
  | RequiresCapture
  | Cancelled

// 支付方法类型
type paymentMethodType = 
  | Card
  | BankTransfer
  | Wallet
  | BankRedirect
  | BankDebit
  | PayLater
  | Crypto
  | Voucher

// 货币类型
type currency = 
  | USD
  | EUR
  | GBP
  | CNY
  | JPY
  | Other(string)

// 支付详情
type paymentDetails = {
  payment_id: string,
  merchant_id: string,
  status: paymentStatus,
  amount: float,
  currency: currency,
  payment_method: option<paymentMethodType>,
  payment_method_data: option<Dict.t<JSON.t>>,
  description: option<string>,
  customer_id: option<string>,
  metadata: option<Dict.t<JSON.t>>,
  created_at: string,
  modified_at: string,
  captured_at: option<string>,
  connector: option<string>,
  error_message: option<string>,
  error_code: option<string>,
}

// 时间范围
type timeRange = {
  start_time: option<string>,
  end_time: option<string>,
}

// v1 API 支付列表请求参数
type paymentListRequest = {
  limit: option<int>,
  offset: option<int>,
  merchant_id: option<string>,
  status: option<array<paymentStatus>>,
  payment_method: option<array<paymentMethodType>>,
  currency: option<array<currency>>,
  time_range: option<timeRange>,
}

// v1 API 支付列表响应
type paymentListResponse = {
  data: array<paymentDetails>,
  total_count: int,
  count: int,
}

// 支付统计数据
type paymentAggregates = {
  total_amount: float,
  total_count: int,
  successful_amount: float,
  successful_count: int,
  failed_amount: float,
  failed_count: int,
  processing_amount: float,
  processing_count: int,
  success_rate: float,
  average_amount: float,
}

// 支付过滤器
type paymentFilters = {
  connector: array<string>,
  currency: array<string>,
  status: array<string>,
  payment_method: array<string>,
}

// 支付方法配置
type paymentMethodConfig = {
  payment_method: paymentMethodType,
  payment_method_types: option<array<string>>,
  minimum_amount: option<float>,
  maximum_amount: option<float>,
  recurring_enabled: option<bool>,
  installment_payment_enabled: option<bool>,
}

// 连接器信息
type connectorInfo = {
  connector_name: string,
  connector_type: string,
  connector_label: option<string>,
  merchant_connector_id: string,
  disabled: bool,
  test_mode: bool,
  payment_methods_enabled: option<array<paymentMethodConfig>>,
  metadata: option<Dict.t<JSON.t>>,
  created_at: string,
  modified_at: string,
}

// 连接器列表响应
type connectorListResponse = {
  data: array<connectorInfo>,
  total_count: int,
  count: int,
}

// 支付查询参数（前端使用）
type paymentListParams = {
  page: option<int>,
  limit: option<int>,
  search: option<string>,
  merchantId: option<string>,
  status: option<paymentStatus>,
  paymentMethod: option<paymentMethodType>,
  currency: option<currency>,
  connector: option<string>,
  dateRange: option<(string, string)>,
  sortBy: option<string>,
  sortOrder: option<[#asc | #desc]>,
}

// 支付状态转字符串
let paymentStatusToString = status => {
  switch status {
  | Succeeded => "succeeded"
  | Failed => "failed"
  | Processing => "processing"
  | RequiresAction => "requires_action"
  | RequiresPaymentMethod => "requires_payment_method"
  | RequiresConfirmation => "requires_confirmation"
  | RequiresCapture => "requires_capture"
  | Cancelled => "cancelled"
  }
}

// 字符串转支付状态
let stringToPaymentStatus = str => {
  switch str {
  | "succeeded" => Some(Succeeded)
  | "failed" => Some(Failed)
  | "processing" => Some(Processing)
  | "requires_action" => Some(RequiresAction)
  | "requires_payment_method" => Some(RequiresPaymentMethod)
  | "requires_confirmation" => Some(RequiresConfirmation)
  | "requires_capture" => Some(RequiresCapture)
  | "cancelled" => Some(Cancelled)
  | _ => None
  }
}

// 支付方法类型转字符串
let paymentMethodTypeToString = pmType => {
  switch pmType {
  | Card => "card"
  | BankTransfer => "bank_transfer"
  | Wallet => "wallet"
  | BankRedirect => "bank_redirect"
  | BankDebit => "bank_debit"
  | PayLater => "pay_later"
  | Crypto => "crypto"
  | Voucher => "voucher"
  }
}

// 字符串转支付方法类型
let stringToPaymentMethodType = str => {
  switch str {
  | "card" => Some(Card)
  | "bank_transfer" => Some(BankTransfer)
  | "wallet" => Some(Wallet)
  | "bank_redirect" => Some(BankRedirect)
  | "bank_debit" => Some(BankDebit)
  | "pay_later" => Some(PayLater)
  | "crypto" => Some(Crypto)
  | "voucher" => Some(Voucher)
  | _ => None
  }
}

// 货币转字符串
let currencyToString = currency => {
  switch currency {
  | USD => "USD"
  | EUR => "EUR"
  | GBP => "GBP"
  | CNY => "CNY"
  | JPY => "JPY"
  | Other(code) => code
  }
}

// 字符串转货币
let stringToCurrency = str => {
  switch str {
  | "USD" => Some(USD)
  | "EUR" => Some(EUR)
  | "GBP" => Some(GBP)
  | "CNY" => Some(CNY)
  | "JPY" => Some(JPY)
  | code => Some(Other(code))
  }
}

// 获取支付状态颜色
let getPaymentStatusColor = status => {
  switch status {
  | Succeeded => "success"
  | Failed => "error"
  | Processing => "processing"
  | RequiresAction => "warning"
  | RequiresPaymentMethod => "warning"
  | RequiresConfirmation => "warning"
  | RequiresCapture => "warning"
  | Cancelled => "secondary"
  }
}

// 获取支付状态显示名称
let getPaymentStatusDisplayName = (status, lang) => {
  switch (status, lang) {
  | (Succeeded, "zh") => "成功"
  | (Failed, "zh") => "失败"
  | (Processing, "zh") => "处理中"
  | (RequiresAction, "zh") => "需要操作"
  | (RequiresPaymentMethod, "zh") => "需要支付方法"
  | (RequiresConfirmation, "zh") => "需要确认"
  | (RequiresCapture, "zh") => "需要捕获"
  | (Cancelled, "zh") => "已取消"
  | (Succeeded, _) => "Succeeded"
  | (Failed, _) => "Failed"
  | (Processing, _) => "Processing"
  | (RequiresAction, _) => "Requires Action"
  | (RequiresPaymentMethod, _) => "Requires Payment Method"
  | (RequiresConfirmation, _) => "Requires Confirmation"
  | (RequiresCapture, _) => "Requires Capture"
  | (Cancelled, _) => "Cancelled"
  }
}

// 获取支付方法显示名称
let getPaymentMethodDisplayName = (pmType, lang) => {
  switch (pmType, lang) {
  | (Card, "zh") => "银行卡"
  | (BankTransfer, "zh") => "银行转账"
  | (Wallet, "zh") => "电子钱包"
  | (BankRedirect, "zh") => "银行重定向"
  | (BankDebit, "zh") => "银行借记"
  | (PayLater, "zh") => "先买后付"
  | (Crypto, "zh") => "加密货币"
  | (Voucher, "zh") => "代金券"
  | (Card, _) => "Card"
  | (BankTransfer, _) => "Bank Transfer"
  | (Wallet, _) => "Wallet"
  | (BankRedirect, _) => "Bank Redirect"
  | (BankDebit, _) => "Bank Debit"
  | (PayLater, _) => "Pay Later"
  | (Crypto, _) => "Crypto"
  | (Voucher, _) => "Voucher"
  }
}

// 格式化金额
let formatAmount = (amount: float, currency: currency) => {
  let currencyCode = currencyToString(currency)
  // 简化的金额格式化
  `${currencyCode} ${amount->Float.toString}`
}

// 计算成功率
let calculateSuccessRate = (successCount: int, totalCount: int) => {
  if totalCount === 0 {
    0.0
  } else {
    Float.fromInt(successCount) /. Float.fromInt(totalCount) *. 100.0
  }
}
