// Generated by ReScript, PLEASE EDIT WITH CARE


function paymentStatusToString(status) {
  switch (status) {
    case "Succeeded" :
        return "succeeded";
    case "Failed" :
        return "failed";
    case "Processing" :
        return "processing";
    case "RequiresAction" :
        return "requires_action";
    case "RequiresPaymentMethod" :
        return "requires_payment_method";
    case "RequiresConfirmation" :
        return "requires_confirmation";
    case "RequiresCapture" :
        return "requires_capture";
    case "Cancelled" :
        return "cancelled";
    
  }
}

function stringToPaymentStatus(str) {
  switch (str) {
    case "cancelled" :
        return "Cancelled";
    case "failed" :
        return "Failed";
    case "processing" :
        return "Processing";
    case "requires_action" :
        return "RequiresAction";
    case "requires_capture" :
        return "RequiresCapture";
    case "requires_confirmation" :
        return "RequiresConfirmation";
    case "requires_payment_method" :
        return "RequiresPaymentMethod";
    case "succeeded" :
        return "Succeeded";
    default:
      return ;
  }
}

function paymentMethodTypeToString(pmType) {
  switch (pmType) {
    case "Card" :
        return "card";
    case "BankTransfer" :
        return "bank_transfer";
    case "Wallet" :
        return "wallet";
    case "BankRedirect" :
        return "bank_redirect";
    case "BankDebit" :
        return "bank_debit";
    case "PayLater" :
        return "pay_later";
    case "Crypto" :
        return "crypto";
    case "Voucher" :
        return "voucher";
    
  }
}

function stringToPaymentMethodType(str) {
  switch (str) {
    case "bank_debit" :
        return "BankDebit";
    case "bank_redirect" :
        return "BankRedirect";
    case "bank_transfer" :
        return "BankTransfer";
    case "card" :
        return "Card";
    case "crypto" :
        return "Crypto";
    case "pay_later" :
        return "PayLater";
    case "voucher" :
        return "Voucher";
    case "wallet" :
        return "Wallet";
    default:
      return ;
  }
}

function currencyToString(currency) {
  if (typeof currency === "object") {
    return currency._0;
  }
  switch (currency) {
    case "USD" :
        return "USD";
    case "EUR" :
        return "EUR";
    case "GBP" :
        return "GBP";
    case "CNY" :
        return "CNY";
    case "JPY" :
        return "JPY";
    
  }
}

function stringToCurrency(str) {
  switch (str) {
    case "CNY" :
        return "CNY";
    case "EUR" :
        return "EUR";
    case "GBP" :
        return "GBP";
    case "JPY" :
        return "JPY";
    case "USD" :
        return "USD";
    default:
      return {
              TAG: "Other",
              _0: str
            };
  }
}

function getPaymentStatusColor(status) {
  switch (status) {
    case "Succeeded" :
        return "success";
    case "Failed" :
        return "error";
    case "Processing" :
        return "processing";
    case "RequiresAction" :
    case "RequiresPaymentMethod" :
    case "RequiresConfirmation" :
    case "RequiresCapture" :
        return "warning";
    case "Cancelled" :
        return "secondary";
    
  }
}

function getPaymentStatusDisplayName(status, lang) {
  switch (status) {
    case "Succeeded" :
        if (lang === "zh") {
          return "成功";
        } else {
          return "Succeeded";
        }
    case "Failed" :
        if (lang === "zh") {
          return "失败";
        } else {
          return "Failed";
        }
    case "Processing" :
        if (lang === "zh") {
          return "处理中";
        } else {
          return "Processing";
        }
    case "RequiresAction" :
        if (lang === "zh") {
          return "需要操作";
        } else {
          return "Requires Action";
        }
    case "RequiresPaymentMethod" :
        if (lang === "zh") {
          return "需要支付方法";
        } else {
          return "Requires Payment Method";
        }
    case "RequiresConfirmation" :
        if (lang === "zh") {
          return "需要确认";
        } else {
          return "Requires Confirmation";
        }
    case "RequiresCapture" :
        if (lang === "zh") {
          return "需要捕获";
        } else {
          return "Requires Capture";
        }
    case "Cancelled" :
        if (lang === "zh") {
          return "已取消";
        } else {
          return "Cancelled";
        }
    
  }
}

function getPaymentMethodDisplayName(pmType, lang) {
  switch (pmType) {
    case "Card" :
        if (lang === "zh") {
          return "银行卡";
        } else {
          return "Card";
        }
    case "BankTransfer" :
        if (lang === "zh") {
          return "银行转账";
        } else {
          return "Bank Transfer";
        }
    case "Wallet" :
        if (lang === "zh") {
          return "电子钱包";
        } else {
          return "Wallet";
        }
    case "BankRedirect" :
        if (lang === "zh") {
          return "银行重定向";
        } else {
          return "Bank Redirect";
        }
    case "BankDebit" :
        if (lang === "zh") {
          return "银行借记";
        } else {
          return "Bank Debit";
        }
    case "PayLater" :
        if (lang === "zh") {
          return "先买后付";
        } else {
          return "Pay Later";
        }
    case "Crypto" :
        if (lang === "zh") {
          return "加密货币";
        } else {
          return "Crypto";
        }
    case "Voucher" :
        if (lang === "zh") {
          return "代金券";
        } else {
          return "Voucher";
        }
    
  }
}

function formatAmount(amount, currency) {
  var currencyCode = currencyToString(currency);
  return currencyCode + " " + amount.toString();
}

function calculateSuccessRate(successCount, totalCount) {
  if (totalCount === 0) {
    return 0.0;
  } else {
    return successCount / totalCount * 100.0;
  }
}

export {
  paymentStatusToString ,
  stringToPaymentStatus ,
  paymentMethodTypeToString ,
  stringToPaymentMethodType ,
  currencyToString ,
  stringToCurrency ,
  getPaymentStatusColor ,
  getPaymentStatusDisplayName ,
  getPaymentMethodDisplayName ,
  formatAmount ,
  calculateSuccessRate ,
}
/* No side effect */
