// Generated by ReScript, PLEASE EDIT WITH CARE

import * as LogicUtils from "../utils/LogicUtils.res.js";

function organizationTypeToString(orgType) {
  switch (orgType) {
    case "Standard" :
        return "standard";
    case "Enterprise" :
        return "enterprise";
    case "Partner" :
        return "partner";
    
  }
}

function stringToOrganizationType(str) {
  switch (str) {
    case "enterprise" :
        return "Enterprise";
    case "partner" :
        return "Partner";
    case "standard" :
        return "Standard";
    default:
      return ;
  }
}

function organizationStatusToString(status) {
  switch (status) {
    case "Active" :
        return "active";
    case "Inactive" :
        return "inactive";
    case "Suspended" :
        return "suspended";
    case "Pending" :
        return "pending";
    
  }
}

function stringToOrganizationStatus(str) {
  switch (str) {
    case "active" :
        return "Active";
    case "inactive" :
        return "Inactive";
    case "pending" :
        return "Pending";
    case "suspended" :
        return "Suspended";
    default:
      return ;
  }
}

function getOrganizationStatusColor(status) {
  switch (status) {
    case "Active" :
        return "success";
    case "Inactive" :
        return "secondary";
    case "Suspended" :
        return "error";
    case "Pending" :
        return "warning";
    
  }
}

function getOrganizationTypeDisplayName(orgType, lang) {
  switch (orgType) {
    case "Standard" :
        if (lang === "zh") {
          return "标准组织";
        } else {
          return "Standard";
        }
    case "Enterprise" :
        if (lang === "zh") {
          return "企业组织";
        } else {
          return "Enterprise";
        }
    case "Partner" :
        if (lang === "zh") {
          return "合作伙伴";
        } else {
          return "Partner";
        }
    
  }
}

function getOrganizationStatusDisplayName(status, lang) {
  switch (status) {
    case "Active" :
        if (lang === "zh") {
          return "活跃";
        } else {
          return "Active";
        }
    case "Inactive" :
        if (lang === "zh") {
          return "非活跃";
        } else {
          return "Inactive";
        }
    case "Suspended" :
        if (lang === "zh") {
          return "暂停";
        } else {
          return "Suspended";
        }
    case "Pending" :
        if (lang === "zh") {
          return "待审核";
        } else {
          return "Pending";
        }
    
  }
}

function validateOrganizationName(name) {
  var trimmedName = name.trim();
  if (trimmedName.length < 2) {
    return {
            TAG: "Error",
            _0: "组织名称至少需要2个字符"
          };
  } else if (trimmedName.length > 100) {
    return {
            TAG: "Error",
            _0: "组织名称不能超过100个字符"
          };
  } else {
    return {
            TAG: "Ok",
            _0: trimmedName
          };
  }
}

function validateContactEmail(email) {
  if (email === undefined) {
    return {
            TAG: "Ok",
            _0: undefined
          };
  }
  var trimmedEmail = email.trim();
  if (LogicUtils.isValidEmail(trimmedEmail)) {
    return {
            TAG: "Ok",
            _0: trimmedEmail
          };
  } else {
    return {
            TAG: "Error",
            _0: "请输入有效的邮箱地址"
          };
  }
}

function validateWebsite(website) {
  if (website === undefined) {
    return {
            TAG: "Ok",
            _0: undefined
          };
  }
  var trimmedUrl = website.trim();
  if (LogicUtils.isValidUrl(trimmedUrl)) {
    return {
            TAG: "Ok",
            _0: trimmedUrl
          };
  } else {
    return {
            TAG: "Error",
            _0: "请输入有效的网站URL"
          };
  }
}

var defaultOrganizationSettings = {
  allowMerchantCreation: true,
  maxMerchants: undefined,
  allowUserInvitation: true,
  maxUsers: undefined,
  enableAuditLog: true,
  enableNotifications: true,
  timezone: "UTC",
  currency: "USD",
  language: "en"
};

export {
  organizationTypeToString ,
  stringToOrganizationType ,
  organizationStatusToString ,
  stringToOrganizationStatus ,
  getOrganizationStatusColor ,
  getOrganizationTypeDisplayName ,
  getOrganizationStatusDisplayName ,
  defaultOrganizationSettings ,
  validateOrganizationName ,
  validateContactEmail ,
  validateWebsite ,
}
/* No side effect */
