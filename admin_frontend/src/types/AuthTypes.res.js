// Generated by ReScript, PLEASE EDIT WITH CARE


function userRoleToString(role) {
  switch (role) {
    case "SuperAdmin" :
        return "super_admin";
    case "PlatformAdmin" :
        return "platform_admin";
    case "OrgAdmin" :
        return "org_admin";
    case "Support" :
        return "support";
    case "ReadOnly" :
        return "read_only";
    
  }
}

function stringToUserRole(str) {
  switch (str) {
    case "org_admin" :
        return "OrgAdmin";
    case "platform_admin" :
        return "PlatformAdmin";
    case "read_only" :
        return "ReadOnly";
    case "super_admin" :
        return "SuperAdmin";
    case "support" :
        return "Support";
    default:
      return ;
  }
}

function permissionToString(permission) {
  switch (permission) {
    case "OrganizationRead" :
        return "organization:read";
    case "OrganizationWrite" :
        return "organization:write";
    case "OrganizationDelete" :
        return "organization:delete";
    case "MerchantRead" :
        return "merchant:read";
    case "MerchantWrite" :
        return "merchant:write";
    case "MerchantDelete" :
        return "merchant:delete";
    case "UserRead" :
        return "user:read";
    case "UserWrite" :
        return "user:write";
    case "UserDelete" :
        return "user:delete";
    case "AnalyticsRead" :
        return "analytics:read";
    case "SystemConfigRead" :
        return "system_config:read";
    case "SystemConfigWrite" :
        return "system_config:write";
    case "AuditLogRead" :
        return "audit_log:read";
    
  }
}

function stringToPermission(str) {
  switch (str) {
    case "analytics:read" :
        return "AnalyticsRead";
    case "audit_log:read" :
        return "AuditLogRead";
    case "merchant:delete" :
        return "MerchantDelete";
    case "merchant:read" :
        return "MerchantRead";
    case "merchant:write" :
        return "MerchantWrite";
    case "organization:delete" :
        return "OrganizationDelete";
    case "organization:read" :
        return "OrganizationRead";
    case "organization:write" :
        return "OrganizationWrite";
    case "system_config:read" :
        return "SystemConfigRead";
    case "system_config:write" :
        return "SystemConfigWrite";
    case "user:delete" :
        return "UserDelete";
    case "user:read" :
        return "UserRead";
    case "user:write" :
        return "UserWrite";
    default:
      return ;
  }
}

function hasPermission(user, permission) {
  return user.permissions.includes(permission);
}

function hasAnyPermission(user, permissions) {
  return permissions.some(function (permission) {
              return user.permissions.includes(permission);
            });
}

function hasAllPermissions(user, permissions) {
  return permissions.every(function (permission) {
              return user.permissions.includes(permission);
            });
}

function getDefaultPermissionsByRole(role) {
  switch (role) {
    case "SuperAdmin" :
        return [
                "OrganizationRead",
                "OrganizationWrite",
                "OrganizationDelete",
                "MerchantRead",
                "MerchantWrite",
                "MerchantDelete",
                "UserRead",
                "UserWrite",
                "UserDelete",
                "AnalyticsRead",
                "SystemConfigRead",
                "SystemConfigWrite",
                "AuditLogRead"
              ];
    case "PlatformAdmin" :
        return [
                "OrganizationRead",
                "OrganizationWrite",
                "MerchantRead",
                "MerchantWrite",
                "UserRead",
                "UserWrite",
                "AnalyticsRead",
                "SystemConfigRead",
                "AuditLogRead"
              ];
    case "OrgAdmin" :
        return [
                "OrganizationRead",
                "MerchantRead",
                "MerchantWrite",
                "UserRead",
                "UserWrite",
                "AnalyticsRead"
              ];
    case "Support" :
        return [
                "OrganizationRead",
                "MerchantRead",
                "UserRead",
                "AnalyticsRead",
                "AuditLogRead"
              ];
    case "ReadOnly" :
        return [
                "OrganizationRead",
                "MerchantRead",
                "UserRead",
                "AnalyticsRead"
              ];
    
  }
}

function isAdmin(user) {
  var match = user.role;
  switch (match) {
    case "Support" :
    case "ReadOnly" :
        return false;
    default:
      return true;
  }
}

function isSuperAdmin(user) {
  return user.role === "SuperAdmin";
}

function canManageOrganizations(user) {
  return hasAnyPermission(user, [
              "OrganizationWrite",
              "OrganizationDelete"
            ]);
}

function canManageMerchants(user) {
  return hasAnyPermission(user, [
              "MerchantWrite",
              "MerchantDelete"
            ]);
}

function canManageUsers(user) {
  return hasAnyPermission(user, [
              "UserWrite",
              "UserDelete"
            ]);
}

export {
  userRoleToString ,
  stringToUserRole ,
  permissionToString ,
  stringToPermission ,
  hasPermission ,
  hasAnyPermission ,
  hasAllPermissions ,
  getDefaultPermissionsByRole ,
  isAdmin ,
  isSuperAdmin ,
  canManageOrganizations ,
  canManageMerchants ,
  canManageUsers ,
}
/* No side effect */
