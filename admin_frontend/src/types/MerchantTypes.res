// 商户账户状态
type merchantStatus = 
  | Active
  | Inactive
  | Suspended
  | Pending

// 商户类型
type merchantType = 
  | Standard
  | Premium
  | Enterprise

// 商户详情
type merchantDetails = {
  business_type: option<string>,
  business_category: option<string>,
  website: option<string>,
  description: option<string>,
  contact_person: option<string>,
  contact_email: option<string>,
  contact_phone: option<string>,
  address: option<string>,
  country: option<string>,
  timezone: option<string>,
  currency: option<string>,
}

// 商户账户信息 - 匹配v1 API响应结构
type merchantAccount = {
  merchant_id: string,
  merchant_name: option<string>,
  merchant_details: option<merchantDetails>,
  metadata: option<Dict.t<JSON.t>>,
  created_at: string,
  modified_at: string,
  organization_id: option<string>,
  // 扩展字段（用于前端显示）
  status: option<merchantStatus>,
  merchantType: option<merchantType>,
  isActive: option<bool>,
  profileCount: option<int>,
  connectorCount: option<int>,
  paymentCount: option<int>,
}

// v1 API 商户列表请求参数
type merchantListRequest = {
  limit: option<int>,
  offset: option<int>,
  organization_id: option<string>,
}

// v1 API 商户列表响应
type merchantListResponse = {
  data: array<merchantAccount>,
  total_count: int,
  count: int,
}

// 创建商户请求
type createMerchantRequest = {
  merchant_name: string,
  organization_id: string,
  merchant_details: option<merchantDetails>,
  metadata: option<Dict.t<JSON.t>>,
}

// 更新商户请求
type updateMerchantRequest = {
  merchant_name: option<string>,
  merchant_details: option<merchantDetails>,
  metadata: option<Dict.t<JSON.t>>,
}

// 商户列表查询参数（前端使用）
type merchantListParams = {
  page: option<int>,
  limit: option<int>,
  search: option<string>,
  organizationId: option<string>,
  status: option<merchantStatus>,
  merchantType: option<merchantType>,
  sortBy: option<string>,
  sortOrder: option<[#asc | #desc]>,
}

// 商户统计
type merchantStats = {
  totalMerchants: int,
  activeMerchants: int,
  inactiveMerchants: int,
  suspendedMerchants: int,
  pendingMerchants: int,
  merchantsByType: Dict.t<int>,
  merchantsByOrganization: Dict.t<int>,
  recentMerchants: array<merchantAccount>,
}

// 商户状态转字符串
let merchantStatusToString = status => {
  switch status {
  | Active => "active"
  | Inactive => "inactive"
  | Suspended => "suspended"
  | Pending => "pending"
  }
}

// 字符串转商户状态
let stringToMerchantStatus = str => {
  switch str {
  | "active" => Some(Active)
  | "inactive" => Some(Inactive)
  | "suspended" => Some(Suspended)
  | "pending" => Some(Pending)
  | _ => None
  }
}

// 商户类型转字符串
let merchantTypeToString = merchantType => {
  switch merchantType {
  | Standard => "standard"
  | Premium => "premium"
  | Enterprise => "enterprise"
  }
}

// 字符串转商户类型
let stringToMerchantType = str => {
  switch str {
  | "standard" => Some(Standard)
  | "premium" => Some(Premium)
  | "enterprise" => Some(Enterprise)
  | _ => None
  }
}

// 获取商户状态颜色
let getMerchantStatusColor = status => {
  switch status {
  | Active => "success"
  | Inactive => "secondary"
  | Suspended => "error"
  | Pending => "warning"
  }
}

// 获取商户状态显示名称
let getMerchantStatusDisplayName = (status, lang) => {
  switch (status, lang) {
  | (Active, "zh") => "活跃"
  | (Inactive, "zh") => "非活跃"
  | (Suspended, "zh") => "暂停"
  | (Pending, "zh") => "待审核"
  | (Active, _) => "Active"
  | (Inactive, _) => "Inactive"
  | (Suspended, _) => "Suspended"
  | (Pending, _) => "Pending"
  }
}

// 获取商户类型显示名称
let getMerchantTypeDisplayName = (merchantType, lang) => {
  switch (merchantType, lang) {
  | (Standard, "zh") => "标准商户"
  | (Premium, "zh") => "高级商户"
  | (Enterprise, "zh") => "企业商户"
  | (Standard, _) => "Standard"
  | (Premium, _) => "Premium"
  | (Enterprise, _) => "Enterprise"
  }
}

// 验证商户名称
let validateMerchantName = name => {
  let trimmedName = name->String.trim
  if trimmedName->String.length < 2 {
    Error("商户名称至少需要2个字符")
  } else if trimmedName->String.length > 100 {
    Error("商户名称不能超过100个字符")
  } else {
    Ok(trimmedName)
  }
}

// 验证联系邮箱
let validateContactEmail = email => {
  switch email {
  | Some(emailStr) => {
      let trimmedEmail = emailStr->String.trim
      if LogicUtils.isValidEmail(trimmedEmail) {
        Ok(Some(trimmedEmail))
      } else {
        Error("请输入有效的邮箱地址")
      }
    }
  | None => Ok(None)
  }
}

// 验证网站URL
let validateWebsite = website => {
  switch website {
  | Some(urlStr) => {
      let trimmedUrl = urlStr->String.trim
      if LogicUtils.isValidUrl(trimmedUrl) {
        Ok(Some(trimmedUrl))
      } else {
        Error("请输入有效的网站URL")
      }
    }
  | None => Ok(None)
  }
}

// 默认商户详情
let defaultMerchantDetails: merchantDetails = {
  business_type: None,
  business_category: None,
  website: None,
  description: None,
  contact_person: None,
  contact_email: None,
  contact_phone: None,
  address: None,
  country: None,
  timezone: Some("UTC"),
  currency: Some("USD"),
}
