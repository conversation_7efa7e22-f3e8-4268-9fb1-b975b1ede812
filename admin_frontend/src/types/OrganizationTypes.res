// 组织相关类型定义

// 组织类型
type organizationType = 
  | Standard    // 标准组织
  | Enterprise  // 企业组织
  | Partner     // 合作伙伴组织

// 组织状态
type organizationStatus = 
  | Active      // 活跃
  | Inactive    // 非活跃
  | Suspended   // 暂停
  | Pending     // 待审核

// 地址类型
type address = {
  street: option<string>,
  city: option<string>,
  state: option<string>,
  country: option<string>,
  postalCode: option<string>,
}

// 组织设置类型
type organizationSettings = {
  allowMerchantCreation: bool,
  maxMerchants: option<int>,
  allowUserInvitation: bool,
  maxUsers: option<int>,
  enableAuditLog: bool,
  enableNotifications: bool,
  timezone: string,
  currency: string,
  language: string,
}

// 组织信息类型 - 匹配v1 API响应结构
type organization = {
  id: string,
  organization_name: option<string>,
  organization_details: option<Dict.t<JSON.t>>,
  metadata: option<Dict.t<JSON.t>>,
  created_at: string,
  modified_at: string,
  // 扩展字段（用于前端显示，从organization_details中解析）
  organizationType: option<organizationType>,
  status: option<organizationStatus>,
  website: option<string>,
  contactEmail: option<string>,
  contactPhone: option<string>,
  address: option<address>,
  settings: option<organizationSettings>,
  merchantCount: option<int>,
  userCount: option<int>,
  isActive: option<bool>,
}

// 创建组织请求类型
type createOrganizationRequest = {
  name: string,
  displayName: option<string>,
  description: option<string>,
  organizationType: organizationType,
  website: option<string>,
  contactEmail: option<string>,
  contactPhone: option<string>,
  address: option<address>,
  settings: option<organizationSettings>,
  metadata: option<Dict.t<JSON.t>>,
}

// 更新组织请求类型
type updateOrganizationRequest = {
  name: option<string>,
  displayName: option<string>,
  description: option<string>,
  organizationType: option<organizationType>,
  status: option<organizationStatus>,
  website: option<string>,
  contactEmail: option<string>,
  contactPhone: option<string>,
  address: option<address>,
  settings: option<organizationSettings>,
  metadata: option<Dict.t<JSON.t>>,
}

// v1 API 组织列表请求参数
type organizationListRequest = {
  limit: option<int>,
  offset: option<int>,
}

// v1 API 组织列表响应
type organizationListResponse = {
  data: array<organization>,
  total_count: int,
  count: int,
}

// 组织列表查询参数（前端使用）
type organizationListParams = {
  page: option<int>,
  limit: option<int>,
  search: option<string>,
  organizationType: option<organizationType>,
  status: option<organizationStatus>,
  sortBy: option<string>,
  sortOrder: option<[#asc | #desc]>,
}

// 组织统计类型
type organizationStats = {
  totalOrganizations: int,
  activeOrganizations: int,
  inactiveOrganizations: int,
  suspendedOrganizations: int,
  pendingOrganizations: int,
  totalMerchants: int,
  totalUsers: int,
  organizationsByType: Dict.t<int>,
  recentOrganizations: array<organization>,
}

// 组织类型转字符串
let organizationTypeToString = orgType => {
  switch orgType {
  | Standard => "standard"
  | Enterprise => "enterprise"
  | Partner => "partner"
  }
}

// 字符串转组织类型
let stringToOrganizationType = str => {
  switch str {
  | "standard" => Some(Standard)
  | "enterprise" => Some(Enterprise)
  | "partner" => Some(Partner)
  | _ => None
  }
}

// 组织状态转字符串
let organizationStatusToString = status => {
  switch status {
  | Active => "active"
  | Inactive => "inactive"
  | Suspended => "suspended"
  | Pending => "pending"
  }
}

// 字符串转组织状态
let stringToOrganizationStatus = str => {
  switch str {
  | "active" => Some(Active)
  | "inactive" => Some(Inactive)
  | "suspended" => Some(Suspended)
  | "pending" => Some(Pending)
  | _ => None
  }
}

// 获取组织状态颜色
let getOrganizationStatusColor = status => {
  switch status {
  | Active => "success"
  | Inactive => "secondary"
  | Suspended => "error"
  | Pending => "warning"
  }
}

// 获取组织类型显示名称
let getOrganizationTypeDisplayName = (orgType, lang) => {
  switch (orgType, lang) {
  | (Standard, "zh") => "标准组织"
  | (Enterprise, "zh") => "企业组织"
  | (Partner, "zh") => "合作伙伴"
  | (Standard, _) => "Standard"
  | (Enterprise, _) => "Enterprise"
  | (Partner, _) => "Partner"
  }
}

// 获取组织状态显示名称
let getOrganizationStatusDisplayName = (status, lang) => {
  switch (status, lang) {
  | (Active, "zh") => "活跃"
  | (Inactive, "zh") => "非活跃"
  | (Suspended, "zh") => "暂停"
  | (Pending, "zh") => "待审核"
  | (Active, _) => "Active"
  | (Inactive, _) => "Inactive"
  | (Suspended, _) => "Suspended"
  | (Pending, _) => "Pending"
  }
}

// 默认组织设置
let defaultOrganizationSettings: organizationSettings = {
  allowMerchantCreation: true,
  maxMerchants: None,
  allowUserInvitation: true,
  maxUsers: None,
  enableAuditLog: true,
  enableNotifications: true,
  timezone: "UTC",
  currency: "USD",
  language: "en",
}

// 验证组织名称
let validateOrganizationName = name => {
  let trimmedName = name->String.trim
  if trimmedName->String.length < 2 {
    Error("组织名称至少需要2个字符")
  } else if trimmedName->String.length > 100 {
    Error("组织名称不能超过100个字符")
  } else {
    Ok(trimmedName)
  }
}

// 验证联系邮箱
let validateContactEmail = email => {
  switch email {
  | Some(emailStr) => {
      let trimmedEmail = emailStr->String.trim
      if LogicUtils.isValidEmail(trimmedEmail) {
        Ok(Some(trimmedEmail))
      } else {
        Error("请输入有效的邮箱地址")
      }
    }
  | None => Ok(None)
  }
}

// 验证网站URL
let validateWebsite = website => {
  switch website {
  | Some(urlStr) => {
      let trimmedUrl = urlStr->String.trim
      if LogicUtils.isValidUrl(trimmedUrl) {
        Ok(Some(trimmedUrl))
      } else {
        Error("请输入有效的网站URL")
      }
    }
  | None => Ok(None)
  }
}
