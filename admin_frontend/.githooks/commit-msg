#!/bin/bash

# Git commit message hook for Pay Project Admin Frontend

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the commit message
commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo -e "${RED}Invalid commit message format!${NC}"
    echo -e "${YELLOW}Format should be: type(scope): description${NC}"
    echo -e "${YELLOW}Types: feat, fix, docs, style, refactor, test, chore${NC}"
    echo -e "${YELLOW}Example: feat(auth): add login functionality${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Commit message format is valid${NC}"
exit 0
