{"$schema": "https://raw.githubusercontent.com/rescript-lang/rescript-compiler/master/docs/docson/build-schema.json", "name": "pay-project-admin", "uncurried": true, "jsx": {"version": 4, "mode": "classic"}, "bsc-flags": ["-bs-super-errors", "-open RescriptCore"], "sources": {"dir": "src", "subdirs": true}, "suffix": ".res.js", "namespace": false, "ppx-flags": [], "package-specs": {"module": "esmodule", "in-source": true}, "bs-dependencies": ["@rescript/react", "rescript-webapi", "bs-fetch", "@rescript/core"]}