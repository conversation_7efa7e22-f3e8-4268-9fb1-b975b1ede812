rescript = 1
rule cp
  command = cp $i $out
rule touch
 command = touch $out
o MerchantService.cmi : cp ../bs/src/services/MerchantService.cmi
o MerchantService.cmj : cp ../bs/src/services/MerchantService.cmj
o MerchantService.cmt : cp ../bs/src/services/MerchantService.cmt
o MerchantService.res : cp ../../src/services/MerchantService.res
o OrganizationService.cmi : cp ../bs/src/services/OrganizationService.cmi
o OrganizationService.cmj : cp ../bs/src/services/OrganizationService.cmj
o OrganizationService.cmt : cp ../bs/src/services/OrganizationService.cmt
o OrganizationService.res : cp ../../src/services/OrganizationService.res
o APIUtils.cmi : cp ../bs/src/api/APIUtils.cmi
o APIUtils.cmj : cp ../bs/src/api/APIUtils.cmj
o APIUtils.cmt : cp ../bs/src/api/APIUtils.cmt
o APIUtils.res : cp ../../src/api/APIUtils.res
o AdminEntry.cmi : cp ../bs/src/entryPoints/AdminEntry.cmi
o AdminEntry.cmj : cp ../bs/src/entryPoints/AdminEntry.cmj
o AdminEntry.cmt : cp ../bs/src/entryPoints/AdminEntry.cmt
o AdminEntry.res : cp ../../src/entryPoints/AdminEntry.res
o Input.cmi : cp ../bs/src/components/Input.cmi
o Input.cmj : cp ../bs/src/components/Input.cmj
o Input.cmt : cp ../bs/src/components/Input.cmt
o Input.res : cp ../../src/components/Input.res
o Button.cmi : cp ../bs/src/components/Button.cmi
o Button.cmj : cp ../bs/src/components/Button.cmj
o Button.cmt : cp ../bs/src/components/Button.cmt
o Button.res : cp ../../src/components/Button.res
o ProtectedRoute.cmi : cp ../bs/src/components/ProtectedRoute.cmi
o ProtectedRoute.cmj : cp ../bs/src/components/ProtectedRoute.cmj
o ProtectedRoute.cmt : cp ../bs/src/components/ProtectedRoute.cmt
o ProtectedRoute.res : cp ../../src/components/ProtectedRoute.res
o PageLoaderWrapper.cmi : cp ../bs/src/components/PageLoaderWrapper.cmi
o PageLoaderWrapper.cmj : cp ../bs/src/components/PageLoaderWrapper.cmj
o PageLoaderWrapper.cmt : cp ../bs/src/components/PageLoaderWrapper.cmt
o PageLoaderWrapper.res : cp ../../src/components/PageLoaderWrapper.res
o Settings.cmi : cp ../bs/src/screens/Settings.cmi
o Settings.cmj : cp ../bs/src/screens/Settings.cmj
o Settings.cmt : cp ../bs/src/screens/Settings.cmt
o Settings.res : cp ../../src/screens/Settings.res
o UserList.cmi : cp ../bs/src/screens/UserList.cmi
o UserList.cmj : cp ../bs/src/screens/UserList.cmj
o UserList.cmt : cp ../bs/src/screens/UserList.cmt
o UserList.res : cp ../../src/screens/UserList.res
o Analytics.cmi : cp ../bs/src/screens/Analytics.cmi
o Analytics.cmj : cp ../bs/src/screens/Analytics.cmj
o Analytics.cmt : cp ../bs/src/screens/Analytics.cmt
o Analytics.res : cp ../../src/screens/Analytics.res
o MerchantList.cmi : cp ../bs/src/screens/MerchantList.cmi
o MerchantList.cmj : cp ../bs/src/screens/MerchantList.cmj
o MerchantList.cmt : cp ../bs/src/screens/MerchantList.cmt
o MerchantList.res : cp ../../src/screens/MerchantList.res
o OrganizationList.cmi : cp ../bs/src/screens/OrganizationList.cmi
o OrganizationList.cmj : cp ../bs/src/screens/OrganizationList.cmj
o OrganizationList.cmt : cp ../bs/src/screens/OrganizationList.cmt
o OrganizationList.res : cp ../../src/screens/OrganizationList.res
o PaymentManagement.cmi : cp ../bs/src/screens/PaymentManagement.cmi
o PaymentManagement.cmj : cp ../bs/src/screens/PaymentManagement.cmj
o PaymentManagement.cmt : cp ../bs/src/screens/PaymentManagement.cmt
o PaymentManagement.res : cp ../../src/screens/PaymentManagement.res
o ConnectorManagement.cmi : cp ../bs/src/screens/ConnectorManagement.cmi
o ConnectorManagement.cmj : cp ../bs/src/screens/ConnectorManagement.cmj
o ConnectorManagement.cmt : cp ../bs/src/screens/ConnectorManagement.cmt
o ConnectorManagement.res : cp ../../src/screens/ConnectorManagement.res
o PaymentMethodManagement.cmi : cp ../bs/src/screens/PaymentMethodManagement.cmi
o PaymentMethodManagement.cmj : cp ../bs/src/screens/PaymentMethodManagement.cmj
o PaymentMethodManagement.cmt : cp ../bs/src/screens/PaymentMethodManagement.cmt
o PaymentMethodManagement.res : cp ../../src/screens/PaymentMethodManagement.res
o I18n.cmi : cp ../bs/src/utils/I18n.cmi
o I18n.cmj : cp ../bs/src/utils/I18n.cmj
o I18n.cmt : cp ../bs/src/utils/I18n.cmt
o I18n.res : cp ../../src/utils/I18n.res
o GlobalVars.cmi : cp ../bs/src/utils/GlobalVars.cmi
o GlobalVars.cmj : cp ../bs/src/utils/GlobalVars.cmj
o GlobalVars.cmt : cp ../bs/src/utils/GlobalVars.cmt
o GlobalVars.res : cp ../../src/utils/GlobalVars.res
o LogicUtils.cmi : cp ../bs/src/utils/LogicUtils.cmi
o LogicUtils.cmj : cp ../bs/src/utils/LogicUtils.cmj
o LogicUtils.cmt : cp ../bs/src/utils/LogicUtils.cmt
o LogicUtils.res : cp ../../src/utils/LogicUtils.res
o LocalStorage.cmi : cp ../bs/src/utils/LocalStorage.cmi
o LocalStorage.cmj : cp ../bs/src/utils/LocalStorage.cmj
o LocalStorage.cmt : cp ../bs/src/utils/LocalStorage.cmt
o LocalStorage.res : cp ../../src/utils/LocalStorage.res
o en.cmi : cp ../bs/src/locales/en.cmi
o en.cmj : cp ../bs/src/locales/en.cmj
o en.cmt : cp ../bs/src/locales/en.cmt
o en.res : cp ../../src/locales/en.res
o zh.cmi : cp ../bs/src/locales/zh.cmi
o zh.cmj : cp ../bs/src/locales/zh.cmj
o zh.cmt : cp ../bs/src/locales/zh.cmt
o zh.res : cp ../../src/locales/zh.res
o AuthContext.cmi : cp ../bs/src/context/AuthContext.cmi
o AuthContext.cmj : cp ../bs/src/context/AuthContext.cmj
o AuthContext.cmt : cp ../bs/src/context/AuthContext.cmt
o AuthContext.res : cp ../../src/context/AuthContext.res
o AuthTypes.cmi : cp ../bs/src/types/AuthTypes.cmi
o AuthTypes.cmj : cp ../bs/src/types/AuthTypes.cmj
o AuthTypes.cmt : cp ../bs/src/types/AuthTypes.cmt
o AuthTypes.res : cp ../../src/types/AuthTypes.res
o PaymentTypes.cmi : cp ../bs/src/types/PaymentTypes.cmi
o PaymentTypes.cmj : cp ../bs/src/types/PaymentTypes.cmj
o PaymentTypes.cmt : cp ../bs/src/types/PaymentTypes.cmt
o PaymentTypes.res : cp ../../src/types/PaymentTypes.res
o MerchantTypes.cmi : cp ../bs/src/types/MerchantTypes.cmi
o MerchantTypes.cmj : cp ../bs/src/types/MerchantTypes.cmj
o MerchantTypes.cmt : cp ../bs/src/types/MerchantTypes.cmt
o MerchantTypes.res : cp ../../src/types/MerchantTypes.res
o OrganizationTypes.cmi : cp ../bs/src/types/OrganizationTypes.cmi
o OrganizationTypes.cmj : cp ../bs/src/types/OrganizationTypes.cmj
o OrganizationTypes.cmt : cp ../bs/src/types/OrganizationTypes.cmt
o OrganizationTypes.res : cp ../../src/types/OrganizationTypes.res
build install.stamp : touch MerchantService.cmi MerchantService.cmj OrganizationService.cmi OrganizationService.cmj APIUtils.cmi APIUtils.cmj AdminEntry.cmi AdminEntry.cmj Input.cmi Input.cmj Button.cmi Button.cmj ProtectedRoute.cmi ProtectedRoute.cmj PageLoaderWrapper.cmi PageLoaderWrapper.cmj Settings.cmi Settings.cmj UserList.cmi UserList.cmj Analytics.cmi Analytics.cmj MerchantList.cmi MerchantList.cmj OrganizationList.cmi OrganizationList.cmj PaymentManagement.cmi PaymentManagement.cmj ConnectorManagement.cmi ConnectorManagement.cmj PaymentMethodManagement.cmi PaymentMethodManagement.cmj I18n.cmi I18n.cmj GlobalVars.cmi GlobalVars.cmj LogicUtils.cmi LogicUtils.cmj LocalStorage.cmi LocalStorage.cmj en.cmi en.cmj zh.cmi zh.cmj AuthContext.cmi AuthContext.cmj AuthTypes.cmi AuthTypes.cmj PaymentTypes.cmi PaymentTypes.cmj MerchantTypes.cmi MerchantTypes.cmj OrganizationTypes.cmi OrganizationTypes.cmj 
