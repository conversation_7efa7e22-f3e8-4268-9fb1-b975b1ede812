rescript = 1
g_finger := /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/@rescript/react/lib/ocaml/install.stamp
g_finger := /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/rescript-webapi/lib/ocaml/install.stamp
g_finger := /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/bs-fetch/lib/ocaml/install.stamp
g_finger := /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/@rescript/core/lib/ocaml/install.stamp
rule astj
  command = /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/rescript/darwinarm64/bsc.exe  -bs-v 11.1.4 -bs-jsx 4 -bs-jsx-mode classic -uncurried -bs-super-errors -open RescriptCore -absname -bs-ast -o $out $i
o src/services/MerchantService.ast : astj ../../src/services/MerchantService.res
rule deps
  command = /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/rescript/darwinarm64/bsb_helper.exe -hash 094ffafe302e6bf39e42f1e91b3ca7ca $in
  restat = 1
o src/services/MerchantService.d : deps src/services/MerchantService.ast
rule mij
  command = /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/rescript/darwinarm64/bsc.exe -I src/types -I src/context -I src/locales -I src/utils -I src/screens/Organizations -I src/screens -I src/styles -I src/components -I src/entryPoints -I src/api -I src/services -I src -I /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/@rescript/react/lib/ocaml -I /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/rescript-webapi/lib/ocaml -I /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/bs-fetch/lib/ocaml -I /Users/<USER>/Documents/rust_code/hyperswich/admin_frontend/node_modules/@rescript/core/lib/ocaml -bs-super-errors -open RescriptCore  -uncurried -bs-package-name pay-project-admin -bs-package-output esmodule:$in_d:.res.js -bs-v $g_finger $i
  dyndep = 1
  restat = 1
o src/services/MerchantService.cmj src/services/MerchantService.cmi ../../src/services/MerchantService.res.js : mij src/services/MerchantService.ast
o src/services/OrganizationService.ast : astj ../../src/services/OrganizationService.res
o src/services/OrganizationService.d : deps src/services/OrganizationService.ast
o src/services/OrganizationService.cmj src/services/OrganizationService.cmi ../../src/services/OrganizationService.res.js : mij src/services/OrganizationService.ast
o src/api/APIUtils.ast : astj ../../src/api/APIUtils.res
o src/api/APIUtils.d : deps src/api/APIUtils.ast
o src/api/APIUtils.cmj src/api/APIUtils.cmi ../../src/api/APIUtils.res.js : mij src/api/APIUtils.ast
o src/entryPoints/AdminEntry.ast : astj ../../src/entryPoints/AdminEntry.res
o src/entryPoints/AdminEntry.d : deps src/entryPoints/AdminEntry.ast
o src/entryPoints/AdminEntry.cmj src/entryPoints/AdminEntry.cmi ../../src/entryPoints/AdminEntry.res.js : mij src/entryPoints/AdminEntry.ast
o src/components/Input.ast : astj ../../src/components/Input.res
o src/components/Input.d : deps src/components/Input.ast
o src/components/Input.cmj src/components/Input.cmi ../../src/components/Input.res.js : mij src/components/Input.ast
o src/components/Button.ast : astj ../../src/components/Button.res
o src/components/Button.d : deps src/components/Button.ast
o src/components/Button.cmj src/components/Button.cmi ../../src/components/Button.res.js : mij src/components/Button.ast
o src/components/ProtectedRoute.ast : astj ../../src/components/ProtectedRoute.res
o src/components/ProtectedRoute.d : deps src/components/ProtectedRoute.ast
o src/components/ProtectedRoute.cmj src/components/ProtectedRoute.cmi ../../src/components/ProtectedRoute.res.js : mij src/components/ProtectedRoute.ast
o src/components/PageLoaderWrapper.ast : astj ../../src/components/PageLoaderWrapper.res
o src/components/PageLoaderWrapper.d : deps src/components/PageLoaderWrapper.ast
o src/components/PageLoaderWrapper.cmj src/components/PageLoaderWrapper.cmi ../../src/components/PageLoaderWrapper.res.js : mij src/components/PageLoaderWrapper.ast
o src/screens/Settings.ast : astj ../../src/screens/Settings.res
o src/screens/Settings.d : deps src/screens/Settings.ast
o src/screens/Settings.cmj src/screens/Settings.cmi ../../src/screens/Settings.res.js : mij src/screens/Settings.ast
o src/screens/UserList.ast : astj ../../src/screens/UserList.res
o src/screens/UserList.d : deps src/screens/UserList.ast
o src/screens/UserList.cmj src/screens/UserList.cmi ../../src/screens/UserList.res.js : mij src/screens/UserList.ast
o src/screens/Analytics.ast : astj ../../src/screens/Analytics.res
o src/screens/Analytics.d : deps src/screens/Analytics.ast
o src/screens/Analytics.cmj src/screens/Analytics.cmi ../../src/screens/Analytics.res.js : mij src/screens/Analytics.ast
o src/screens/MerchantList.ast : astj ../../src/screens/MerchantList.res
o src/screens/MerchantList.d : deps src/screens/MerchantList.ast
o src/screens/MerchantList.cmj src/screens/MerchantList.cmi ../../src/screens/MerchantList.res.js : mij src/screens/MerchantList.ast
o src/screens/OrganizationList.ast : astj ../../src/screens/OrganizationList.res
o src/screens/OrganizationList.d : deps src/screens/OrganizationList.ast
o src/screens/OrganizationList.cmj src/screens/OrganizationList.cmi ../../src/screens/OrganizationList.res.js : mij src/screens/OrganizationList.ast
o src/screens/PaymentManagement.ast : astj ../../src/screens/PaymentManagement.res
o src/screens/PaymentManagement.d : deps src/screens/PaymentManagement.ast
o src/screens/PaymentManagement.cmj src/screens/PaymentManagement.cmi ../../src/screens/PaymentManagement.res.js : mij src/screens/PaymentManagement.ast
o src/screens/ConnectorManagement.ast : astj ../../src/screens/ConnectorManagement.res
o src/screens/ConnectorManagement.d : deps src/screens/ConnectorManagement.ast
o src/screens/ConnectorManagement.cmj src/screens/ConnectorManagement.cmi ../../src/screens/ConnectorManagement.res.js : mij src/screens/ConnectorManagement.ast
o src/screens/PaymentMethodManagement.ast : astj ../../src/screens/PaymentMethodManagement.res
o src/screens/PaymentMethodManagement.d : deps src/screens/PaymentMethodManagement.ast
o src/screens/PaymentMethodManagement.cmj src/screens/PaymentMethodManagement.cmi ../../src/screens/PaymentMethodManagement.res.js : mij src/screens/PaymentMethodManagement.ast
o src/utils/I18n.ast : astj ../../src/utils/I18n.res
o src/utils/I18n.d : deps src/utils/I18n.ast
o src/utils/I18n.cmj src/utils/I18n.cmi ../../src/utils/I18n.res.js : mij src/utils/I18n.ast
o src/utils/GlobalVars.ast : astj ../../src/utils/GlobalVars.res
o src/utils/GlobalVars.d : deps src/utils/GlobalVars.ast
o src/utils/GlobalVars.cmj src/utils/GlobalVars.cmi ../../src/utils/GlobalVars.res.js : mij src/utils/GlobalVars.ast
o src/utils/LogicUtils.ast : astj ../../src/utils/LogicUtils.res
o src/utils/LogicUtils.d : deps src/utils/LogicUtils.ast
o src/utils/LogicUtils.cmj src/utils/LogicUtils.cmi ../../src/utils/LogicUtils.res.js : mij src/utils/LogicUtils.ast
o src/utils/LocalStorage.ast : astj ../../src/utils/LocalStorage.res
o src/utils/LocalStorage.d : deps src/utils/LocalStorage.ast
o src/utils/LocalStorage.cmj src/utils/LocalStorage.cmi ../../src/utils/LocalStorage.res.js : mij src/utils/LocalStorage.ast
o src/locales/en.ast : astj ../../src/locales/en.res
o src/locales/en.d : deps src/locales/en.ast
o src/locales/en.cmj src/locales/en.cmi ../../src/locales/en.res.js : mij src/locales/en.ast
o src/locales/zh.ast : astj ../../src/locales/zh.res
o src/locales/zh.d : deps src/locales/zh.ast
o src/locales/zh.cmj src/locales/zh.cmi ../../src/locales/zh.res.js : mij src/locales/zh.ast
o src/context/AuthContext.ast : astj ../../src/context/AuthContext.res
o src/context/AuthContext.d : deps src/context/AuthContext.ast
o src/context/AuthContext.cmj src/context/AuthContext.cmi ../../src/context/AuthContext.res.js : mij src/context/AuthContext.ast
o src/types/AuthTypes.ast : astj ../../src/types/AuthTypes.res
o src/types/AuthTypes.d : deps src/types/AuthTypes.ast
o src/types/AuthTypes.cmj src/types/AuthTypes.cmi ../../src/types/AuthTypes.res.js : mij src/types/AuthTypes.ast
o src/types/PaymentTypes.ast : astj ../../src/types/PaymentTypes.res
o src/types/PaymentTypes.d : deps src/types/PaymentTypes.ast
o src/types/PaymentTypes.cmj src/types/PaymentTypes.cmi ../../src/types/PaymentTypes.res.js : mij src/types/PaymentTypes.ast
o src/types/MerchantTypes.ast : astj ../../src/types/MerchantTypes.res
o src/types/MerchantTypes.d : deps src/types/MerchantTypes.ast
o src/types/MerchantTypes.cmj src/types/MerchantTypes.cmi ../../src/types/MerchantTypes.res.js : mij src/types/MerchantTypes.ast
o src/types/OrganizationTypes.ast : astj ../../src/types/OrganizationTypes.res
o src/types/OrganizationTypes.d : deps src/types/OrganizationTypes.ast
o src/types/OrganizationTypes.cmj src/types/OrganizationTypes.cmi ../../src/types/OrganizationTypes.res.js : mij src/types/OrganizationTypes.ast
