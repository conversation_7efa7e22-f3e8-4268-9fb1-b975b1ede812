# Pay Project Admin Frontend Environment Configuration

# Application Environment
APP_ENV=development
NODE_ENV=development

# API Configuration
API_BASE_URL=http://localhost:8080
API_TIMEOUT=30000

# Authentication
JWT_SECRET=your-jwt-secret-key
SESSION_TIMEOUT=3600

# Analytics
MIXPANEL_TOKEN=your-mixpanel-token
GOOGLE_ANALYTICS_ID=your-ga-id

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_AUDIT_LOG=true
ENABLE_DEBUG_MODE=false

# UI Configuration
DEFAULT_LANGUAGE=en
DEFAULT_TIMEZONE=UTC
DEFAULT_CURRENCY=USD

# Development
HOT_RELOAD=true
SOURCE_MAPS=true
WEBPACK_DEV_SERVER_PORT=9001

# Production
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CDN_URL=

# Logging
LOG_LEVEL=info
ENABLE_ERROR_REPORTING=true

# Security
ENABLE_CSRF_PROTECTION=true
ENABLE_RATE_LIMITING=true
CORS_ORIGINS=http://localhost:3000,http://localhost:9001

# Database (if needed for admin operations)
DATABASE_URL=postgresql://username:password@localhost:5432/pay_project_admin

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,csv,xlsx

# Monitoring
SENTRY_DSN=your-sentry-dsn
HEALTH_CHECK_INTERVAL=30000

# Third-party Services
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
PAYPAL_CLIENT_ID=your-paypal-client-id

# Backup and Recovery
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Compliance
GDPR_ENABLED=true
DATA_RETENTION_DAYS=2555
AUDIT_LOG_RETENTION_DAYS=2555
