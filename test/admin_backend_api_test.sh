#!/bin/bash

# 总后台管理系统 API 测试脚本
# 测试基于v1版本API的组织列表接口

set -e

# 配置
BASE_URL="http://localhost:8080"
API_VERSION="v1"
ADMIN_API_KEY="your_admin_api_key_here"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -s -f "${BASE_URL}/health" > /dev/null 2>&1; then
        log_info "服务运行正常"
        return 0
    else
        log_error "服务未运行或无法访问: ${BASE_URL}"
        return 1
    fi
}

# 测试组织列表接口
test_organization_list() {
    log_info "测试组织列表接口..."
    
    local url="${BASE_URL}/organization"
    local response_file="/tmp/org_list_response.json"
    
    # 测试不带参数的请求
    log_info "测试基本请求: GET ${url}"
    
    if curl -s -H "Authorization: Bearer ${ADMIN_API_KEY}" \
           -H "Content-Type: application/json" \
           "${url}" \
           -o "${response_file}" \
           -w "%{http_code}" | grep -q "200"; then
        log_info "✓ 基本请求成功"
        
        # 检查响应格式
        if jq -e '.data' "${response_file}" > /dev/null 2>&1; then
            log_info "✓ 响应格式正确，包含data字段"
        else
            log_warn "⚠ 响应格式可能不正确"
        fi
        
        if jq -e '.total_count' "${response_file}" > /dev/null 2>&1; then
            log_info "✓ 响应包含total_count字段"
        else
            log_warn "⚠ 响应缺少total_count字段"
        fi
        
        if jq -e '.count' "${response_file}" > /dev/null 2>&1; then
            log_info "✓ 响应包含count字段"
        else
            log_warn "⚠ 响应缺少count字段"
        fi
        
    else
        log_error "✗ 基本请求失败"
        return 1
    fi
    
    # 测试带分页参数的请求
    log_info "测试分页请求: GET ${url}?limit=10&offset=0"
    
    if curl -s -H "Authorization: Bearer ${ADMIN_API_KEY}" \
           -H "Content-Type: application/json" \
           "${url}?limit=10&offset=0" \
           -w "%{http_code}" | grep -q "200"; then
        log_info "✓ 分页请求成功"
    else
        log_error "✗ 分页请求失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f "${response_file}"
    
    return 0
}

# 测试商户列表接口
test_merchant_list() {
    log_info "测试商户列表接口..."

    local url="${BASE_URL}/accounts/list"
    local response_file="/tmp/merchant_list_response.json"

    log_info "测试商户列表请求: GET ${url}"

    if curl -s -H "Authorization: Bearer ${ADMIN_API_KEY}" \
           -H "Content-Type: application/json" \
           "${url}" \
           -o "${response_file}" \
           -w "%{http_code}" | grep -q "200"; then
        log_info "✓ 商户列表请求成功"

        # 检查响应格式（v1版本返回数组）
        if jq -e 'type == "array"' "${response_file}" > /dev/null 2>&1; then
            log_info "✓ 响应格式正确（v1版本数组格式）"
        else
            log_warn "⚠ 响应格式可能不正确"
        fi
    else
        log_warn "⚠ 商户列表请求失败（可能需要先创建商户）"
    fi

    # 清理临时文件
    rm -f "${response_file}"

    return 0
}

# 测试权限验证
test_authentication() {
    log_info "测试权限验证..."
    
    local url="${BASE_URL}/organization"
    
    # 测试无认证头的请求
    log_info "测试无认证请求..."
    
    local status_code=$(curl -s -w "%{http_code}" "${url}" -o /dev/null)
    
    if [ "${status_code}" = "401" ] || [ "${status_code}" = "403" ]; then
        log_info "✓ 权限验证正常，未认证请求被拒绝"
    else
        log_warn "⚠ 权限验证可能有问题，状态码: ${status_code}"
    fi
    
    # 测试错误的API密钥
    log_info "测试错误API密钥..."
    
    local status_code=$(curl -s -H "Authorization: Bearer invalid_key" \
                            -w "%{http_code}" "${url}" -o /dev/null)
    
    if [ "${status_code}" = "401" ] || [ "${status_code}" = "403" ]; then
        log_info "✓ API密钥验证正常，错误密钥被拒绝"
    else
        log_warn "⚠ API密钥验证可能有问题，状态码: ${status_code}"
    fi
    
    return 0
}

# 测试连接器接口
test_connector_api() {
    log_info "测试连接器管理接口..."

    # 需要一个测试商户ID
    local test_merchant_id="test_merchant_123"
    local url="${BASE_URL}/account/${test_merchant_id}/connectors"

    log_info "测试连接器列表请求: GET ${url}"

    local status_code=$(curl -s -H "Authorization: Bearer ${ADMIN_API_KEY}" \
                            -H "Content-Type: application/json" \
                            -w "%{http_code}" "${url}" -o /dev/null)

    if [ "${status_code}" = "200" ]; then
        log_info "✓ 连接器列表请求成功"
    elif [ "${status_code}" = "404" ]; then
        log_warn "⚠ 商户不存在或无连接器配置"
    else
        log_warn "⚠ 连接器列表请求失败，状态码: ${status_code}"
    fi

    return 0
}

# 测试支付接口（管理员权限）
test_admin_payment_api() {
    log_info "测试管理员支付接口..."

    # 测试平台级支付统计（模拟接口）
    local url="${BASE_URL}/admin/payments/platform/stats"

    log_info "测试平台支付统计: GET ${url}"

    local status_code=$(curl -s -H "Authorization: Bearer ${ADMIN_API_KEY}" \
                            -H "Content-Type: application/json" \
                            -w "%{http_code}" "${url}" -o /dev/null)

    if [ "${status_code}" = "200" ]; then
        log_info "✓ 平台支付统计请求成功"
    elif [ "${status_code}" = "404" ]; then
        log_warn "⚠ 管理员支付接口未实现（使用模拟数据）"
    else
        log_warn "⚠ 平台支付统计请求失败，状态码: ${status_code}"
    fi

    return 0
}

# 主测试函数
main() {
    log_info "开始总后台管理系统API测试"
    log_info "测试目标: ${BASE_URL}"
    log_info "API版本: ${API_VERSION}"
    echo
    
    # 检查必要工具
    if ! command -v curl &> /dev/null; then
        log_error "curl 命令未找到，请安装curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 命令未找到，请安装jq"
        exit 1
    fi
    
    # 检查API密钥
    if [ "${ADMIN_API_KEY}" = "your_admin_api_key_here" ]; then
        log_warn "请设置正确的ADMIN_API_KEY"
        log_info "使用环境变量: export ADMIN_API_KEY=your_actual_key"
    fi
    
    # 执行测试
    local test_passed=0
    local test_total=0
    
    # 服务状态检查
    ((test_total++))
    if check_service; then
        ((test_passed++))
    fi
    echo
    
    # 权限验证测试
    ((test_total++))
    if test_authentication; then
        ((test_passed++))
    fi
    echo
    
    # 组织列表接口测试
    ((test_total++))
    if test_organization_list; then
        ((test_passed++))
    fi
    echo
    
    # 商户列表接口测试
    ((test_total++))
    if test_merchant_list; then
        ((test_passed++))
    fi
    echo

    # 连接器接口测试
    ((test_total++))
    if test_connector_api; then
        ((test_passed++))
    fi
    echo

    # 管理员支付接口测试
    ((test_total++))
    if test_admin_payment_api; then
        ((test_passed++))
    fi
    echo
    
    # 测试结果汇总
    log_info "测试完成"
    log_info "通过: ${test_passed}/${test_total}"
    
    if [ ${test_passed} -eq ${test_total} ]; then
        log_info "🎉 所有测试通过！"
        exit 0
    else
        log_warn "⚠ 部分测试失败，请检查日志"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    "org")
        test_organization_list
        ;;
    "merchant")
        test_merchant_list
        ;;
    "connector")
        test_connector_api
        ;;
    "payment")
        test_admin_payment_api
        ;;
    "auth")
        test_authentication
        ;;
    "health")
        check_service
        ;;
    *)
        main
        ;;
esac
