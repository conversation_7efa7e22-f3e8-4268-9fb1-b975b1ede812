# Hyperswitch Control Center 国际化技术实现文档

## 技术架构

### 核心组件
- **翻译引擎**: 基于 ReScript 的自定义国际化系统
- **语言包**: JSON 格式的翻译文件
- **状态管理**: React Context + Hooks
- **类型安全**: ReScript 类型系统保证

### 文件结构
```
src/
├── locales/
│   ├── zh.res              # 中文翻译 (主要)
│   └── en.res              # 英文翻译 (默认)
├── utils/
│   └── I18nUtils.res       # 国际化工具函数
├── components/             # 通用组件
└── screens/               # 页面组件
```

## 实现细节

### 1. 翻译函数系统

#### 核心Hook
```rescript
// I18nUtils.res
let useTranslation = () => {
  let (currentLang, setCurrentLang) = React.useState(_ => "zh")
  let (t, setT) = React.useState(_ => (key: string) => key)
  
  // 返回 (翻译函数, 当前语言, 设置语言函数, 可用语言列表)
  (t, currentLang, setCurrentLang, ["zh", "en"])
}
```

#### 使用模式
```rescript
// 在组件中使用
open I18nUtils
let (t, _, _, _) = useTranslation()

// 翻译文本
<div> {t("welcome_message")->React.string} </div>
<Button text={t("save")} />
```

### 2. 语言包结构

#### 中文语言包 (zh.res)
```rescript
let dict = Dict.make()

// 基础UI
dict->set("save", "保存")
dict->set("cancel", "取消")
dict->set("edit", "编辑")

// 业务术语
dict->set("payment_settings", "支付设置")
dict->set("fraud_risk_management", "欺诈与风险管理")

// 导出
dict
```

### 3. 组件国际化模式

#### 标准实现
```rescript
@react.component
let make = (~title, ~onSave) => {
  open I18nUtils
  let (t, _, _, _) = useTranslation()
  
  <div>
    <h1> {t("page_title")->React.string} </h1>
    <Button text={t("save")} onClick=onSave />
  </div>
}
```

#### 表单组件
```rescript
<FormRenderer.FieldRenderer
  field={FormRenderer.makeFieldInfo(
    ~label=t("field_label"),
    ~placeholder=t("field_placeholder"),
    ~description=t("field_description"),
    ~name="field_name",
  )}
/>
```

## 已实现的翻译模块

### 1. 核心UI组件 (50+ 键)
- 按钮文本: save, cancel, edit, delete, create
- 状态显示: loading, success, error, pending
- 通用操作: search, filter, sort, export

### 2. 导航系统 (30+ 键)
- 侧边栏菜单: home, connectors, analytics, developers
- 面包屑导航: dashboard, settings, management
- 用户菜单: profile, logout, switch_merchant

### 3. 连接器管理 (40+ 键)
- FRM相关: fraud_risk_management, connect_configure_processors
- 3DS认证: three_ds_authenticators, authentication_processor
- 支付处理器: payment_processors, connector_status

### 4. 对账系统 (30+ 键)
- 激活功能: activate_reconciliation, setup_reconciliation
- 报告管理: download_reports, reconciliation_reports
- 状态消息: processing, completed, failed

### 5. 支付设置 (35+ 键)
- 基础配置: payment_settings, webhook_settings
- 高级功能: auto_retries, click_to_pay
- 钱包集成: collect_billing_details, collect_shipping_details

### 6. 用户管理 (20+ 键)
- 用户操作: invite_users, manage_roles, team_management
- 权限控制: access_control, role_permissions

## 编译和构建

### ReScript 编译
```bash
# 编译 ReScript 代码
npx rescript

# 监听模式
npx rescript build -w
```

### 开发服务器
```bash
# 启动开发服务器
npm start

# 访问地址
http://localhost:9000/
```

### 构建生产版本
```bash
# 构建生产版本
npm run build
```

## 质量保证

### 类型安全
- ReScript 类型系统确保翻译键的正确性
- 编译时检查防止运行时错误
- 自动补全和IDE支持

### 编译验证
- ✅ 无类型错误
- ✅ 无语法错误  
- ✅ 热更新正常
- ✅ 构建成功

### 测试覆盖
- 页面渲染测试
- 语言切换功能
- 翻译键完整性检查

## 性能优化

### 当前实现
- 静态翻译文件加载
- 编译时优化
- 最小化运行时开销

### 未来优化
- 按需加载语言包
- 翻译缓存机制
- 代码分割优化

## 维护和扩展

### 添加新翻译
1. 在 `src/locales/zh.res` 中添加翻译键值对
2. 在组件中使用 `t("new_key")`
3. 重新编译验证

### 添加新语言
1. 创建新的语言文件 (如 `fr.res`)
2. 复制现有翻译结构
3. 更新语言选择器

### 翻译键命名规范
- 使用下划线分隔: `payment_settings`
- 按功能模块分组: `frm_`, `recon_`, `user_`
- 保持简洁明确: `save`, `cancel`, `edit`

## 故障排除

### 常见问题
1. **翻译不显示**: 检查翻译键是否存在
2. **编译错误**: 验证 ReScript 语法
3. **热更新失败**: 重启开发服务器

### 调试技巧
- 使用浏览器开发者工具检查翻译
- 查看编译输出确认无错误
- 验证翻译键的拼写和格式

## 部署注意事项

### 生产环境
- 确保所有翻译键都有对应的翻译
- 验证构建产物包含正确的语言文件
- 测试语言切换功能

### 监控和维护
- 监控缺失的翻译键
- 定期更新翻译内容
- 收集用户反馈优化翻译质量
