# 前端修改日志

## 2025-06-17 修改记录

### 1. 侧边栏菜单修改
- **文件**: `hyperswitch-control-center/src/entryPoints/SidebarValues.res`
- **修改**: 注释掉 dashboard/recon 路由
- **变更**: 第895行，将 `recon->reconAndSettlement(isReconEnabled, checkUserEntity, userHasResourceAccess),` 注释掉

### 2. Recon 页面联系信息修改
- **文件**: `hyperswitch-control-center/src/screens/Recon/Recon.res`
- **修改内容**:
  - 将 Slack 链接改为 Telegram 链接
  - 联系方式从 "slack" 改为 "Telegram: @yeeu"
  - 链接地址从 `https://hyperswitch-io.slack.com/?redir=%2Fssb%2Fredirect` 改为 `https://t.me/yeeu`

### 3. 邮件地址配置修改
修改了以下配置文件中的 `recon_recipient_email` 从 `<EMAIL>` 改为 `<EMAIL>`:

- `hyperswitch/config/development.toml` (第362行)
- `hyperswitch/config/docker_compose.toml` (第1045行)
- `hyperswitch/config/config.example.toml` (第430行)
- `hyperswitch/config/deployments/env_specific.toml` (第71行)
- `hyperswitch/loadtest/config/development.toml` (第674行)

### 4. 开发者文档链接修改
将所有开发者文档链接从 `https://hyperswitch.io/docs` 或 `https://docs.hyperswitch.io/...` 改为 `http://************:3000/`:

#### 修改的文件:
- `hyperswitch-control-center/src/screens/Home/HomeUtils.res` (第309行)
- `hyperswitch-control-center/src/screens/DefaultHome/DefaultHomeUtils.res` (第95行, 第86行)
- `hyperswitch-control-center/src/Vault/VaultScreens/VaultHomeUtils.res` (第37行, 第45行)
- `hyperswitch-control-center/src/screens/AlternatePaymentMethods/AltPaymentMethodsUtils.res` (第28行, 第39行)

### 5. 修改影响范围
- **前端**: 侧边栏菜单、Recon页面、Home页面、Vault页面
- **后端**: 邮件配置文件
- **功能**: 
  - Recon 路由在侧边栏中被隐藏
  - 联系方式从 Slack 改为 Telegram
  - 邮件接收地址更新
  - 开发者文档链接指向新地址

### 6. 测试建议
1. 验证侧边栏中 Recon 菜单项已被隐藏
2. 测试 Recon 页面的联系信息显示正确
3. 验证发送邮件功能使用新的邮件地址
4. 测试开发者文档链接跳转到正确地址
5. 确认 Telegram 链接可以正常打开

### 7. 注意事项
- 如果需要重新启用 Recon 路由，取消注释 `SidebarValues.res` 中的相关行
- 邮件配置修改需要重启后端服务才能生效
- 前端修改需要重新编译才能看到效果
