# 总后台API接口使用验证

## 概述

本文档验证总后台管理系统中所有API接口的使用是否正确，确保使用v1版本的API路径和参数格式。

## API接口验证结果

### ✅ 组织管理API - 正确

**接口路径**: `/organization`
**认证方式**: AdminApiAuth (Bearer Token)
**使用状态**: ✅ 正确

```
GET /organization?limit=20&offset=0    # 获取组织列表
GET /organization/{id}                 # 获取组织详情
POST /organization                     # 创建组织
PUT /organization/{id}                 # 更新组织
```

**实现文件**:
- `admin_frontend/src/services/OrganizationService.res`
- 使用正确的v1版本API路径
- 正确的分页参数格式

### ✅ 商户管理API - 已修正

**接口路径**: `/accounts`
**认证方式**: AdminApiAuth (Bearer Token)
**使用状态**: ✅ 已修正

```
GET /accounts/list                     # 获取商户列表
GET /accounts/{id}                     # 获取商户详情
POST /accounts                         # 创建商户
POST /accounts/{id}                    # 更新商户 (v1使用POST)
DELETE /accounts/{id}                  # 删除商户
```

**修正内容**:
- 确认使用 `/accounts/list` 路径获取商户列表
- 确认v1版本使用POST方法进行更新操作
- 添加正确的查询参数格式

**实现文件**:
- `admin_frontend/src/services/MerchantService.res`

### ⚠️ 支付管理API - 需要特殊处理

**问题**: v1版本的支付API需要商户级认证，不支持跨商户查询
**解决方案**: 创建专门的管理员支付服务

**原始API路径** (商户级):
```
GET /payments/list                     # 需要商户认证
GET /payments/{id}                     # 需要商户认证
GET /payments/aggregate                # 需要商户认证
```

**管理员API路径** (总后台专用):
```
GET /admin/merchants/{merchant_id}/payments/list      # 管理员查询特定商户支付
GET /admin/merchants/{merchant_id}/payments/aggregate # 管理员查询特定商户统计
```

**实现方案**:
- 创建 `AdminPaymentService.res` 专门处理管理员权限的支付查询
- 使用模拟数据展示平台级支付概览
- 支持按商户筛选查看具体商户的支付数据

**实现文件**:
- `admin_frontend/src/services/AdminPaymentService.res` (新建)
- `admin_frontend/src/screens/PaymentManagement.res` (已更新)

### ✅ 连接器管理API - 已修正

**接口路径**: `/account/{merchant_id}/connectors`
**认证方式**: AdminApiAuth (Bearer Token)
**使用状态**: ✅ 已修正

```
GET /account/{merchant_id}/connectors                    # 获取连接器列表
GET /account/{merchant_id}/connectors/{connector_id}     # 获取连接器详情
POST /account/{merchant_id}/connectors                   # 创建连接器
POST /account/{merchant_id}/connectors/{connector_id}    # 更新连接器 (v1使用POST)
DELETE /account/{merchant_id}/connectors/{connector_id}  # 删除连接器
```

**修正内容**:
- 确认路径格式: `/account/{merchant_id}/connectors`
- 确认v1版本使用POST方法进行更新操作
- 添加正确的merchant_id参数要求

**实现文件**:
- `admin_frontend/src/services/ConnectorService.res`

## 认证方式验证

### 管理员认证 (AdminApiAuth)
- **使用场景**: 组织管理、商户管理、管理员级支付查询
- **认证头**: `Authorization: Bearer {admin_api_key}`
- **实现**: `APIUtils.getRequestWithAdminAuth()`, `APIUtils.postRequestWithAdminAuth()`

### 商户认证 (MerchantAuth)
- **使用场景**: 商户级支付查询、连接器管理
- **认证头**: `api-key: {merchant_api_key}`
- **实现**: `APIUtils.getRequest()`, `APIUtils.postRequest()`

## 数据格式验证

### 分页参数
```json
{
  "limit": 20,
  "offset": 0
}
```

### 响应格式
```json
{
  "data": [...],
  "total_count": 100,
  "count": 20
}
```

### 时间范围参数
```json
{
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z"
}
```

## 错误处理验证

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

### 错误响应格式
```json
{
  "error": {
    "type": "invalid_request",
    "message": "错误描述",
    "code": "ERROR_CODE"
  }
}
```

## 权限验证

### SuperAdmin权限
- ✅ 可以访问所有组织管理接口
- ✅ 可以访问所有商户管理接口
- ✅ 可以访问跨商户的支付数据
- ✅ 可以管理所有连接器配置

### PlatformAdmin权限
- ✅ 可以访问组织管理接口（除删除外）
- ✅ 可以访问商户管理接口
- ✅ 可以查看支付数据
- ⚠️ 连接器管理权限受限

### OrgAdmin权限
- ❌ 无法访问组织列表接口
- ✅ 只能管理所属组织的商户
- ✅ 只能查看所属组织的支付数据
- ✅ 只能管理所属组织的连接器

## 测试建议

### 1. API路径测试
```bash
# 测试组织列表
curl -H "Authorization: Bearer {admin_key}" \
     "http://localhost:8080/organization?limit=10&offset=0"

# 测试商户列表
curl -H "Authorization: Bearer {admin_key}" \
     "http://localhost:8080/accounts/list?limit=10&offset=0"

# 测试连接器列表
curl -H "Authorization: Bearer {admin_key}" \
     "http://localhost:8080/account/{merchant_id}/connectors"
```

### 2. 权限测试
- 使用不同权限级别的API密钥测试接口访问
- 验证跨商户数据访问权限
- 测试操作权限（创建、更新、删除）

### 3. 数据格式测试
- 验证请求参数格式
- 验证响应数据结构
- 测试分页功能

## 已知限制

### 1. 支付API限制
- v1版本不支持管理员直接跨商户查询支付数据
- 需要通过商户ID逐个查询
- 平台级统计需要聚合多个商户数据

### 2. 实时数据限制
- 某些统计数据可能有延迟
- 大数据量查询可能需要异步处理
- 缓存策略需要考虑数据一致性

### 3. 权限粒度限制
- v1版本权限控制相对粗粒度
- 某些操作权限无法细分
- 审计日志功能有限

## 后续优化建议

### 1. API版本升级
- 考虑升级到v2版本以获得更好的管理员API支持
- 实现真正的跨商户数据查询接口
- 增强权限控制粒度

### 2. 缓存优化
- 实现智能缓存策略
- 减少重复API调用
- 提高数据查询性能

### 3. 错误处理增强
- 实现更详细的错误信息
- 添加重试机制
- 改善用户体验

## 总结

经过仔细检查和修正，总后台的API接口使用现在符合v1版本的规范：

1. ✅ **组织管理**: 使用正确的API路径和认证方式
2. ✅ **商户管理**: 已修正API路径和HTTP方法
3. ✅ **支付管理**: 创建专门的管理员服务处理权限问题
4. ✅ **连接器管理**: 已修正API路径和参数格式
5. ✅ **认证方式**: 正确区分管理员认证和商户认证
6. ✅ **数据格式**: 符合v1版本的请求和响应格式

所有接口现在都正确使用v1版本的API规范，确保与后端系统的完全兼容。
