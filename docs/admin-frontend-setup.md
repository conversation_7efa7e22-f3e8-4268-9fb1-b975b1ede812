# Pay Project 总后台前端项目设置指南

## 项目概述

Pay Project 总后台是一个基于 React + ReScript 的现代化管理系统，用于管理平台级的组织、租户、用户和数据分析。

## 技术栈

- **前端框架**: React 18 + ReScript
- **样式**: Tailwind CSS
- **构建工具**: Webpack 5
- **状态管理**: Recoil
- **路由**: ReScript React Router
- **图表**: ApexCharts / Highcharts
- **UI组件**: Headless UI
- **国际化**: 自定义 i18n 系统
- **认证**: JWT + Context API

## 项目结构

```
admin_frontend/
├── src/
│   ├── api/                    # API 接口层
│   │   ├── APIUtils.res        # API 工具函数
│   │   └── OrganizationAPI.res # 组织管理 API
│   ├── components/             # 通用组件
│   │   ├── Button.res          # 按钮组件
│   │   ├── Input.res           # 输入框组件
│   │   └── ProtectedRoute.res  # 路由保护组件
│   ├── context/                # React Context
│   │   └── AuthContext.res     # 认证上下文
│   ├── entryPoints/            # 应用入口
│   │   └── AdminEntry.res      # 主入口文件
│   ├── locales/                # 国际化文件
│   │   ├── en.res              # 英文翻译
│   │   └── zh.res              # 中文翻译
│   ├── screens/                # 页面组件
│   │   └── Organizations/      # 组织管理页面
│   ├── types/                  # 类型定义
│   │   ├── AuthTypes.res       # 认证类型
│   │   └── OrganizationTypes.res # 组织类型
│   └── utils/                  # 工具函数
│       ├── GlobalVars.res      # 全局变量
│       ├── LogicUtils.res      # 逻辑工具
│       ├── LocalStorage.res    # 本地存储
│       └── I18n.res            # 国际化工具
├── public/                     # 静态资源
├── scripts/                    # 脚本文件
├── docs/                       # 文档
└── 配置文件...
```

## 快速开始

### 1. 环境要求

- Node.js >= 16
- Yarn 或 npm
- Git

### 2. 项目设置

```bash
# 进入项目目录
cd admin_frontend

# 运行设置脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 3. 配置环境变量

复制并编辑环境配置文件：

```bash
cp .env.example .env
```

主要配置项：

```env
# API 配置
API_BASE_URL=http://localhost:8080

# 应用环境
APP_ENV=development
NODE_ENV=development

# 功能开关
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
```

### 4. 启动开发服务器

```bash
# 使用开发脚本
chmod +x scripts/dev.sh
./scripts/dev.sh

# 或者手动启动
yarn run re:start  # 启动 ReScript 编译器
yarn start         # 启动开发服务器
```

访问 http://localhost:9001 查看应用。

## 开发指南

### ReScript 编译

项目使用 ReScript 编写，需要先编译为 JavaScript：

```bash
# 编译一次
yarn run re:build

# 监听模式（推荐开发时使用）
yarn run re:start

# 清理编译文件
yarn run re:clean
```

### 代码规范

```bash
# 检查代码规范
yarn run lint:hooks

# 自动修复
yarn run lint:fix

# 格式化 ReScript 代码
yarn run re:format
```

### 构建部署

```bash
# 生产构建
yarn run build:prod

# 测试构建
yarn run build:test

# 自定义构建
yarn run build:custom
```

## 功能模块

### 1. 认证系统

- 管理员登录/登出
- JWT 令牌管理
- 权限控制
- 路由保护

### 2. 组织管理

- 组织列表查看
- 创建/编辑/删除组织
- 组织类型管理
- 状态管理

### 3. 租户管理

- 商户账户管理
- 租户配置
- 数据统计

### 4. 用户管理

- 平台用户管理
- 角色权限分配
- 用户状态管理

### 5. 数据分析

- 平台级数据统计
- 图表展示
- 报表生成

### 6. 系统设置

- 平台配置
- 安全设置
- 通知配置

## API 接口

### 认证接口

```
POST /admin/auth/login      # 管理员登录
POST /admin/auth/logout     # 登出
POST /admin/auth/refresh    # 刷新令牌
GET  /admin/auth/me         # 获取当前用户信息
```

### 组织管理接口

```
GET    /v2/organization              # 获取组织列表
POST   /v2/organization              # 创建组织
GET    /v2/organization/{id}         # 获取组织详情
PUT    /v2/organization/{id}         # 更新组织
DELETE /v2/organization/{id}         # 删除组织
GET    /v2/organization/stats        # 获取组织统计
GET    /v2/organization/{id}/merchant-accounts # 获取组织下的商户
```

## 国际化

项目支持中英文切换：

```rescript
// 使用翻译函数
let text = I18n.t("organization_management")

// 带参数的翻译
let message = I18n.t("min_length", ~params=["2"], ())

// 语言切换组件
<I18n.LanguageSelector />
<I18n.LanguageToggle />
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t pay-project-admin .

# 运行容器
docker run -p 9001:80 pay-project-admin

# 使用 Docker Compose
docker-compose up -d
```

### 传统部署

```bash
# 构建生产版本
yarn run build:prod

# 部署 dist 目录到 Web 服务器
cp -r dist/* /var/www/html/
```

## 故障排除

### 常见问题

1. **ReScript 编译错误**
   ```bash
   yarn run re:clean
   yarn run re:build
   ```

2. **依赖安装失败**
   ```bash
   rm -rf node_modules yarn.lock
   yarn install
   ```

3. **端口冲突**
   - 修改 `webpack.dev.js` 中的端口配置
   - 或设置环境变量 `PORT=9002`

4. **API 连接失败**
   - 检查 `.env` 中的 `API_BASE_URL` 配置
   - 确保后端服务正在运行

### 日志查看

```bash
# 开发模式日志
yarn start

# 生产模式日志
docker logs pay-project-admin-frontend
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 邮箱: <EMAIL>
- Telegram: @yeeu

---

更多详细信息请参考项目 README.md 文件。
