# Hyperswitch Control Center 国际化验证指南

## 快速验证步骤

### 1. 环境准备
确保开发服务器正在运行：
```bash
cd hyperswitch-control-center
npm start
```
访问地址: `http://localhost:9000/`

### 2. 语言切换验证
1. 打开应用程序
2. 查找语言切换器（通常在右上角）
3. 切换到中文 (zh)
4. 验证界面语言是否正确切换

### 3. 页面级验证清单

#### ✅ 已完成页面
- [ ] **首页 Dashboard** (`/dashboard/home`)
  - 侧边栏菜单全部中文
  - 主要内容区域中文
  - 搜索功能中文

- [ ] **Fraud & Risk Management** (`/dashboard/fraud-risk-management`)
  - 页面标题: "欺诈与风险管理"
  - 描述文本: "连接和配置处理器以筛选交易并减轻欺诈"
  - 按钮: "带我去连接器"
  - 提示信息: "尚未配置连接器。尝试连接一个连接器。"

- [ ] **Reconciliation** (`/dashboard/recon`)
  - 页面标题: "激活对账" 或 "对账"
  - 描述文本: "立即升级以简化您的对账和结算操作"
  - 按钮: "发送邮件", "前往对账标签页"
  - 联系信息: "给我们发邮件！", "或通过以下方式联系我们"

- [ ] **Payment Settings** (`/dashboard/payment-settings`)
  - 页面标题: "支付设置"
  - 配置项: "配置文件名称", "商户ID", "支付响应哈希密钥"
  - 功能选项: "从钱包收集账单详细信息", "自动重试", "点击支付"
  - 按钮: "更新", "取消"

- [ ] **API Keys** (`/dashboard/developer-api-keys`)
  - 页面标题: "API密钥"
  - 表格标题: "API密钥名称", "密钥ID", "创建时间"
  - 按钮: "创建API密钥"

- [ ] **Configure PMTs** (`/dashboard/configure-pmts`)
  - 页面标题: "在结账时配置PMTs"
  - 描述: "控制您的支付方式在结账时的可见性"
  - 表格标题: "支付方式"

- [ ] **Users Management** (`/dashboard/users`)
  - 页面标题: "团队管理"
  - 标签页: "用户", "角色"
  - 按钮: "邀请新用户", "创建自定义角色"

### 4. 组件级验证

#### 通用组件
- [ ] **搜索框**: 占位符文本为中文
- [ ] **日期选择器**: 月份和星期显示中文
- [ ] **表格**: 列标题、排序、分页等为中文
- [ ] **按钮**: 所有操作按钮显示中文文本
- [ ] **表单**: 标签、占位符、验证消息为中文

#### 状态消息
- [ ] **加载状态**: "加载中..."
- [ ] **成功消息**: "操作成功", "保存成功"
- [ ] **错误消息**: "操作失败", "网络错误"
- [ ] **空状态**: "暂无数据", "未找到结果"

### 5. 交互功能验证

#### 表单操作
- [ ] 填写表单时标签显示中文
- [ ] 验证错误消息显示中文
- [ ] 提交成功/失败消息显示中文

#### 导航操作
- [ ] 面包屑导航显示中文路径
- [ ] 侧边栏菜单项点击正常
- [ ] 页面跳转后标题正确更新

#### 数据操作
- [ ] 表格排序功能正常
- [ ] 搜索功能返回正确结果
- [ ] 分页控件显示中文

### 6. 浏览器兼容性验证

#### 测试浏览器
- [ ] Chrome (推荐)
- [ ] Firefox
- [ ] Safari
- [ ] Edge

#### 响应式设计
- [ ] 桌面端 (1920x1080)
- [ ] 平板端 (768x1024)
- [ ] 移动端 (375x667)

### 7. 常见问题检查

#### 显示问题
- [ ] 文本是否有截断或重叠
- [ ] 中文字符是否正确显示
- [ ] 布局是否因文本长度变化而错乱

#### 功能问题
- [ ] 语言切换是否立即生效
- [ ] 页面刷新后语言设置是否保持
- [ ] 所有交互功能是否正常工作

### 8. 性能验证

#### 加载性能
- [ ] 首次加载时间 < 3秒
- [ ] 语言切换响应时间 < 1秒
- [ ] 页面跳转流畅无卡顿

#### 内存使用
- [ ] 长时间使用无内存泄漏
- [ ] 语言切换不影响性能

### 9. 错误场景测试

#### 网络问题
- [ ] 网络断开时的错误提示
- [ ] API调用失败的错误消息
- [ ] 超时处理的用户提示

#### 数据异常
- [ ] 空数据状态的显示
- [ ] 异常数据的处理
- [ ] 权限不足的提示

### 10. 验证报告模板

#### 发现的问题
```
页面: [页面名称]
问题: [具体问题描述]
期望: [期望的正确行为]
实际: [实际观察到的行为]
严重程度: [高/中/低]
```

#### 验证结果汇总
- 总验证项目: ___
- 通过项目: ___
- 失败项目: ___
- 整体完成度: ___%

### 11. 验证完成标准

#### 必须满足的条件
- [ ] 所有主要页面标题正确显示中文
- [ ] 核心功能按钮和链接为中文
- [ ] 表单标签和提示信息为中文
- [ ] 错误和成功消息为中文
- [ ] 导航菜单完全中文化

#### 可接受的例外
- 技术术语可保持英文 (如 API, JSON, HTTP)
- 第三方组件的部分文本
- 开发调试信息

### 12. 后续改进建议

#### 短期优化
- 修复发现的翻译遗漏
- 优化文本长度和布局
- 完善错误处理

#### 长期规划
- 添加更多语言支持
- 实现动态语言包加载
- 建立翻译管理流程

---

**验证负责人**: ___________  
**验证日期**: ___________  
**验证版本**: ___________  
**验证结果**: [ ] 通过 [ ] 需要修复
